-- 创建插件表
CREATE TABLE IF NOT EXISTS public.plugins (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  version TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size BIGINT,
  mime_type TEXT,
  public_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 设置RLS策略
ALTER TABLE public.plugins ENABLE ROW LEVEL SECURITY;

-- 创建公开访问策略（允许所有用户查看插件）
CREATE POLICY "允许所有用户查看插件" ON public.plugins
  FOR SELECT USING (true);

-- 创建插入策略（允许认证用户上传插件）
CREATE POLICY "允许认证用户上传插件" ON public.plugins
  FOR INSERT TO authenticated USING (true);

-- 创建插入策略（允许匿名用户上传插件，临时用于测试）
CREATE POLICY "允许匿名用户上传插件" ON public.plugins
  FOR INSERT TO anon USING (true);

-- 创建更新策略（允许认证用户更新自己上传的插件）
CREATE POLICY "允许认证用户更新插件" ON public.plugins
  FOR UPDATE TO authenticated USING (true);

-- 创建删除策略（允许认证用户删除自己上传的插件）
CREATE POLICY "允许认证用户删除插件" ON public.plugins
  FOR DELETE TO authenticated USING (true);

-- 添加注释
COMMENT ON TABLE public.plugins IS '存储浏览器插件信息';
COMMENT ON COLUMN public.plugins.id IS '插件ID';
COMMENT ON COLUMN public.plugins.name IS '插件名称';
COMMENT ON COLUMN public.plugins.description IS '插件描述';
COMMENT ON COLUMN public.plugins.version IS '插件版本';
COMMENT ON COLUMN public.plugins.file_path IS '文件存储路径';
COMMENT ON COLUMN public.plugins.file_size IS '文件大小（字节）';
COMMENT ON COLUMN public.plugins.mime_type IS '文件MIME类型';
COMMENT ON COLUMN public.plugins.public_url IS '文件公共访问URL';
COMMENT ON COLUMN public.plugins.created_at IS '创建时间';
COMMENT ON COLUMN public.plugins.updated_at IS '更新时间'; 
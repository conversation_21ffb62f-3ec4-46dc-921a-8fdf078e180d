const fetch = require('node-fetch');

async function testApiFormat() {
  console.log('🔍 测试API响应格式...\n');
  
  try {
    // 测试本地API
    console.log('📍 测试本地API (localhost:3001):');
    const localResponse = await fetch('http://localhost:3001/api/plugin/list');
    console.log('状态码:', localResponse.status);
    console.log('Content-Type:', localResponse.headers.get('content-type'));
    console.log('响应头:', Object.fromEntries(localResponse.headers.entries()));
    
    const localData = await localResponse.text();
    console.log('响应数据长度:', localData.length);
    console.log('响应数据前200字符:', localData.substring(0, 200));
    console.log('是否为JSON格式:', isJsonString(localData));
    console.log('');
    
    // 测试生产API
    console.log('📍 测试生产API (***************):');
    const prodResponse = await fetch('http://***************/api/plugin/list');
    console.log('状态码:', prodResponse.status);
    console.log('Content-Type:', prodResponse.headers.get('content-type'));
    console.log('响应头:', Object.fromEntries(prodResponse.headers.entries()));
    
    const prodData = await prodResponse.text();
    console.log('响应数据长度:', prodData.length);
    console.log('响应数据前200字符:', prodData.substring(0, 200));
    console.log('是否为JSON格式:', isJsonString(prodData));
    console.log('');
    
    // 比较两个响应
    console.log('🔍 比较结果:');
    console.log('本地API Content-Type:', localResponse.headers.get('content-type'));
    console.log('生产API Content-Type:', prodResponse.headers.get('content-type'));
    console.log('本地API是JSON:', isJsonString(localData));
    console.log('生产API是JSON:', isJsonString(prodData));
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

function isJsonString(str) {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

testApiFormat(); 
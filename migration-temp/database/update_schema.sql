-- =================================================================
-- 数据库结构更新
-- 本文件记录了所有对数据库结构的修改，请按顺序执行。
-- =================================================================

-- 版本 1.1.0 更新
-- 添加自定义XPath字段
ALTER TABLE public.accounts
ADD COLUMN IF NOT EXISTS username_xpath TEXT,
ADD COLUMN IF NOT EXISTS password_xpath TEXT,
ADD COLUMN IF NOT EXISTS captcha_xpath TEXT,
ADD COLUMN IF NOT EXISTS login_button_xpath TEXT,
ADD COLUMN IF NOT EXISTS captcha_input_xpath TEXT;

-- 添加字段注释
COMMENT ON COLUMN public.accounts.username_xpath IS '自定义用户名输入框的XPath选择器';
COMMENT ON COLUMN public.accounts.password_xpath IS '自定义密码输入框的XPath选择器';
COMMENT ON COLUMN public.accounts.captcha_xpath IS '验证码图片的XPath选择器';
COMMENT ON COLUMN public.accounts.login_button_xpath IS '登录按钮的XPath选择器';
COMMENT ON COLUMN public.accounts.captcha_input_xpath IS '验证码输入框的XPath选择器';

-- 更新现有记录的注释（如果需要）
UPDATE environments
SET name = '默认环境'
WHERE name IS NULL;

ALTER TABLE environments
ALTER COLUMN name SET NOT NULL;

-- =================================================================
-- 版本 1.2.0 更新 - 登录方式插件选择功能（简化版）
-- =================================================================

-- 添加登录方式相关字段
ALTER TABLE public.accounts
ADD COLUMN IF NOT EXISTS login_type VARCHAR(20) DEFAULT 'custom',
ADD COLUMN IF NOT EXISTS automa_workflow_id VARCHAR(100),
ADD COLUMN IF NOT EXISTS plugin_config JSONB;

-- 添加字段注释
COMMENT ON COLUMN public.accounts.login_type IS '登录方式类型: custom(自定义插件), automa(Automa插件)';
COMMENT ON COLUMN public.accounts.automa_workflow_id IS 'Automa插件的工作流程ID';
COMMENT ON COLUMN public.accounts.plugin_config IS '插件扩展配置，JSON格式';

-- 添加登录类型约束
ALTER TABLE public.accounts
ADD CONSTRAINT check_login_type CHECK (login_type IN ('custom', 'automa'));

-- 为现有账号设置默认登录类型
UPDATE public.accounts 
SET login_type = 'custom' 
WHERE login_type IS NULL;

-- 添加索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_accounts_login_type ON public.accounts(login_type);
CREATE INDEX IF NOT EXISTS idx_accounts_automa_workflow_id ON public.accounts(automa_workflow_id) WHERE automa_workflow_id IS NOT NULL;

-- ================================================================= 
/**
 * OCR代理服务
 * 解决HTTPS页面调用HTTP OCR服务的Mixed Content问题
 * 在本地3002端口启动代理，转发到生产服务器
 */

const http = require('http');
const https = require('https');
const url = require('url');

// 配置
const PROXY_PORT = 3002;
const TARGET_SERVER = 'http://192.168.202.230';

// 创建代理服务器
const proxyServer = http.createServer((req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Origin');
  
  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // 只处理OCR相关请求
  if (!req.url.includes('/api/ocr')) {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
    return;
  }
  
  console.log(`代理请求: ${req.method} ${req.url}`);
  
  // 构建目标URL
  const targetUrl = `${TARGET_SERVER}${req.url}`;
  const parsedUrl = url.parse(targetUrl);
  
  // 收集请求体数据
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  
  req.on('end', () => {
    // 构建代理请求选项
    const options = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || 80,
      path: parsedUrl.path,
      method: req.method,
      headers: {
        ...req.headers,
        host: parsedUrl.host
      }
    };
    
    // 发送代理请求
    const proxyReq = http.request(options, (proxyRes) => {
      // 复制响应头
      res.writeHead(proxyRes.statusCode, proxyRes.headers);
      
      // 转发响应数据
      proxyRes.pipe(res);
    });
    
    proxyReq.on('error', (err) => {
      console.error('代理请求失败:', err.message);
      res.writeHead(500, { 'Content-Type': 'text/plain' });
      res.end(`代理请求失败: ${err.message}`);
    });
    
    // 发送请求体
    if (body) {
      proxyReq.write(body);
    }
    
    proxyReq.end();
  });
});

// 启动代理服务器
proxyServer.listen(PROXY_PORT, () => {
  console.log(`OCR代理服务器已启动`);
  console.log(`监听端口: ${PROXY_PORT}`);
  console.log(`目标服务器: ${TARGET_SERVER}`);
  console.log(`代理URL: http://localhost:${PROXY_PORT}/api/ocr/recognize`);
});

// 错误处理
proxyServer.on('error', (err) => {
  console.error('代理服务器错误:', err.message);
});

process.on('SIGINT', () => {
  console.log('\n正在关闭OCR代理服务器...');
  proxyServer.close(() => {
    console.log('OCR代理服务器已关闭');
    process.exit(0);
  });
}); 
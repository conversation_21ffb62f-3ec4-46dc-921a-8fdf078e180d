{"extVersion": "1.29.10", "name": "登录后执行一体化商城开发", "icon": "riGlobalLine", "table": [], "version": "3.0.0", "drawflow": {"nodes": [{"id": "main-trigger", "type": "BlockBasic", "initialized": false, "position": {"x": 96, "y": 36}, "data": {"disableBlock": false, "description": "🚀 主流程触发器", "type": "manual", "interval": 60, "delay": 2, "date": "", "time": "00:00", "url": "", "shortcut": "", "activeInInput": false, "isUrlRegex": false, "days": [], "contextMenuName": "", "contextTypes": [], "parameters": [], "preferParamsInTab": false}, "label": "trigger"}, {"id": "init-variables", "type": "BlockBasic", "initialized": false, "position": {"x": 400, "y": 36}, "data": {"disableBlock": false, "description": "🔧 初始化登录配置", "timeout": 5000, "context": "background", "code": "// === 初始化登录配置变量 ===\ntry {\n  console.log('🔧 === 初始化组合工作流 ===');\n  \n  // 设置登录配置参数供包使用\n  const loginConfig = {\n    loginUrl: \"https://test-aliuser.lotsmall.cn/usercenter/login\",\n    credentials: {\n      username: \"18766668891\",\n      password: \"123456asd.\"\n    },\n    selectorMode: \"xpath\",\n    selectors: {\n      username: \"//input[@name='username']\",\n      password: \"//input[@type='password']\",\n      captcha: \"//input[@placeholder='验证码']\",\n      captchaImage: \"//img[@class='code-img']\",\n      loginButton: \"//button[contains(text(),'登录')]\"\n    },\n    postLoginAction: {\n      type: \"click_app_card\",\n      targetText: \"智游宝数智平台\"\n    },\n    options: {\n      maxRetries: 3,\n      showProgress: true,\n      skipPostAction: false,\n      ocrConfig: {\n        url: \"https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize\",\n        headers: {\n          Cookie: \"tr_de_id=ZHYtSEhOUlZRWkVXSFZI\"\n        }\n      }\n    }\n  };\n  \n  // 设置变量供登录包使用\n  automaSetVariable('loginConfig', loginConfig);\n  automaSetVariable('workflowStartTime', Date.now());\n  automaSetVariable('mainWorkflowStatus', 'initialized');\n  \n  console.log('✅ 登录配置初始化完成');\n  \n} catch (error) {\n  console.error('❌ 初始化失败:', error);\n  automaSetVariable('initError', error.message);\n} finally {\n  automaNextBlock();\n}", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "execute-login-package", "type": "BlockBasic", "initialized": false, "position": {"x": 700, "y": 36}, "data": {"disableBlock": false, "description": "📦 执行登录包", "packageName": "通用登录工作流", "packageId": "universal-login-xpath-enhanced"}, "label": "execute-package"}, {"id": "check-login-result", "type": "BlockBasic", "initialized": false, "position": {"x": 1000, "y": 36}, "data": {"disableBlock": false, "description": "✅ 检查登录结果", "conditions": [{"type": "javascript", "value": "return automaRefData('variables', 'loginSuccess') === true;"}], "fallback": true}, "label": "conditions"}, {"id": "delay-before-next", "type": "BlockDelay", "initialized": false, "position": {"x": 1300, "y": 36}, "data": {"disableBlock": false, "description": "⏱️ 等待登录完成", "time": "3000"}, "label": "delay"}, {"id": "start-localdev-process", "type": "BlockBasic", "initialized": false, "position": {"x": 1600, "y": 36}, "data": {"disableBlock": false, "description": "🏪 开始一体化商城本地开发流程", "timeout": 5000, "context": "background", "code": "// === 开始一体化商城本地开发流程 ===\ntry {\n  console.log('🏪 === 开始一体化商城本地开发流程 ===');\n  \n  // 标记本地开发流程开始\n  automaSetVariable('localdevProcessStarted', true);\n  \n  console.log('✅ 一体化商城本地开发流程已启动');\n  \n} catch (error) {\n  console.error('❌ 本地开发流程启动失败:', error);\n  automaSetVariable('localdevError', error.message);\n} finally {\n  automaNextBlock();\n}", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "open-production-mall", "type": "BlockBasic", "initialized": false, "position": {"x": 1900, "y": 36}, "data": {"active": true, "customUserAgent": false, "description": "🌐 打开生产商城", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "https://test-aliwww.lotsmall.cn/manage/workspace", "userAgent": "", "waitTabLoaded": false}, "label": "new-tab"}, {"id": "extract-session-data", "type": "BlockBasic", "initialized": false, "position": {"x": 2200, "y": 36}, "data": {"disableBlock": false, "description": "🔑 提取并上传会话数据", "timeout": 20000, "context": "website", "code": "/*\n * Automa Script: Precisely Extract and Send Data to Supabase\n */\n\n// --- 配置 (不变) ---\nconst SET_URL = 'https://okkgchwzppghiyfgmrlj.supabase.co/functions/v1/set-data';\nconst API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ra2djaHd6cHBnaGl5ZmdtcmxqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTQwNTAsImV4cCI6MjA2NTIzMDA1MH0.LMhY-7H3ySiXEZt2cjLXhhicL4idx0A6xurvxynqJf8';\n\n// --- 核心逻辑 (精准提取版) ---\n\n// 1. 定义一个函数，用于从cookie字符串中精确查找某个key的值\nfunction getCookieValue(name) {\n  const cookieString = \"; \" + document.cookie;\n  const parts = cookieString.split(`; ${name}=`);\n  if (parts.length === 2) {\n    return parts.pop().split(';').shift();\n  }\n}\n\n// 2. 精确提取需要的数据\nconst sessionId = getCookieValue('SESSIONID');\nconst token = localStorage.getItem('xjsc_2018_token');\nconst userId = localStorage.getItem('xjsc_2018_userId');\n\n// 3. 将提取到的三项数据打包成一个新对象\nconst payloadValue = {\n  sessionId: sessionId,\n  token: token,\n  userId: userId,\n};\n\n// 4. 发送打包后的精准数据到Supabase\nconsole.log('Automa: Sending precise data to Supabase...', payloadValue);\nfetch(SET_URL, {\n    method: 'POST',\n    headers: {\n        'Content-Type': 'application/json',\n        'apikey': API_KEY,\n        'Authorization': 'Bearer ' + API_KEY\n    },\n    body: JSON.stringify({\n        key: 'my-session',\n        value: payloadValue\n    })\n})\n.then(response => response.json())\n.then(data => {\n    console.log('Automa: Successfully sent precise data.', data);\n    // 设置变量表示数据提取完成\n    automaSetVariable('sessionDataExtracted', true);\n    automaNextBlock();\n})\n.catch(error => {\n    console.error('Automa: Error sending precise data.', error);\n    automaSetVariable('sessionDataExtracted', false);\n    automaNextBlock();\n});", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "open-local-dev", "type": "BlockBasic", "initialized": false, "position": {"x": 2500, "y": 36}, "data": {"active": true, "customUserAgent": false, "description": "💻 打开本地开发环境", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "http://127.0.0.1:8080/manage/login", "userAgent": "", "waitTabLoaded": true}, "label": "new-tab"}, {"id": "apply-session-data", "type": "BlockBasic", "initialized": false, "position": {"x": 2800, "y": 36}, "data": {"disableBlock": false, "description": "🔄 下载并应用会话数据", "timeout": 20000, "context": "website", "code": "/*\n * Automa Script: Get Precise Data from Supabase and Apply\n */\n\n// --- 配置 (不变) ---\nconst GET_URL = 'https://okkgchwzppghiyfgmrlj.supabase.co/functions/v1/get-data?key=my-session';\nconst API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ra2djaHd6cHBnaGl5ZmdtcmxqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTQwNTAsImV4cCI6MjA2NTIzMDA1MH0.LMhY-7H3ySiXEZt2cjLXhhicL4idx0A6xurvxynqJf8';\n\n// --- 核心逻辑 (精准注入版) ---\nasync function getAndApplyPreciseData() {\n    console.log('Automa: Attempting to retrieve precise data from Supabase...');\n    try {\n        const response = await fetch(GET_URL, {\n            method: 'GET',\n            headers: { 'apikey': API_KEY, 'Authorization': 'Bearer ' + API_KEY }\n        });\n\n        if (!response.ok) throw new Error(`Network response was not ok: ${response.status}`);\n        \n        const data = await response.json();\n        // 关键改动：将取回的字符串值，解析成一个真正的JavaScript对象\n        const preciseData = JSON.parse(data.value); \n        \n        if (preciseData) {\n            console.log('Automa: Successfully retrieved precise data.', preciseData);\n            console.log('Automa: SESSIONID cookie：'+preciseData.sessionId);\n\n            // 1. 精确设置Cookie: SESSIONID\n            if (preciseData.sessionId) {\n                // 设置cookie时最好指定path=/，确保全站可用\n                document.cookie = `SESSIONID=${preciseData.sessionId}; path=/`;\n                console.log('Automa: SESSIONID cookie applied.');\n            }\n\n            // 2. 精确设置Local Storage\n            if (preciseData.token) {\n                localStorage.setItem('xjsc_2018_token', preciseData.token);\n                console.log('Automa: xjsc_2018_token applied.');\n            }\n            if (preciseData.userId) {\n                localStorage.setItem('xjsc_2018_userId', preciseData.userId);\n                console.log('Automa: xjsc_2018_userId applied.');\n            }\n\n            // 3. 刷新页面以应用所有更改\n            console.log('Automa: Reloading page...');\n            // location.reload();\n            \n            automaSetVariable('sessionDataApplied', true);\n\n        } else {\n            console.error('Automa: No data found in Supabase response.');\n            alert('未能从云端获取到有效的数据。');\n            automaSetVariable('sessionDataApplied', false);\n        }\n    } catch (error) {\n        console.error('Automa: Error getting or applying data.', error);\n        alert('从云端获取数据失败，请按F12打开浏览器控制台查看详细错误信息。');\n        automaSetVariable('sessionDataApplied', false);\n    }\n}\n\ngetAndApplyPreciseData().then(() => {\n    automaNextBlock();\n});", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "delay-for-app", "type": "BlockDelay", "initialized": false, "position": {"x": 3100, "y": 36}, "data": {"disableBlock": false, "description": "⏱️ 等待应用加载", "time": "2000"}, "label": "delay"}, {"id": "open-local-workspace", "type": "BlockBasic", "initialized": false, "position": {"x": 3400, "y": 36}, "data": {"active": true, "customUserAgent": false, "description": "🏠 打开本地工作台", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "http://127.0.0.1:8080/manage/workspace", "userAgent": "", "waitTabLoaded": true}, "label": "new-tab"}, {"id": "completion-summary", "type": "BlockBasic", "initialized": false, "position": {"x": 3700, "y": 36}, "data": {"disableBlock": false, "description": "📋 完成总结", "timeout": 3000, "context": "background", "code": "// === 组合工作流完成总结 ===\ntry {\n  const startTime = automaRefData('variables', 'workflowStartTime') || Date.now();\n  const endTime = Date.now();\n  const duration = endTime - startTime;\n  \n  const loginSuccess = automaRefData('variables', 'loginSuccess');\n  const sessionDataExtracted = automaRefData('variables', 'sessionDataExtracted');\n  const sessionDataApplied = automaRefData('variables', 'sessionDataApplied');\n  const localdevStarted = automaRefData('variables', 'localdevProcessStarted');\n  \n  console.log('🎉 === 组合工作流执行完成 ===');\n  console.log('📊 执行摘要:');\n  console.log('  - 登录状态:', loginSuccess ? '✅ 成功' : '❌ 失败');\n  console.log('  - 会话数据提取:', sessionDataExtracted ? '✅ 完成' : '❌ 失败');\n  console.log('  - 会话数据应用:', sessionDataApplied ? '✅ 完成' : '❌ 失败');\n  console.log('  - 本地开发启动:', localdevStarted ? '✅ 完成' : '❌ 失败');\n  console.log('  - 总耗时:', Math.round(duration / 1000), '秒');\n  \n  // 设置最终状态\n  automaSetVariable('workflowCompleted', true);\n  automaSetVariable('workflowDuration', duration);\n  \n  const successCount = [loginSuccess, sessionDataExtracted, sessionDataApplied, localdevStarted].filter(Boolean).length;\n  console.log(`✅ 成功完成 ${successCount}/4 个主要步骤`);\n  \n  if (successCount === 4) {\n    console.log('🎉 所有步骤执行成功！一体化商城本地开发环境已准备就绪！');\n  } else {\n    console.log(`⚠️ 有 ${4 - successCount} 个步骤执行失败，请检查日志`);\n  }\n  \n} catch (error) {\n  console.error('❌ 完成总结失败:', error);\n} finally {\n  console.log('🏁 组合工作流结束');\n  automaNextBlock();\n}", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}], "edges": [{"id": "trigger-to-init", "type": "custom", "source": "main-trigger", "target": "init-variables", "sourceHandle": "main-trigger-output-1", "targetHandle": "init-variables-input-1", "markerEnd": "arrowclosed"}, {"id": "init-to-login-package", "type": "custom", "source": "init-variables", "target": "execute-login-package", "sourceHandle": "init-variables-output-1", "targetHandle": "execute-login-package-input-1", "markerEnd": "arrowclosed"}, {"id": "login-package-to-check", "type": "custom", "source": "execute-login-package", "target": "check-login-result", "sourceHandle": "execute-login-package-output-1", "targetHandle": "check-login-result-input-1", "markerEnd": "arrowclosed"}, {"id": "check-to-delay", "type": "custom", "source": "check-login-result", "target": "delay-before-next", "sourceHandle": "check-login-result-output-1", "targetHandle": "delay-before-next-input-1", "markerEnd": "arrowclosed"}, {"id": "delay-to-localdev", "type": "custom", "source": "delay-before-next", "target": "start-localdev-process", "sourceHandle": "delay-before-next-output-1", "targetHandle": "start-localdev-process-input-1", "markerEnd": "arrowclosed"}, {"id": "localdev-to-production", "type": "custom", "source": "start-localdev-process", "target": "open-production-mall", "sourceHandle": "start-localdev-process-output-1", "targetHandle": "open-production-mall-input-1", "markerEnd": "arrowclosed"}, {"id": "production-to-extract", "type": "custom", "source": "open-production-mall", "target": "extract-session-data", "sourceHandle": "open-production-mall-output-1", "targetHandle": "extract-session-data-input-1", "markerEnd": "arrowclosed"}, {"id": "extract-to-local", "type": "custom", "source": "extract-session-data", "target": "open-local-dev", "sourceHandle": "extract-session-data-output-1", "targetHandle": "open-local-dev-input-1", "markerEnd": "arrowclosed"}, {"id": "local-to-apply", "type": "custom", "source": "open-local-dev", "target": "apply-session-data", "sourceHandle": "open-local-dev-output-1", "targetHandle": "apply-session-data-input-1", "markerEnd": "arrowclosed"}, {"id": "apply-to-delay2", "type": "custom", "source": "apply-session-data", "target": "delay-for-app", "sourceHandle": "apply-session-data-output-1", "targetHandle": "delay-for-app-input-1", "markerEnd": "arrowclosed"}, {"id": "delay2-to-workspace", "type": "custom", "source": "delay-for-app", "target": "open-local-workspace", "sourceHandle": "delay-for-app-output-1", "targetHandle": "open-local-workspace-input-1", "markerEnd": "arrowclosed"}, {"id": "workspace-to-summary", "type": "custom", "source": "open-local-workspace", "target": "completion-summary", "sourceHandle": "open-local-workspace-output-1", "targetHandle": "completion-summary-input-1", "markerEnd": "arrowclosed"}], "position": [-200, 50], "zoom": 0.6, "viewport": {"x": -200, "y": 50, "zoom": 0.6}}, "settings": {"publicId": "login-then-localdev-clean", "blockDelay": 0, "saveLog": true, "debugMode": false, "restartTimes": 3, "notification": true, "execContext": "popup", "reuseLastState": false, "inputAutocomplete": true, "onError": "stop-workflow", "executedBlockOnWeb": false, "insertDefaultColumn": false, "defaultColumnName": "column", "tabLoadTimeout": 30000}, "globalData": "{\n\t\"workflowType\": \"clean-combined-workflow\",\n\t\"version\": \"3.0.0\",\n\t\"features\": [\"package-integration\", \"session-sync\", \"streamlined-flow\"]\n}", "description": "🔗 简洁组合工作流：使用包引用通用登录，成功后执行简化版一体化商城本地开发（专注sessionId共享）", "includedWorkflows": {"universal-login-xpath-enhanced": {"name": "通用登录工作流包", "description": "通用登录工作流的包引用", "version": "2.0.0"}}}
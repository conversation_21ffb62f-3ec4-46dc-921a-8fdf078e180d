{"extVersion": "1.29.10", "name": "一体化商城本地开发", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"nodes": [{"id": "02pBUI6PycykS4yVkFJeb", "type": "BlockBasic", "initialized": false, "position": {"x": 26.05803976506911, "y": 176.98785948259646}, "data": {"activeInInput": false, "contextMenuName": "", "contextTypes": [], "date": "", "days": [], "delay": 5, "description": "", "disableBlock": false, "interval": 60, "isUrlRegex": false, "observeElement": {"baseElOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}, "baseSelector": "", "matchPattern": "", "selector": "", "targetOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}}, "parameters": [], "preferParamsInTab": false, "shortcut": "", "time": "00:00", "type": "manual", "url": ""}, "label": "trigger"}, {"id": "a991sbt", "type": "BlockBasic", "initialized": false, "position": {"x": 272.1420015055479, "y": 167.54773288083092}, "data": {"active": true, "customUserAgent": false, "description": "打开生产商城", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "https://test-aliwww.lotsmall.cn/manage/workspace", "userAgent": "", "waitTabLoaded": false}, "label": "new-tab"}, {"id": "dkcm9uc", "type": "BlockBasic", "initialized": false, "position": {"x": 259.60689709936514, "y": 347.2272292020906}, "data": {"active": true, "customUserAgent": false, "description": "", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "http://127.0.0.1:8080/manage/login", "userAgent": "", "waitTabLoaded": true}, "label": "new-tab"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "type": "BlockBasic", "initialized": false, "position": {"x": 578.809515185316, "y": 357.9585248798655}, "data": {"code": "/*\n * Automa Script: Get Precise Data from Supabase and Apply\n */\n\n// --- 配置 (不变) ---\nconst GET_URL = 'https://okkgchwzppghiyfgmrlj.supabase.co/functions/v1/get-data?key=my-session';\nconst API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ra2djaHd6cHBnaGl5ZmdtcmxqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTQwNTAsImV4cCI6MjA2NTIzMDA1MH0.LMhY-7H3ySiXEZt2cjLXhhicL4idx0A6xurvxynqJf8';\n\n// --- 核心逻辑 (精准注入版) ---\nasync function getAndApplyPreciseData() {\n    console.log('Automa: Attempting to retrieve precise data from Supabase...');\n    try {\n        const response = await fetch(GET_URL, {\n            method: 'GET',\n            headers: { 'apikey': API_KEY, 'Authorization': 'Bearer ' + API_KEY }\n        });\n\n        if (!response.ok) throw new Error(`Network response was not ok: ${response.status}`);\n        \n        const data = await response.json();\n      // 关键改动：将取回的字符串值，解析成一个真正的JavaScript对象\n        const preciseData = JSON.parse(data.value); \n        // const preciseData = data.value; // value现在是包含三个特定字段的对象\n        // alert(preciseData)\n        if (preciseData) {\n            console.log('Automa: Successfully retrieved precise data.', preciseData);\n            console.log('Automa: SESSIONID cookie：'+preciseData.sessionId);\n\n            // 1. 精确设置Cookie: SESSIONID\n            if (preciseData.sessionId) {\n                // 设置cookie时最好指定path=/，确保全站可用\n                document.cookie = `SESSIONID=${preciseData.sessionId}; path=/`;\n                console.log('Automa: SESSIONID cookie applied.');\n            }\n\n            // 2. 精确设置Local Storage\n            if (preciseData.token) {\n                localStorage.setItem('xjsc_2018_token', preciseData.token);\n                console.log('Automa: xjsc_2018_token applied.');\n            }\n            if (preciseData.userId) {\n                localStorage.setItem('xjsc_2018_userId', preciseData.userId);\n                console.log('Automa: xjsc_2018_userId applied.');\n            }\n\n            // 3. 刷新页面以应用所有更改\n            console.log('Automa: Reloading page...');\n            // location.reload();\n\n        } else {\n            console.error('Automa: No data found in Supabase response.');\n            alert('未能从云端获取到有效的数据。');\n        }\n    } catch (error) {\n        console.error('Automa: Error getting or applying data.', error);\n        alert('从云端获取数据失败，请按F12打开浏览器控制台查看详细错误信息。');\n    }\n}\n\ngetAndApplyPreciseData();", "context": "website", "description": "设置<PERSON><PERSON>", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "settings": {"blockTimeout": 0, "debugMode": false}, "timeout": 20000}, "label": "javascript-code"}, {"id": "uunolg1", "type": "BlockBasic", "initialized": false, "position": {"x": 542.6334445299433, "y": 161.76050841137263}, "data": {"code": "/*\n * Automa Script: Precisely Extract and Send Data to Supabase\n */\n\n// --- 配置 (不变) ---\nconst SET_URL = 'https://okkgchwzppghiyfgmrlj.supabase.co/functions/v1/set-data';\nconst API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ra2djaHd6cHBnaGl5ZmdtcmxqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTQwNTAsImV4cCI6MjA2NTIzMDA1MH0.LMhY-7H3ySiXEZt2cjLXhhicL4idx0A6xurvxynqJf8';\n\n// --- 核心逻辑 (精准提取版) ---\n\n// 1. 定义一个函数，用于从cookie字符串中精确查找某个key的值\nfunction getCookieValue(name) {\n  const cookieString = \"; \" + document.cookie;\n  const parts = cookieString.split(`; ${name}=`);\n  if (parts.length === 2) {\n    return parts.pop().split(';').shift();\n  }\n}\n\n// 2. 精确提取需要的数据\nconst sessionId = getCookieValue('SESSIONID');\nconst token = localStorage.getItem('xjsc_2018_token');\nconst userId = localStorage.getItem('xjsc_2018_userId');\n\n// 3. 将提取到的三项数据打包成一个新对象\nconst payloadValue = {\n  sessionId: sessionId,\n  token: token,\n  userId: userId,\n};\n\n// 4. 发送打包后的精准数据到Supabase\nconsole.log('Automa: Sending precise data to Supabase...', payloadValue);\nfetch(SET_URL, {\n    method: 'POST',\n    headers: {\n        'Content-Type': 'application/json',\n        'apikey': API_KEY,\n        'Authorization': 'Bearer ' + API_KEY\n    },\n    body: JSON.stringify({\n        key: 'my-session',\n        value: payloadValue\n    })\n})\n.then(response => response.json())\n.then(data => console.log('Automa: Successfully sent precise data.', data))\n.catch(error => console.error('Automa: Error sending precise data.', error));", "context": "website", "description": "设置<PERSON><PERSON>", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "settings": {"blockTimeout": 0, "debugMode": false}, "timeout": 20000}, "label": "javascript-code"}, {"id": "l8lyhoa", "type": "BlockBasic", "initialized": false, "position": {"x": 294.6987732855156, "y": 589.8890000924529}, "data": {"active": true, "customUserAgent": false, "description": "", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "http://127.0.0.1:8080/manage/workspace", "userAgent": "", "waitTabLoaded": true}, "label": "new-tab"}, {"id": "i0oaiey", "type": "BlockDelay", "initialized": false, "position": {"x": 152.7407857033308, "y": 470.1807378679664}, "data": {"disableBlock": false, "time": "2000"}, "label": "delay"}], "edges": [{"id": "vueflow__edge-02pBUI6PycykS4yVkFJeb02pBUI6PycykS4yVkFJeb-output-1-a991sbta991sbt-input-1", "type": "custom", "source": "02pBUI6PycykS4yVkFJeb", "target": "a991sbt", "sourceHandle": "02pBUI6PycykS4yVkFJeb-output-1", "targetHandle": "a991sbt-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "class": "connected-edges", "sourceX": 242.05854962010883, "sourceY": 212.987936955216, "targetX": 248.14195250877106, "targetY": 203.54781035345044}, {"id": "vueflow__edge-a991sbta991sbt-output-1-uunolg1uunolg1-input-1", "type": "custom", "source": "a991sbt", "target": "uunolg1", "sourceHandle": "a991sbt-output-1", "targetHandle": "uunolg1-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "class": "connected-edges", "sourceX": 488.1425113605876, "sourceY": 203.54781035345044, "targetX": 518.6333346040269, "targetY": 197.76058588399215}, {"id": "vueflow__edge-dkcm9ucdkcm9uc-output-1-fmirqwifmirqwi-input-1", "type": "custom", "source": "dkcm9uc", "target": "<PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "dkcm9uc-output-1", "targetHandle": "fmirqwi-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "class": "connected-edges", "sourceX": 475.6073460252653, "sourceY": 383.2273066747101, "targetX": 554.8094052593996, "targetY": 393.95857188791524}, {"id": "vueflow__edge-uunolg1uunolg1-output-1-dkcm9ucdkcm9uc-input-1", "type": "custom", "source": "uunolg1", "target": "dkcm9uc", "sourceHandle": "uunolg1-output-1", "targetHandle": "dkcm9uc-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "class": "connected-edges", "sourceX": 758.633954384983, "sourceY": 197.76058588399215, "targetX": 235.6068481025883, "targetY": 383.2273066747101}, {"id": "vueflow__edge-fmirqwifmirqwi-output-1-i0oaieyi0oaiey-input-1", "type": "custom", "source": "<PERSON><PERSON><PERSON><PERSON>", "target": "i0oaiey", "sourceHandle": "fmirqwi-output-1", "targetHandle": "i0oaiey-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 794.8099031820766, "sourceY": 393.95857188791524, "targetX": 128.7406757774144, "targetY": 528.7746502611834}, {"id": "vueflow__edge-i0oaieyi0oaiey-output-1-l8lyhoal8lyhoa-input-1", "type": "custom", "source": "i0oaiey", "target": "l8lyhoa", "sourceHandle": "i0oaiey-output-1", "targetHandle": "l8lyhoa-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 368.74129555837055, "sourceY": 528.7746502611834, "targetX": 270.6986633595992, "targetY": 625.8890471005026}], "position": [24.49300239175477, -235.0499777901041], "zoom": 1.0017424408659286, "viewport": {"x": 24.49300239175477, "y": -235.0499777901041, "zoom": 1.0017424408659286}}, "settings": {"publicId": "localdev", "restartTimes": 3, "notification": true, "tabLoadTimeout": 30000, "inputAutocomplete": true, "insertDefaultColumn": false, "defaultColumnName": "column", "blockDelay": 0, "debugMode": false, "execContext": "popup", "executedBlockOnWeb": false, "onError": "stop-workflow", "reuseLastState": false, "saveLog": true}, "globalData": "{\n  \"githubName\": \"Yg<PERSON>\"\n}", "description": "sessionId共享", "includedWorkflows": {}}
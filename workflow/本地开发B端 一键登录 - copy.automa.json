{"extVersion": "1.29.10", "name": "本地开发B端 一键登录 - copy", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"nodes": [{"id": "trigger-config", "type": "BlockBasic", "initialized": false, "position": {"x": 96, "y": 36}, "data": {"disableBlock": false, "description": "🚀 通用登录配置触发器", "type": "manual", "interval": 60, "delay": 2, "date": "", "time": "00:00", "url": "", "shortcut": "", "activeInInput": false, "isUrlRegex": false, "days": [], "contextMenuName": "", "contextTypes": [], "parameters": [{"data": {"required": true}, "defaultValue": "{\n  \"loginUrl\": \"https://test-aliuser.lotsmall.cn/usercenter/login\",\n   \"redirectUrl\": \"https://test-aliwww.lotsmall.cn/manage/customForm\",\n  \"credentials\": {\n    \"username\": \"18766668891\",\n    \"password\": \"123456asd.\"\n  },\n  \"selectorMode\": \"css\",\n  \"selectors\":{\n    \"username\": \".sdi-form-item-content > .sdi-input-type-text > .sdi-input\",\n    \"password\": \".sdi-input-type-password > .sdi-input\",\n    \"captcha\": \".checkcode-warper .sdi-input\",\n    \"captchaImage\": \"img.code-img\",\n    \"captchaRefresh\": \"i.sdi-icon-ios-refresh\",\n    \"loginButton\": \"button.login-btn\"\n  },\n  \"postLoginAction\": {\n    \"type\": \"click_app_card\",\n    \"targetText\": \"智游宝数智平台\"\n  },\n  \"options\": {\n    \"maxRetries\": 3,\n    \"showProgress\": true,\n    \"skipPostAction\": false,\n    \"ocrConfig\": {\n      \"url\": \"https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize\",\n      \"headers\": {\n        \"Cookie\": \"tr_de_id=ZHYtSEhOUlZRWkVXSFZI\"\n      }\n    }\n  }\n}", "description": "🔧 登录配置（支持XPath简化字符串）", "id": "1rYA", "name": "loginConfig", "placeholder": "Universal Login Configuration", "type": "json"}], "preferParamsInTab": false, "observeElement": {"selector": "", "baseSelector": "", "matchPattern": "", "targetOptions": {"subtree": false, "childList": true, "attributes": false, "attributeFilter": [], "characterData": false}, "baseElOptions": {"subtree": false, "childList": true, "attributes": false, "attributeFilter": [], "characterData": false}}}, "label": "trigger"}, {"id": "config-processor", "type": "BlockBasic", "initialized": false, "position": {"x": 400, "y": 36}, "data": {"code": "// === 配置处理器：支持XPath简化字符串格式 ===\ntry {\n  console.log('🔧 === 配置处理开始 ===');\n  \n  const rawConfig = automaRefData('variables', 'loginConfig') || {};\n  console.log('原始配置:', rawConfig);\n  \n  // 1. 配置标准化\n  const config = normalizeConfig(rawConfig);\n  console.log('标准化后配置:', config);\n  \n  // 2. 配置验证\n  const validation = validateConfig(config);\n  if (!validation.valid) {\n    throw new Error('配置验证失败: ' + validation.errors.join(', '));\n  }\n  \n  // 3. 设置运行时配置\n  automaSetVariable('runtimeConfig', config);\n  automaSetVariable('currentRetry', 0);\n  automaSetVariable('loginSuccess', false);\n  \n  console.log('✅ 配置处理完成');\n  \n  // === 核心函数 ===\n  \n  function normalizeConfig(rawConfig) {\n    const config = JSON.parse(JSON.stringify(rawConfig));\n    \n    // 标准化选择器格式\n    if (config.selectors && config.selectorMode) {\n      config.selectors = normalizeSelectors(config.selectors, config.selectorMode);\n      console.log('✅ 选择器标准化完成');\n    }\n    \n    // 设置默认值\n    config.options = Object.assign({\n      maxRetries: 3,\n      showProgress: true,\n      skipPostAction: false\n    }, config.options || {});\n    \n    return config;\n  }\n  \n  function normalizeSelectors(selectors, selectorMode) {\n    const normalized = {};\n    \n    for (const [key, value] of Object.entries(selectors)) {\n      if (typeof value === 'string') {\n        // 简化字符串格式自动转换\n        if (selectorMode === 'xpath') {\n          normalized[key] = { xpath: value };\n          console.log(`📝 XPath字符串转换: ${key} -> ${value}`);\n        } else {\n          normalized[key] = { css: value };\n          console.log(`📝 CSS字符串转换: ${key} -> ${value}`);\n        }\n      } else if (typeof value === 'object' && value !== null) {\n        // 对象格式直接使用\n        normalized[key] = value;\n        console.log(`📋 对象格式保留: ${key}`);\n      } else {\n        console.warn(`⚠️ 跳过无效选择器: ${key} = ${value}`);\n      }\n    }\n    \n    return normalized;\n  }\n  \n  function validateConfig(config) {\n    const errors = [];\n    \n    // 必需字段检查\n    if (!config.loginUrl) errors.push('缺少loginUrl');\n    if (!config.credentials?.username) errors.push('缺少用户名');\n    if (!config.credentials?.password) errors.push('缺少密码');\n    if (!config.selectors) errors.push('缺少选择器配置');\n    \n    // 选择器验证\n    const requiredSelectors = ['username', 'password', 'loginButton'];\n    for (const selector of requiredSelectors) {\n      if (!config.selectors[selector]) {\n        errors.push(`缺少${selector}选择器`);\n      }\n    }\n    \n    // XPath格式验证\n    if (config.selectorMode === 'xpath') {\n      for (const [key, selectorConfig] of Object.entries(config.selectors)) {\n        if (selectorConfig.xpath && !selectorConfig.xpath.startsWith('/')) {\n          errors.push(`${key}的XPath格式错误，应以/开头`);\n        }\n      }\n    }\n    \n    return {\n      valid: errors.length === 0,\n      errors: errors\n    };\n  }\n  \n} catch (error) {\n  console.error('❌ 配置处理失败:', error);\n  automaSetVariable('configError', error.message);\n} finally {\n  automaNextBlock();\n}", "context": "background", "description": "⚙️ 配置处理器（XPath简化支持）", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "timeout": 5000}, "label": "javascript-code"}, {"id": "open-login-page", "type": "BlockBasic", "initialized": false, "position": {"x": 549.3947525497706, "y": -159.87387674162792}, "data": {"active": true, "customUserAgent": false, "description": "🌐 打开登录页面", "disableBlock": false, "inGroup": false, "settings": {"blockTimeout": 20000, "debugMode": false}, "tabZoom": 1, "updatePrevTab": false, "url": "{{variables.runtimeConfig.loginUrl}}", "userAgent": "", "waitTabLoaded": true}, "label": "new-tab"}, {"id": "enhanced-login-engine", "type": "BlockBasic", "initialized": false, "position": {"x": 1000, "y": 36}, "data": {"code": "// === 增强登录引擎：支持XPath简化字符串 ===\n(async () => {\n  try {\n    const config = automaRefData('variables', 'runtimeConfig') || {};\n    let currentRetry = automaRefData('variables', 'currentRetry') || 0;\n    const maxRetries = config.options?.maxRetries || 3;\n    \n    console.log(`🚀 === 登录尝试 ${currentRetry + 1}/${maxRetries} ===`);\n    \n    // 显示进度\n    if (config.options?.showProgress) {\n      showProgress(`登录尝试 ${currentRetry + 1}/${maxRetries}`, '正在准备登录...');\n    }\n    \n    // 等待页面准备\n    await waitForPageReady();\n    \n    // 刷新验证码（重试时）\n    if (currentRetry > 0) {\n      await refreshCaptcha();\n    }\n    \n    // 识别验证码\n    const captcha = await recognizeCaptcha();\n    console.log(`✅ 验证码识别: ${captcha}`);\n    \n    // 填充表单\n    await fillLoginForm({\n      username: config.credentials.username,\n      password: config.credentials.password,\n      captcha: captcha\n    });\n    \n    // 点击登录\n    await clickLoginButton();\n    \n    // 检查登录结果\n    const result = await checkLoginResult();\n    \n    if (result.success) {\n      console.log('🎉 登录成功！');\n      automaSetVariable('loginSuccess', true);\n      if (config.options?.showProgress) {\n        showProgress('登录成功', '准备执行后续动作...');\n      }\n    } else {\n      // 登录失败处理\n      currentRetry++;\n      automaSetVariable('currentRetry', currentRetry);\n      \n      if (currentRetry < maxRetries) {\n        console.log(`❌ 登录失败: ${result.error}`);\n        console.log(`🔄 准备第 ${currentRetry + 1} 次重试...`);\n        automaSetVariable('shouldRetry', true);\n        \n        if (config.options?.showProgress) {\n          showProgress('登录失败', `准备重试 ${currentRetry + 1}/${maxRetries}`);\n        }\n      } else {\n        console.error(`💥 登录最终失败，已尝试 ${maxRetries} 次`);\n        automaSetVariable('loginSuccess', false);\n        automaSetVariable('finalError', result.error);\n      }\n    }\n    \n    // === 核心功能函数 ===\n    \n    async function waitForPageReady() {\n      const timeout = 15000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        if (document.querySelector('.sdi-form-item-content, .form-warper, form')) {\n          await sleep(1000);\n          return;\n        }\n        await sleep(200);\n      }\n      throw new Error('页面加载超时');\n    }\n    \n    async function recognizeCaptcha() {\n      const captchaImageConfig = config.selectors?.captchaImage;\n      if (!captchaImageConfig) {\n        console.log('⚠️ 未配置验证码图片选择器，跳过识别');\n        return '';\n      }\n      \n      const img = await findElementEnhanced(captchaImageConfig);\n      if (!img) throw new Error('验证码图片未找到');\n      \n      // 转换为Base64\n      const response = await fetch(img.src);\n      const blob = await response.blob();\n      const base64 = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsDataURL(blob);\n      });\n      \n      // 调用OCR\n      const ocrConfig = config.options?.ocrConfig || {};\n      const ocr = await automaFetch('json', {\n        url: ocrConfig.url || 'https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize',\n        method: 'POST',\n        headers: Object.assign({\n          'Content-Type': 'application/json'\n        }, ocrConfig.headers || {}),\n        body: JSON.stringify({ imageData: base64 })\n      });\n      \n      if (!ocr?.success || !ocr.data?.text) {\n        throw new Error('OCR识别失败');\n      }\n      \n      return ocr.data.text.trim();\n    }\n    \n    async function fillLoginForm(data) {\n      const fieldMappings = {\n        username: data.username,\n        password: data.password,\n        captcha: data.captcha\n      };\n      \n      for (const [fieldName, value] of Object.entries(fieldMappings)) {\n        if (!value || !config.selectors[fieldName]) continue;\n        \n        const element = await findElementEnhanced(config.selectors[fieldName]);\n        if (!element) {\n          console.warn(`⚠️ ${fieldName}字段未找到，跳过`);\n          continue;\n        }\n        \n        // 聚焦并填充\n        element.focus();\n        element.click();\n        await sleep(200);\n        \n        element.value = '';\n        element.value = value;\n        \n        // 触发事件\n        ['input', 'change', 'blur'].forEach(eventType => {\n          element.dispatchEvent(new Event(eventType, { bubbles: true }));\n        });\n        \n        console.log(`✅ ${fieldName}填充完成`);\n        await sleep(300);\n      }\n    }\n    \n    async function clickLoginButton() {\n      const buttonConfig = config.selectors?.loginButton;\n      if (!buttonConfig) throw new Error('未配置登录按钮选择器');\n      \n      const button = await findElementEnhanced(buttonConfig);\n      if (!button) throw new Error('登录按钮未找到');\n      \n      button.scrollIntoView({ block: 'center' });\n      await sleep(500);\n      button.click();\n      console.log('✅ 登录按钮已点击');\n    }\n    \n    async function checkLoginResult() {\n      await sleep(3000);\n      \n      // 检查URL变化\n      if (window.location.href.includes('/worktable') || \n          window.location.href.includes('/personal') ||\n          window.location.href.includes('/dashboard')) {\n        return { success: true };\n      }\n      \n      // 检查页面内容\n      const pageText = document.body.textContent || '';\n      if (pageText.includes('我的应用') || \n          pageText.includes('工作台') || \n          pageText.includes('欢迎')) {\n        return { success: true };\n      }\n      \n      // 检查错误信息\n      const errorSelectors = ['.error-message', '[class*=\"error\"]', '.ant-message-error'];\n      for (const selector of errorSelectors) {\n        const errorEl = document.querySelector(selector);\n        if (errorEl && errorEl.textContent.trim()) {\n          return { success: false, error: errorEl.textContent.trim() };\n        }\n      }\n      \n      // 检查是否还在登录页面\n      const loginButtonConfig = config.selectors?.loginButton;\n      if (loginButtonConfig && findElementEnhanced(loginButtonConfig)) {\n        return { success: false, error: '仍在登录页面，可能是验证码错误' };\n      }\n      \n      return { success: false, error: '未知登录状态' };\n    }\n    \n    async function refreshCaptcha() {\n      const refreshSelectors = [\n        'i.sdi-icon-ios-refresh',\n        '.captcha-refresh',\n        '[class*=\"refresh\"]'\n      ];\n      \n      for (const selector of refreshSelectors) {\n        const refresh = document.querySelector(selector);\n        if (refresh) {\n          refresh.click();\n          await sleep(2000);\n          console.log('✅ 验证码已刷新');\n          return;\n        }\n      }\n    }\n    \n    // === 增强的元素查找函数 ===\n    function findElementEnhanced(selectorConfig) {\n      if (!selectorConfig) return null;\n      \n      // 支持直接传入字符串（自动判断类型）\n      if (typeof selectorConfig === 'string') {\n        if (selectorConfig.startsWith('//') || selectorConfig.startsWith('/')) {\n          return getElementByXPath(selectorConfig);\n        } else {\n          return document.querySelector(selectorConfig);\n        }\n      }\n      \n      // 对象格式处理\n      if (typeof selectorConfig === 'object') {\n        // 优先使用xpath\n        if (selectorConfig.xpath) {\n          const element = getElementByXPath(selectorConfig.xpath);\n          if (element) return element;\n        }\n        \n        // 回退到css\n        if (selectorConfig.css) {\n          try {\n            const element = document.querySelector(selectorConfig.css);\n            if (element) return element;\n          } catch (error) {\n            console.warn('CSS选择器错误:', selectorConfig.css, error);\n          }\n        }\n        \n        // 其他备用选择器\n        if (selectorConfig.id) {\n          return document.getElementById(selectorConfig.id);\n        }\n      }\n      \n      return null;\n    }\n    \n    function getElementByXPath(xpath) {\n      try {\n        const result = document.evaluate(\n          xpath, \n          document, \n          null, \n          XPathResult.FIRST_ORDERED_NODE_TYPE, \n          null\n        );\n        return result.singleNodeValue;\n      } catch (error) {\n        console.error('XPath执行错误:', xpath, error);\n        return null;\n      }\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n    function showProgress(title, message) {\n      let div = document.getElementById('universal-progress');\n      if (!div) {\n        div = document.createElement('div');\n        div.id = 'universal-progress';\n        div.style.cssText = `\n          position: fixed; top: 20px; right: 20px;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white; padding: 15px; border-radius: 8px;\n          z-index: 99999; font-size: 14px;\n          box-shadow: 0 4px 15px rgba(0,0,0,0.3);\n          max-width: 300px;\n        `;\n        document.body.appendChild(div);\n      }\n      div.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 5px;\">🤖 ${title}</div>\n        <div style=\"font-size: 12px; opacity: 0.9;\">${message}</div>\n      `;\n    }\n    \n  } catch (error) {\n    console.error('❌ 登录引擎执行失败:', error);\n    \n    const currentRetry = automaRefData('variables', 'currentRetry') || 0;\n    const maxRetries = (automaRefData('variables', 'runtimeConfig') || {}).options?.maxRetries || 3;\n    \n    if (currentRetry < maxRetries - 1) {\n      automaSetVariable('shouldRetry', true);\n    } else {\n      automaSetVariable('loginSuccess', false);\n      automaSetVariable('finalError', error.message);\n    }\n  } finally {\n    automaNextBlock();\n  }\n})();", "context": "website", "description": "🧠 增强登录引擎（XPath智能支持）", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "timeout": 40000}, "label": "javascript-code"}, {"id": "post-login-handler", "type": "BlockBasic", "initialized": false, "position": {"x": 56.88665078852455, "y": 220.92358437128286}, "data": {"code": "// === 登录后动作处理器 ===\n(async () => {\n  try {\n    const config = automaRefData('variables', 'runtimeConfig') || {};\n    const postAction = config.postLoginAction || {};\n    \n    if (config.options?.skipPostAction || postAction.type === 'none') {\n      console.log('⏩ 跳过后续动作');\n      return;\n    }\n    \n    console.log('🎯 执行登录后动作:', postAction.type);\n    \n    switch (postAction.type) {\n      case 'direct_redirect':\n        await handleDirectRedirect(postAction);\n        break;\n      case 'click_app_card':\n        await handleClickAppCard(postAction);\n        break;\n      case 'click_menu_item':\n        await handleClickMenuItem(postAction);\n        break;\n      case 'custom_click':\n        await handleCustomClick(postAction);\n        break;\n      default:\n        console.log('ℹ️ 无需执行后续动作');\n    }\n    \n    // === 动作处理函数 ===\n    \n    async function handleDirectRedirect(action) {\n      if (action.redirectUrl) {\n        console.log(`🔗 直接跳转: ${action.redirectUrl}`);\n        window.location.href = action.redirectUrl;\n      }\n    }\n    \n    async function handleClickAppCard(action) {\n      const targetText = action.targetText;\n      if (!targetText) throw new Error('未指定目标应用名称');\n      \n      console.log(`📱 查找应用卡片: ${targetText}`);\n      \n      // 等待应用列表加载\n      await waitForApps();\n      \n      // 查找目标应用\n      const appData = findTargetApp(targetText);\n      if (!appData.found) {\n        throw new Error(`应用未找到: ${targetText}`);\n      }\n      \n      // 生成并点击\n      const selector = `.my-app__item:nth-child(${appData.position}) .my-app__item-footer`;\n      const element = document.querySelector(selector);\n      if (element) {\n        element.click();\n        console.log(`✅ 已点击应用: ${targetText}`);\n      } else {\n        throw new Error('应用点击失败');\n      }\n    }\n    \n    async function handleClickMenuItem(action) {\n      const targetText = action.targetText;\n      if (!targetText) throw new Error('未指定目标菜单');\n      \n      console.log(`📋 查找菜单项: ${targetText}`);\n      \n      const menuSelectors = [\n        `a[href*=\"${targetText.toLowerCase()}\"]`,\n        `*[text()*=\"${targetText}\"]`,\n        `.menu-item:contains(\"${targetText}\")`,\n        `[title*=\"${targetText}\"]`\n      ];\n      \n      for (const selector of menuSelectors) {\n        const element = document.querySelector(selector);\n        if (element) {\n          element.click();\n          console.log(`✅ 已点击菜单: ${targetText}`);\n          return;\n        }\n      }\n      \n      throw new Error(`菜单项未找到: ${targetText}`);\n    }\n    \n    async function handleCustomClick(action) {\n      const targetText = action.targetText;\n      const targetUrl = action.targetUrl;\n      \n      if (targetUrl) {\n        console.log(`🔗 跳转到: ${targetUrl}`);\n        window.location.href = targetUrl;\n      } else if (targetText) {\n        console.log(`🎯 查找并点击: ${targetText}`);\n        \n        const element = document.querySelector(`*:contains(\"${targetText}\")`) ||\n                       document.evaluate(`//*[contains(text(), \"${targetText}\")]`, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;\n        \n        if (element) {\n          element.click();\n          console.log(`✅ 已点击: ${targetText}`);\n        } else {\n          throw new Error(`目标元素未找到: ${targetText}`);\n        }\n      }\n    }\n    \n    // === 辅助函数 ===\n    \n    async function waitForApps() {\n      const timeout = 10000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        const apps = document.querySelectorAll('div.my-app__item');\n        if (apps.length > 0) {\n          await sleep(1000);\n          return;\n        }\n        await sleep(500);\n      }\n      throw new Error('应用列表加载超时');\n    }\n    \n    function findTargetApp(target) {\n      const apps = document.querySelectorAll('div.my-app__item');\n      \n      for (let i = 0; i < apps.length; i++) {\n        const text = apps[i].textContent.replace(/\\s+/g, ' ').trim();\n        console.log(`检查应用 ${i + 1}: ${text}`);\n        \n        if (text.includes(target)) {\n          return {\n            found: true,\n            position: i + 1,\n            element: apps[i],\n            text: text\n          };\n        }\n      }\n      \n      return { found: false };\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n  } catch (error) {\n    console.error('❌ 登录后动作执行失败:', error);\n  } finally {\n    automaNextBlock();\n  }\n})();", "context": "website", "description": "🎯 登录后动作处理器", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "timeout": 20000}, "label": "javascript-code"}, {"id": "ae5iy1o", "type": "BlockBasic", "initialized": false, "position": {"x": 876.6923076923074, "y": 348.84615384615387}, "data": {"description": "", "disableBlock": false, "executeId": "", "globalData": "", "insertAllGlobalData": false, "insertAllVars": false, "workflowId": "Abq9TqnSlPv5nIiKKMoad"}, "label": "execute-workflow"}, {"id": "gu0rp04", "type": "BlockBasic", "initialized": false, "position": {"x": 856.2071687221271, "y": 223.5469890007876}, "data": {"disableBlock": false, "description": "🌐 打开登录页面", "url": "{{variables.runtimeConfig.redirectUrl}}", "userAgent": "", "active": true, "tabZoom": 1, "inGroup": false, "waitTabLoaded": true, "updatePrevTab": false, "customUserAgent": false, "settings": {"blockTimeout": 20000, "debugMode": false}}, "label": "new-tab"}, {"id": "5cizvug", "type": "BlockDelay", "initialized": false, "position": {"x": 477.70217945462946, "y": 216.71215798962487}, "data": {"disableBlock": false, "time": "1000"}, "label": "delay"}], "edges": [{"id": "trigger-to-processor", "type": "custom", "source": "trigger-config", "target": "config-processor", "sourceHandle": "trigger-config-output-1", "targetHandle": "config-processor-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 311.999725827974, "sourceY": 71.99995098909365, "targetX": 376.0000116469977, "targetY": 71.99995098909365}, {"id": "vueflow__edge-enhanced-login-engineenhanced-login-engine-output-1-post-login-handlerpost-login-handler-input-1", "type": "custom", "source": "enhanced-login-engine", "target": "post-login-handler", "sourceHandle": "enhanced-login-engine-output-1", "targetHandle": "post-login-handler-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 1215.9996726938605, "sourceY": 71.99995098909365, "targetX": 32.886689002579025, "targetY": 256.9235619274333}, {"id": "vueflow__edge-config-processorconfig-processor-output-1-open-login-pageopen-login-page-input-1", "type": "custom", "source": "config-processor", "target": "open-login-page", "sourceHandle": "config-processor-output-1", "targetHandle": "open-login-page-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 615.9996726938605, "sourceY": 71.99995098909365, "targetX": 525.3947641967683, "targetY": -123.87391246900589}, {"id": "vueflow__edge-open-login-pageopen-login-page-output-1-enhanced-login-engineenhanced-login-engine-input-1", "type": "custom", "source": "open-login-page", "target": "enhanced-login-engine", "sourceHandle": "open-login-page-output-1", "targetHandle": "enhanced-login-engine-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "class": "source-open-login-page-output-1 target-enhanced-login-engine-input-1", "sourceX": 765.3944252436311, "sourceY": -123.87391246900589, "targetX": 975.9999585128842, "targetY": 71.99995098909365}, {"id": "vueflow__edge-post-login-handlerpost-login-handler-output-1-5cizvug5cizvug-input-1", "type": "custom", "source": "post-login-handler", "target": "5cizvug", "sourceHandle": "post-login-handler-output-1", "targetHandle": "5cizvug-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 272.8863500494418, "sourceY": 256.9235619274333, "targetX": 453.70219110162714, "targetY": 275.3058233151015}, {"id": "vueflow__edge-5cizvug5cizvug-output-1-gu0rp04gu0rp04-input-1", "type": "custom", "source": "5cizvug", "target": "gu0rp04", "sourceHandle": "5cizvug-output-1", "targetHandle": "gu0rp04-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 693.701958416717, "sourceY": 275.3058233151015, "targetX": 832.2071272350113, "targetY": 259.54691342282445}], "position": [-10.735707718031335, 289.0631006094604], "zoom": 1.148698354997035, "viewport": {"x": -10.735707718031335, "y": 289.0631006094604, "zoom": 1.148698354997035}}, "settings": {"publicId": "auto-local-login", "blockDelay": 0, "saveLog": true, "debugMode": false, "restartTimes": 3, "notification": true, "execContext": "popup", "reuseLastState": false, "inputAutocomplete": true, "onError": "stop-workflow", "executedBlockOnWeb": false, "insertDefaultColumn": false, "defaultColumnName": "column", "tabLoadTimeout": 30000}, "globalData": "{\n\t\"workflowType\": \"universal-login-xpath-enhanced\",\n\t\"version\": \"2.0.0\",\n\t\"features\": [\"xpath-string-support\", \"intelligent-retry\", \"universal-config\"]\n}", "description": "🚀 通用登录工作流（XPath简化增强版）：支持XPath字符串简化格式、智能重试、多种后续动作处理", "includedWorkflows": {"Abq9TqnSlPv5nIiKKMoad": {"extVersion": "1.29.10", "name": "一体化商城本地开发", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"edges": [{"data": {}, "id": "vueflow__edge-02pBUI6PycykS4yVkFJeb02pBUI6PycykS4yVkFJeb-output-1-a991sbta991sbt-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "02pBUI6PycykS4yVkFJeb", "sourceHandle": "02pBUI6PycykS4yVkFJeb-output-1", "sourceX": 242.05855723625126, "sourceY": 212.987936955216, "target": "a991sbt", "targetHandle": "a991sbt-input-1", "targetX": 248.14195250877106, "targetY": 203.54781035345044, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-a991sbta991sbt-output-1-uunolg1uunolg1-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "a991sbt", "sourceHandle": "a991sbt-output-1", "sourceX": 488.1425113605876, "sourceY": 203.54781035345044, "target": "uunolg1", "targetHandle": "uunolg1-input-1", "targetX": 518.6333346040269, "targetY": 197.76058588399215, "type": "custom", "updatable": true}, {"class": "connected-edges", "data": {}, "id": "vueflow__edge-dkcm9ucdkcm9uc-output-1-fmirqwifmirqwi-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "dkcm9uc", "sourceHandle": "dkcm9uc-output-1", "sourceX": 475.6073460252653, "sourceY": 383.2273066747101, "target": "<PERSON><PERSON><PERSON><PERSON>", "targetHandle": "fmirqwi-input-1", "targetX": 554.8094661885391, "targetY": 393.95857188791524, "type": "custom", "updatable": true}, {"class": "connected-edges", "data": {}, "id": "vueflow__edge-uunolg1uunolg1-output-1-dkcm9ucdkcm9uc-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "uunolg1", "sourceHandle": "uunolg1-output-1", "sourceX": 758.6338934558435, "sourceY": 197.76058588399215, "target": "dkcm9uc", "targetHandle": "dkcm9uc-input-1", "targetX": 235.6068481025883, "targetY": 383.2273066747101, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-fmirqwifmirqwi-output-1-i0oaieyi0oaiey-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "<PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "fmirqwi-output-1", "sourceX": 794.8100250403556, "sourceY": 393.95857188791524, "target": "i0oaiey", "targetHandle": "i0oaiey-input-1", "targetX": 128.74073670655395, "targetY": 528.7746502611834, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-i0oaieyi0oaiey-output-1-l8lyhoal8lyhoa-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "i0oaiey", "sourceHandle": "i0oaiey-output-1", "sourceX": 368.74129555837055, "sourceY": 528.7746502611834, "target": "l8lyhoa", "targetHandle": "l8lyhoa-input-1", "targetX": 270.69869382416897, "targetY": 625.8890471005026, "type": "custom", "updatable": true}], "nodes": [{"data": {"activeInInput": false, "contextMenuName": "", "contextTypes": [], "date": "", "days": [], "delay": 5, "description": "", "disableBlock": false, "interval": 60, "isUrlRegex": false, "observeElement": {"baseElOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}, "baseSelector": "", "matchPattern": "", "selector": "", "targetOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}}, "parameters": [], "preferParamsInTab": false, "shortcut": "", "time": "00:00", "type": "manual", "url": ""}, "id": "02pBUI6PycykS4yVkFJeb", "initialized": false, "label": "trigger", "position": {"x": 26.05803976506911, "y": 176.98785948259646}, "type": "BlockBasic"}, {"data": {"active": true, "customUserAgent": false, "description": "打开生产商城", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "https://test-aliwww.lotsmall.cn/manage/workspace", "userAgent": "", "waitTabLoaded": false}, "id": "a991sbt", "initialized": false, "label": "new-tab", "position": {"x": 272.1420015055479, "y": 167.54773288083092}, "type": "BlockBasic"}, {"data": {"active": true, "customUserAgent": false, "description": "", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "http://127.0.0.1:8080/manage/login", "userAgent": "", "waitTabLoaded": true}, "id": "dkcm9uc", "initialized": false, "label": "new-tab", "position": {"x": 259.60689709936514, "y": 347.2272292020906}, "type": "BlockBasic"}, {"data": {"code": "/*\n * Automa Script: Get Precise Data from Supabase and Apply\n */\n\n// --- 配置 (不变) ---\nconst GET_URL = 'https://okkgchwzppghiyfgmrlj.supabase.co/functions/v1/get-data?key=my-session';\nconst API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ra2djaHd6cHBnaGl5ZmdtcmxqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTQwNTAsImV4cCI6MjA2NTIzMDA1MH0.LMhY-7H3ySiXEZt2cjLXhhicL4idx0A6xurvxynqJf8';\n\n// --- 核心逻辑 (精准注入版) ---\nasync function getAndApplyPreciseData() {\n    console.log('Automa: Attempting to retrieve precise data from Supabase...');\n    try {\n        const response = await fetch(GET_URL, {\n            method: 'GET',\n            headers: { 'apikey': API_KEY, 'Authorization': 'Bearer ' + API_KEY }\n        });\n\n        if (!response.ok) throw new Error(`Network response was not ok: ${response.status}`);\n        \n        const data = await response.json();\n      // 关键改动：将取回的字符串值，解析成一个真正的JavaScript对象\n        const preciseData = JSON.parse(data.value); \n        // const preciseData = data.value; // value现在是包含三个特定字段的对象\n        // alert(preciseData)\n        if (preciseData) {\n            console.log('Automa: Successfully retrieved precise data.', preciseData);\n            console.log('Automa: SESSIONID cookie：'+preciseData.sessionId);\n\n            // 1. 精确设置Cookie: SESSIONID\n            if (preciseData.sessionId) {\n                // 设置cookie时最好指定path=/，确保全站可用\n                document.cookie = `SESSIONID=${preciseData.sessionId}; path=/`;\n                console.log('Automa: SESSIONID cookie applied.');\n            }\n\n            // 2. 精确设置Local Storage\n            if (preciseData.token) {\n                localStorage.setItem('xjsc_2018_token', preciseData.token);\n                console.log('Automa: xjsc_2018_token applied.');\n            }\n            if (preciseData.userId) {\n                localStorage.setItem('xjsc_2018_userId', preciseData.userId);\n                console.log('Automa: xjsc_2018_userId applied.');\n            }\n\n            // 3. 刷新页面以应用所有更改\n            console.log('Automa: Reloading page...');\n            // location.reload();\n\n        } else {\n            console.error('Automa: No data found in Supabase response.');\n            alert('未能从云端获取到有效的数据。');\n        }\n    } catch (error) {\n        console.error('Automa: Error getting or applying data.', error);\n        alert('从云端获取数据失败，请按F12打开浏览器控制台查看详细错误信息。');\n    }\n}\n\ngetAndApplyPreciseData();", "context": "website", "description": "设置<PERSON><PERSON>", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "settings": {"blockTimeout": 0, "debugMode": false}, "timeout": 20000}, "id": "<PERSON><PERSON><PERSON><PERSON>", "initialized": false, "label": "javascript-code", "position": {"x": 578.809515185316, "y": 357.9585248798655}, "type": "BlockBasic"}, {"data": {"code": "/*\n * Automa Script: Precisely Extract and Send Data to Supabase\n */\n\n// --- 配置 (不变) ---\nconst SET_URL = 'https://okkgchwzppghiyfgmrlj.supabase.co/functions/v1/set-data';\nconst API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ra2djaHd6cHBnaGl5ZmdtcmxqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTQwNTAsImV4cCI6MjA2NTIzMDA1MH0.LMhY-7H3ySiXEZt2cjLXhhicL4idx0A6xurvxynqJf8';\n\n// --- 核心逻辑 (精准提取版) ---\n\n// 1. 定义一个函数，用于从cookie字符串中精确查找某个key的值\nfunction getCookieValue(name) {\n  const cookieString = \"; \" + document.cookie;\n  const parts = cookieString.split(`; ${name}=`);\n  if (parts.length === 2) {\n    return parts.pop().split(';').shift();\n  }\n}\n\n// 2. 精确提取需要的数据\nconst sessionId = getCookieValue('SESSIONID');\nconst token = localStorage.getItem('xjsc_2018_token');\nconst userId = localStorage.getItem('xjsc_2018_userId');\n\n// 3. 将提取到的三项数据打包成一个新对象\nconst payloadValue = {\n  sessionId: sessionId,\n  token: token,\n  userId: userId,\n};\n\n// 4. 发送打包后的精准数据到Supabase\nconsole.log('Automa: Sending precise data to Supabase...', payloadValue);\nfetch(SET_URL, {\n    method: 'POST',\n    headers: {\n        'Content-Type': 'application/json',\n        'apikey': API_KEY,\n        'Authorization': 'Bearer ' + API_KEY\n    },\n    body: JSON.stringify({\n        key: 'my-session',\n        value: payloadValue\n    })\n})\n.then(response => response.json())\n.then(data => console.log('Automa: Successfully sent precise data.', data))\n.catch(error => console.error('Automa: Error sending precise data.', error));", "context": "website", "description": "设置<PERSON><PERSON>", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "settings": {"blockTimeout": 0, "debugMode": false}, "timeout": 20000}, "id": "uunolg1", "initialized": false, "label": "javascript-code", "position": {"x": 542.6334445299433, "y": 161.76050841137263}, "type": "BlockBasic"}, {"data": {"active": true, "customUserAgent": false, "description": "", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "http://127.0.0.1:8080/manage/workspace", "userAgent": "", "waitTabLoaded": true}, "id": "l8lyhoa", "initialized": false, "label": "new-tab", "position": {"x": 294.6987732855156, "y": 589.8890000924529}, "type": "BlockBasic"}, {"data": {"disableBlock": false, "time": "2000"}, "id": "i0oaiey", "initialized": false, "label": "delay", "position": {"x": 152.7407857033308, "y": 470.1807378679664}, "type": "BlockDelay"}], "position": [0, 0], "viewport": {"x": 0, "y": 0, "zoom": 1.0017424408659286}, "zoom": 1.0017424408659286}, "settings": {"blockDelay": 0, "debugMode": false, "defaultColumnName": "column", "execContext": "popup", "executedBlockOnWeb": false, "inputAutocomplete": true, "insertDefaultColumn": false, "notification": true, "onError": "stop-workflow", "publicId": "localdev", "restartTimes": 3, "reuseLastState": false, "saveLog": true, "tabLoadTimeout": 30000}, "globalData": "{\n  \"githubName\": \"Yg<PERSON>\"\n}", "description": "sessionId共享"}}}
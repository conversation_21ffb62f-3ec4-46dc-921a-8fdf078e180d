{"extVersion": "1.29.10", "name": "本地开发B端 一键登录 - 智能检查版", "icon": "riFocus2Line", "version": "3.0.0", "drawflow": {"nodes": [{"id": "trigger-config", "type": "BlockBasic", "initialized": false, "position": {"x": 60, "y": 80}, "data": {"disableBlock": false, "description": "🚀 通用登录配置触发器", "type": "manual", "parameters": [{"data": {"required": true}, "defaultValue": "{\n  \"loginUrl\": \"https://test-aliuser.lotsmall.cn/usercenter/login\",\n  \"redirectUrl\": \"https://test-aliwww.lotsmall.cn/manage/customForm\",\n  \"credentials\": {\n    \"username\": \"18766668891\",\n    \"password\": \"123456asd.\"\n  },\n  \"selectorMode\": \"css\",\n  \"selectors\":{\n    \"username\": {\"css\": \".sdi-form-item-content > .sdi-input-type-text > .sdi-input\"},\n    \"password\": {\"css\": \".sdi-input-type-password > .sdi-input\"},\n    \"captcha\": {\"css\": \".checkcode-warper .sdi-input\"},\n    \"captchaImage\": {\"css\": \"img.code-img\"},\n    \"captchaRefresh\": {\"css\": \"i.sdi-icon-ios-refresh\"},\n    \"loginButton\": {\"css\": \"button.login-btn\"}\n  },\n  \"postLoginAction\": {\n    \"type\": \"click_app_card\",\n    \"targetText\": \"智游宝数智平台\"\n  },\n  \"options\": {\n    \"maxRetries\": 3,\n    \"showProgress\": true,\n    \"skipPostAction\": false,\n    \"ocrConfig\": {\n      \"url\": \"https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize\",\n      \"headers\": {\n        \"Cookie\": \"tr_de_id=ZHYtSEhOUlZRWkVXSFZI\"\n      }\n    }\n  }\n}", "description": "🔧 登录配置 (对象格式，更稳定)", "id": "1rYA", "name": "loginConfig", "placeholder": "Universal Login Configuration", "type": "json"}], "preferParamsInTab": false}, "label": "trigger"}, {"id": "config-processor", "type": "BlockBasic", "initialized": false, "position": {"x": 350, "y": 80}, "data": {"code": "try {\n  console.log('🔧 === 配置处理开始 ===');\n  const rawConfig = automaRefData('variables', 'loginConfig') || {};\n  // 验证\n  if (!rawConfig.loginUrl || !rawConfig.redirectUrl || !rawConfig.credentials?.username) {\n    throw new Error('配置不完整: 缺少 loginUrl, redirectUrl 或 credentials');\n  }\n  automaSetVariable('runtimeConfig', rawConfig);\n  console.log('✅ 配置处理完成');\n} catch (error) {\n  console.error('❌ 配置处理失败:', error);\n  automaSetVariable('configError', error.message);\n} finally {\n  automaNextBlock();\n}", "context": "background", "description": "⚙️ 基础配置处理器", "disableBlock": false}, "label": "javascript-code"}, {"id": "open-target-page", "type": "BlockBasic", "initialized": false, "position": {"x": 610, "y": 80}, "label": "new-tab", "data": {"label": "尝试访问目标页", "description": "🌐 优先打开最终目标页，用于检查登录状态", "url": "{{variables.runtimeConfig.redirectUrl}}", "active": true, "waitTabLoaded": true, "inGroup": false, "settings": {"blockTimeout": 20000}}}, {"id": "smart-login-script", "type": "BlockBasic", "initialized": false, "position": {"x": 890, "y": 80}, "label": "javascript-code", "data": {"label": "智能登录与后续动作整合脚本", "description": "整合了登录检查、核心登录引擎与后续动作的全功能脚本", "code": "// === 智能登录与后续动作整合脚本 v2.1 ===\n// 功能：\n// 1. 智能检查登录状态，已登录则跳过\n// 2. 内置循环实现登录重试\n// 3. 登录成功后自动执行后续动作\n\n(async () => {\n    // --- 1. 初始化与配置加载 ---\n    const config = automaRefData('variables', 'runtimeConfig') || {};\n    const loginUrlIdentifier = new URL(config.loginUrl).pathname; // 获取登录页的路径作为标识，例如 /usercenter/login\n\n    console.log('🚀 === 智能登录流程启动 ===');\n    console.log(`🔧 登录页标识: ${loginUrlIdentifier}`);\n\n    // --- 核心辅助函数 (提前定义) ---\n    const showProgress = (title, message) => {\n        let div = document.getElementById('universal-progress');\n        if (!div) {\n            div = document.createElement('div');\n            div.id = 'universal-progress';\n            div.style.cssText = `\n              position: fixed; top: 20px; right: 20px;\n              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n              color: white; padding: 15px; border-radius: 8px;\n              z-index: 99999; font-size: 14px;\n              box-shadow: 0 4px 15px rgba(0,0,0,0.3);\n              max-width: 300px; transition: opacity 0.5s;\n            `;\n            document.body.appendChild(div);\n        }\n        div.innerHTML = `\n            <div style=\"font-weight: bold; margin-bottom: 5px;\">🤖 ${title}</div>\n            <div style=\"font-size: 12px; opacity: 0.9;\">${message}</div>\n        `;\n    };\n\n    const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));\n    \n    const findElementEnhanced = (selectorConfig) => {\n        if (!selectorConfig) return null;\n        if (typeof selectorConfig === 'object' && selectorConfig.xpath) {\n            return document.evaluate(selectorConfig.xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;\n        }\n        if (typeof selectorConfig === 'object' && selectorConfig.css) {\n            return document.querySelector(selectorConfig.css);\n        }\n        return null;\n    };\n\n    try {\n        // --- 2. 检查当前是否在登录页 ---\n        if (!window.location.pathname.includes(loginUrlIdentifier)) {\n            console.log('✅ 已处于登录状态，无需执行登录操作。');\n            showProgress('状态检查', '您已登录，流程结束。');\n            await sleep(2500); // 等待用户看到提示\n            const progressDiv = document.getElementById('universal-progress');\n            if (progressDiv) progressDiv.style.opacity = '0';\n            return; // 直接结束脚本\n        }\n\n        console.log('ℹ️ 检测到当前在登录页，开始执行登录程序...');\n        \n        // --- 3. 核心登录逻辑 (内置重试循环) ---\n        let loginSuccess = false;\n        let currentRetry = 0;\n        const maxRetries = config.options?.maxRetries || 3;\n\n        while (currentRetry < maxRetries && !loginSuccess) {\n            currentRetry++;\n            console.log(`🔄 === 登录尝试 第 ${currentRetry} / ${maxRetries} 次 ===`);\n            showProgress(`登录尝试 ${currentRetry}/${maxRetries}`, '正在识别验证码...');\n\n            try {\n                // 如果不是第一次尝试，则刷新验证码\n                if (currentRetry > 1) {\n                    const refreshButton = findElementEnhanced(config.selectors.captchaRefresh);\n                    if (refreshButton) {\n                        refreshButton.click();\n                        console.log('🔄 验证码已刷新');\n                        await sleep(1500); // 等待新验证码图片加载\n                    }\n                }\n\n                // 识别验证码\n                const captchaImage = findElementEnhanced(config.selectors.captchaImage);\n                if (!captchaImage) throw new Error('未找到验证码图片');\n                \n                const response = await fetch(captchaImage.src);\n                const blob = await response.blob();\n                const base64 = await new Promise(resolve => {\n                    const reader = new FileReader();\n                    reader.onloadend = () => resolve(reader.result);\n                    reader.readAsDataURL(blob);\n                });\n\n                showProgress(`登录尝试 ${currentRetry}/${maxRetries}`, '正在请求OCR服务...');\n                const ocrResult = await automaFetch('json', {\n                    url: config.options.ocrConfig.url,\n                    method: 'POST',\n                    headers: Object.assign({ 'Content-Type': 'application/json' }, config.options.ocrConfig.headers || {}),\n                    body: JSON.stringify({ imageData: base64 })\n                });\n\n                if (!ocrResult?.success || !ocrResult.data?.text) {\n                    throw new Error('OCR识别失败或返回无效结果');\n                }\n                const captchaCode = ocrResult.data.text.trim();\n                console.log(`✅ 验证码识别结果: ${captchaCode}`);\n                showProgress(`登录尝试 ${currentRetry}/${maxRetries}`, `验证码: ${captchaCode}，正在填充表单...`);\n\n                // 填充表单\n                const fields = {\n                    username: config.credentials.username,\n                    password: config.credentials.password,\n                    captcha: captchaCode\n                };\n                for (const [key, value] of Object.entries(fields)) {\n                    const element = findElementEnhanced(config.selectors[key]);\n                    if (element) {\n                        element.value = ''; // **关键：先清空**\n                        element.value = value;\n                        element.dispatchEvent(new Event('input', { bubbles: true }));\n                        element.dispatchEvent(new Event('change', { bubbles: true }));\n                        console.log(`📝 字段 [${key}] 填充完成`);\n                    }\n                }\n                \n                await sleep(500);\n\n                // 点击登录按钮\n                const loginButton = findElementEnhanced(config.selectors.loginButton);\n                if (!loginButton) throw new Error('未找到登录按钮');\n                loginButton.click();\n                console.log('✅ 登录按钮已点击，等待跳转...');\n                showProgress(`登录尝试 ${currentRetry}/${maxRetries}`, '已点击登录，正在验证结果...');\n\n                // 检查登录结果\n                await sleep(4000); // 等待页面跳转或响应\n                if (!window.location.pathname.includes(loginUrlIdentifier)) {\n                    console.log('🎉 登录成功！页面已跳转。');\n                    loginSuccess = true;\n                    showProgress('登录成功', '准备执行后续动作...');\n                } else {\n                    const errorElement = document.querySelector('.error-message, [class*=\"error\"], .ant-message-error');\n                    const errorText = errorElement ? errorElement.textContent.trim() : '仍在登录页,可能是验证码错误';\n                    console.log(`❌ 登录失败: ${errorText}`);\n                    if(currentRetry >= maxRetries){\n                         throw new Error(`登录最终失败: ${errorText}`);\n                    }\n                }\n\n            } catch (error) {\n                console.error(`❌ 第 ${currentRetry} 次尝试出错:`, error);\n                if (currentRetry >= maxRetries) {\n                    showProgress('登录失败', `已达最大重试次数: ${error.message}`);\n                    throw error; // 抛出最终错误，终止流程\n                }\n                 showProgress('尝试失败', `原因: ${error.message}，准备重试...`);\n                await sleep(2000);\n            }\n        }\n        \n        // --- 4. 登录成功后的后续动作 ---\n        if (loginSuccess) {\n            const postAction = config.postLoginAction || {};\n            if (config.options?.skipPostAction || !postAction.type || postAction.type === 'none') {\n                console.log('⏩ 跳过后续动作');\n                return;\n            }\n\n            console.log(`🎯 开始执行登录后动作: ${postAction.type}`);\n            showProgress('执行后续动作', `类型: ${postAction.type}`);\n\n            switch (postAction.type) {\n                case 'click_app_card':\n                    const targetText = postAction.targetText;\n                    if (!targetText) throw new Error('未配置目标应用名称');\n                    \n                    // 等待应用列表加载\n                    let appElement = null;\n                    for (let i = 0; i < 10; i++) { // 最多等待10秒\n                        const appCards = document.querySelectorAll('div.my-app__item');\n                        for(const card of appCards){\n                            if(card.textContent.includes(targetText)){\n                                appElement = card;\n                                break;\n                            }\n                        }\n                        if (appElement) break;\n                        await sleep(1000);\n                    }\n\n                    if (appElement) {\n                        const clickable = appElement.querySelector('.my-app__item-footer') || appElement;\n                        clickable.click();\n                        console.log(`✅ 已点击应用: ${targetText}`);\n                        showProgress('操作成功', `已点击应用: ${targetText}`);\n                    } else {\n                        throw new Error(`未找到应用卡片: ${targetText}`);\n                    }\n                    break;\n                // 在此可以添加更多 case，如 click_menu_item 等\n            }\n        }\n\n    } catch (finalError) {\n        console.error('💥 工作流执行时发生致命错误:', finalError);\n        showProgress('执行失败', `发生致命错误: ${finalError.message}`);\n    } finally {\n        console.log('🏁 === 智能登录流程结束 ===');\n        automaNextBlock();\n    }\n})();", "context": "website", "timeout": 90000, "everyNewTab": false, "runBeforeLoad": false}}], "edges": [{"id": "trigger-to-processor", "type": "custom", "source": "trigger-config", "target": "config-processor", "sourceHandle": "trigger-config-output-1", "targetHandle": "config-processor-input-1", "updatable": true, "selectable": true}, {"id": "processor-to-new-tab", "type": "custom", "source": "config-processor", "target": "open-target-page", "sourceHandle": "config-processor-output-1", "targetHandle": "open-target-page-input-1", "updatable": true, "selectable": true}, {"id": "new-tab-to-script", "type": "custom", "source": "open-target-page", "target": "smart-login-script", "sourceHandle": "open-target-page-output-1", "targetHandle": "smart-login-script-input-1", "updatable": true, "selectable": true}], "position": [-25, -150], "zoom": 1, "viewport": {"x": -25, "y": -150, "zoom": 1}}, "settings": {"publicId": "smart-auto-login", "saveLog": true, "debugMode": false, "restartTimes": 0, "notification": true, "onError": "stop-workflow"}, "globalData": "{\n\t\"workflowType\": \"smart-universal-login\",\n\t\"version\": \"3.0.0\",\n\t\"features\": [\"login-status-check\", \"integrated-engine\", \"universal-config\"]\n}", "description": "🚀 智能检查登录状态，自动完成登录并执行后续动作的通用工作流。整合了登录前状态检查、核心登录引擎与后续动作处理器。", "includedWorkflows": {}}
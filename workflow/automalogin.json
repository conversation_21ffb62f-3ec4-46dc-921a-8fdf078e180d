{"extVersion": "1.29.10", "name": "autologin-ultimate-complete", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"nodes": [{"id": "trigger-ultimate", "type": "BlockBasic", "initialized": false, "position": {"x": 96, "y": 36}, "data": {"disableBlock": false, "description": "🚀 终极自动登录触发器", "type": "manual", "interval": 60, "delay": 2, "date": "", "time": "00:00", "url": "", "shortcut": "", "activeInInput": false, "isUrlRegex": false, "days": [], "contextMenuName": "", "contextTypes": [], "parameters": [{"name": "initParams", "type": "json", "description": "🔧 终极配置参数", "defaultValue": "{\n  \"loginUrl\": \"https://test-aliuser.lotsmall.cn/usercenter/login\",\n  \"username\": \"18766668891\",\n  \"password\": \"123456asd.\",\n  \"targetApp\": \"智游宝数智平台\",\n  \"advanced\": {\n    \"maxRetries\": 3,\n    \"showProgress\": true,\n    \"timeouts\": {\n      \"pageLoad\": 15000,\n      \"elementWait\": 8000,\n      \"networkRequest\": 20000\n    },\n    \"debug\": {\n      \"verboseLog\": true\n    }\n  }\n}", "placeholder": "Ultimate Configuration", "data": {"required": false}, "id": "ultimate-config"}], "preferParamsInTab": false}, "label": "trigger"}, {"id": "open-login-page", "type": "BlockBasic", "initialized": false, "position": {"x": 400, "y": 36}, "data": {"active": true, "customUserAgent": false, "description": "🌐 打开登录页面", "disableBlock": false, "inGroup": false, "settings": {"blockTimeout": 20000, "debugMode": false}, "tabZoom": 1, "updatePrevTab": false, "url": "{{variables.initParams.loginUrl}}", "userAgent": "", "waitTabLoaded": true}, "label": "new-tab"}, {"id": "retry-loop", "type": "BlockBasic", "initialized": false, "position": {"x": 700, "y": 36}, "data": {"disableBlock": false, "description": "🔄 重试循环(最多3次)", "loopType": "number", "loopData": 3, "maxLoop": 3, "reverseLoop": false, "breakPoint": false}, "label": "loop-data"}, {"id": "ultimate-login-core", "type": "BlockBasic", "initialized": false, "position": {"x": 1000, "y": 36}, "data": {"disableBlock": false, "description": "🧠 终极登录核心引擎", "timeout": 40000, "context": "website", "code": "// === 终极登录核心引擎 ===\n(async () => {\n  try {\n    const config = automaRefData('variables', 'initParams') || {};\n    const advanced = config.advanced || {};\n    const currentLoop = automaRefData('loopData', '$index') || 0;\n    \n    console.log(`🚀 === 第 ${currentLoop + 1} 次登录尝试 ===`);\n    \n    // 显示进度\n    if (advanced.showProgress) {\n      showProgress(`登录尝试 ${currentLoop + 1}/3`, '正在准备登录...');\n    }\n    \n    // 等待页面加载\n    await waitForPageReady();\n    \n    // 如果是重试，刷新验证码\n    if (currentLoop > 0) {\n      await refreshCaptcha();\n    }\n    \n    // 智能表单检测\n    const selectors = await detectFormFields();\n    console.log('✅ 表单字段检测完成:', selectors);\n    \n    // 验证码识别\n    const captcha = await recognizeCaptcha();\n    console.log(`✅ 验证码识别: ${captcha}`);\n    \n    // 表单填充\n    await fillForm({\n      username: { selector: selectors.username, value: config.username },\n      password: { selector: selectors.password, value: config.password },\n      captcha: { selector: selectors.captcha, value: captcha }\n    });\n    \n    // 点击登录\n    await clickLogin();\n    \n    // 检测登录结果\n    const success = await checkLoginResult();\n    \n    if (success) {\n      console.log('🎉 登录成功！');\n      automaSetVariable('loginSuccess', true);\n      if (advanced.showProgress) {\n        showProgress('登录成功', '正在跳转...');\n      }\n    } else {\n      throw new Error('登录失败');\n    }\n    \n    // === 核心函数 ===\n    \n    async function waitForPageReady() {\n      const timeout = advanced.timeouts?.pageLoad || 15000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        if (document.querySelector('.sdi-form-item-content, .form-warper')) {\n          await sleep(1000); // 等待完全渲染\n          return;\n        }\n        await sleep(200);\n      }\n      throw new Error('页面加载超时');\n    }\n    \n    async function detectFormFields() {\n      const strategies = {\n        username: [\n          '.sdi-form-item-content > .sdi-input-type-text > .sdi-input',\n          'input[type=\"text\"]', 'input[name*=\"user\"]'\n        ],\n        password: [\n          '.sdi-input-type-password > .sdi-input',\n          'input[type=\"password\"]'\n        ],\n        captcha: [\n          '.checkcode-warper .sdi-input',\n          'input[name*=\"captcha\"]', 'input[placeholder*=\"验证码\"]'\n        ]\n      };\n      \n      const result = {};\n      for (const [field, selectors] of Object.entries(strategies)) {\n        for (const selector of selectors) {\n          if (document.querySelector(selector)) {\n            result[field] = selector;\n            break;\n          }\n        }\n      }\n      return result;\n    }\n    \n    async function recognizeCaptcha() {\n      const img = document.querySelector('img.code-img');\n      if (!img) throw new Error('验证码图片未找到');\n      \n      const response = await fetch(img.src);\n      const blob = await response.blob();\n      const base64 = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsDataURL(blob);\n      });\n      \n      const ocr = await automaFetch('json', {\n        url: 'https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize',\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Cookie': 'tr_de_id=ZHYtSEhOUlZRWkVXSFZI'\n        },\n        body: JSON.stringify({ imageData: base64 })\n      });\n      \n      if (!ocr?.success) throw new Error('OCR识别失败');\n      return ocr.data.text.trim();\n    }\n    \n    async function fillForm(fields) {\n      for (const [name, {selector, value}] of Object.entries(fields)) {\n        const element = document.querySelector(selector);\n        if (!element) continue;\n        \n        element.focus();\n        element.value = '';\n        element.value = value;\n        \n        ['input', 'change', 'blur'].forEach(event => {\n          element.dispatchEvent(new Event(event, { bubbles: true }));\n        });\n        \n        console.log(`✅ ${name}填充完成`);\n        await sleep(300);\n      }\n    }\n    \n    async function clickLogin() {\n      const btn = document.querySelector('button.login-btn, button[type=\"submit\"]');\n      if (!btn) throw new Error('登录按钮未找到');\n      \n      btn.scrollIntoView({ block: 'center' });\n      await sleep(500);\n      btn.click();\n      console.log('✅ 登录按钮已点击');\n    }\n    \n    async function checkLoginResult() {\n      await sleep(3000);\n      \n      // 检查URL变化\n      if (window.location.href.includes('/worktable') || \n          window.location.href.includes('/personal')) {\n        return true;\n      }\n      \n      // 检查页面内容\n      const text = document.body.textContent || '';\n      if (text.includes('我的应用') || text.includes('工作台')) {\n        return true;\n      }\n      \n      // 检查错误提示\n      const errors = document.querySelectorAll('.error-message, [class*=\"error\"]');\n      for (const error of errors) {\n        if (error.textContent.trim()) {\n          throw new Error('登录错误: ' + error.textContent.trim());\n        }\n      }\n      \n      return false;\n    }\n    \n    async function refreshCaptcha() {\n      const refresh = document.querySelector('i.sdi-icon-ios-refresh');\n      if (refresh) {\n        refresh.click();\n        await sleep(2000);\n        console.log('✅ 验证码已刷新');\n      }\n    }\n    \n    async function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n    function showProgress(title, message) {\n      let div = document.getElementById('ultimate-progress');\n      if (!div) {\n        div = document.createElement('div');\n        div.id = 'ultimate-progress';\n        div.style.cssText = `\n          position: fixed; top: 20px; right: 20px;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white; padding: 15px; border-radius: 8px;\n          z-index: 99999; font-size: 14px;\n          box-shadow: 0 4px 15px rgba(0,0,0,0.3);\n        `;\n        document.body.appendChild(div);\n      }\n      div.innerHTML = `<div><strong>🤖 ${title}</strong></div><div style=\"font-size:12px;opacity:0.9;margin-top:5px;\">${message}</div>`;\n    }\n    \n  } catch (error) {\n    console.error(`❌ 第 ${currentLoop + 1} 次尝试失败:`, error);\n    \n    const maxRetries = advanced.maxRetries || 3;\n    if (currentLoop >= maxRetries - 1) {\n      console.error('💥 所有尝试都失败了');\n      automaSetVariable('loginFinalError', error.message);\n      automaSetVariable('breakLoop', true);\n    }\n  } finally {\n    automaNextBlock();\n  }\n})();", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "success-check", "type": "BlockBasic", "initialized": false, "position": {"x": 1300, "y": 36}, "data": {"disableBlock": false, "description": "✅ 成功检查", "conditions": [{"type": "javascript", "value": "return automaRefData('variables', 'loginSuccess') === true || automaRefData('variables', 'breakLoop') === true;"}], "fallback": false}, "label": "conditions"}, {"id": "loop-end", "type": "BlockBasic", "initialized": false, "position": {"x": 1000, "y": 200}, "data": {"disableBlock": false, "description": "🔄 循环结束"}, "label": "loop-breakpoint"}, {"id": "goto-worktable", "type": "BlockBasic", "initialized": false, "position": {"x": 1600, "y": 36}, "data": {"disableBlock": false, "description": "🎯 跳转工作台", "url": "https://test-aliuser.lotsmall.cn/usercenter/personal/worktable", "userAgent": "", "active": true, "tabZoom": 1, "inGroup": false, "waitTabLoaded": true, "updatePrevTab": false, "customUserAgent": false}, "label": "new-tab"}, {"id": "find-target-app", "type": "BlockBasic", "initialized": false, "position": {"x": 1900, "y": 36}, "data": {"disableBlock": false, "description": "🔍 智能应用查找", "timeout": 15000, "context": "website", "code": "// === 智能应用查找器 ===\n(async () => {\n  try {\n    const config = automaRefData('variables', 'initParams') || {};\n    const targetApp = config.targetApp || '智游宝数智平台';\n    \n    console.log(`🔍 查找目标应用: ${targetApp}`);\n    \n    // 等待应用列表加载\n    await waitForApps();\n    \n    // 查找目标应用\n    const appData = findTargetApp(targetApp);\n    \n    if (!appData.found) {\n      throw new Error(`应用未找到: ${targetApp}`);\n    }\n    \n    console.log('✅ 应用找到:', appData);\n    \n    // 生成点击选择器\n    const selector = `.my-app__item:nth-child(${appData.position}) .my-app__item-footer`;\n    automaSetVariable('dynamicClickSelector', selector);\n    \n    console.log(`🎯 生成选择器: ${selector}`);\n    \n    async function waitForApps() {\n      const timeout = 10000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        const apps = document.querySelectorAll('div.my-app__item');\n        if (apps.length > 0) {\n          console.log(`✅ 找到 ${apps.length} 个应用`);\n          await sleep(1000); // 等待完全加载\n          return;\n        }\n        await sleep(500);\n      }\n      throw new Error('应用列表加载超时');\n    }\n    \n    function findTargetApp(target) {\n      const apps = document.querySelectorAll('div.my-app__item');\n      \n      for (let i = 0; i < apps.length; i++) {\n        const text = apps[i].textContent || '';\n        const cleanText = text.replace(/\\s+/g, ' ').trim();\n        \n        console.log(`检查应用 ${i + 1}: ${cleanText}`);\n        \n        if (cleanText.includes(target)) {\n          return {\n            found: true,\n            position: i + 1,\n            element: apps[i],\n            text: cleanText\n          };\n        }\n      }\n      \n      // 列出所有可用应用\n      const availableApps = Array.from(apps).map((app, index) => \n        `${index + 1}. ${app.textContent.replace(/\\s+/g, ' ').trim()}`\n      );\n      \n      console.log('可用应用列表:', availableApps);\n      \n      return { found: false, availableApps };\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n  } catch (error) {\n    console.error('❌ 应用查找失败:', error);\n    automaSetVariable('appSearchError', error.message);\n  } finally {\n    automaNextBlock();\n  }\n})();", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "click-target-app", "type": "BlockBasic", "initialized": false, "position": {"x": 2200, "y": 36}, "data": {"disableBlock": false, "description": "🎯 点击目标应用", "findBy": "cssSelector", "waitForSelector": true, "waitSelectorTimeout": 8000, "selector": "{{variables.dynamicClickSelector}}", "markEl": false, "multiple": false}, "label": "event-click"}], "edges": [{"id": "trigger-to-open", "type": "custom", "source": "trigger-ultimate", "target": "open-login-page", "sourceHandle": "trigger-ultimate-output-1", "targetHandle": "open-login-page-input-1", "markerEnd": "arrowclosed"}, {"id": "open-to-loop", "type": "custom", "source": "open-login-page", "target": "retry-loop", "sourceHandle": "open-login-page-output-1", "targetHandle": "retry-loop-input-1", "markerEnd": "arrowclosed"}, {"id": "loop-to-core", "type": "custom", "source": "retry-loop", "target": "ultimate-login-core", "sourceHandle": "retry-loop-output-1", "targetHandle": "ultimate-login-core-input-1", "markerEnd": "arrowclosed"}, {"id": "core-to-check", "type": "custom", "source": "ultimate-login-core", "target": "success-check", "sourceHandle": "ultimate-login-core-output-1", "targetHandle": "success-check-input-1", "markerEnd": "arrowclosed"}, {"id": "check-to-worktable", "type": "custom", "source": "success-check", "target": "goto-worktable", "sourceHandle": "success-check-output-1", "targetHandle": "goto-worktable-input-1", "markerEnd": "arrowclosed"}, {"id": "check-to-loop-end", "type": "custom", "source": "success-check", "target": "loop-end", "sourceHandle": "success-check-output-2", "targetHandle": "loop-end-input-1", "markerEnd": "arrowclosed"}, {"id": "worktable-to-find", "type": "custom", "source": "goto-worktable", "target": "find-target-app", "sourceHandle": "goto-worktable-output-1", "targetHandle": "find-target-app-input-1", "markerEnd": "arrowclosed"}, {"id": "find-to-click", "type": "custom", "source": "find-target-app", "target": "click-target-app", "sourceHandle": "find-target-app-output-1", "targetHandle": "click-target-app-input-1", "markerEnd": "arrowclosed"}], "position": [-200, 50], "zoom": 0.8, "viewport": {"x": -200, "y": 50, "zoom": 0.8}}, "settings": {"publicId": "autologin-ultimate-complete", "blockDelay": 0, "saveLog": true, "debugMode": false, "restartTimes": 3, "notification": true, "execContext": "popup", "reuseLastState": false, "inputAutocomplete": true, "onError": "stop-workflow", "executedBlockOnWeb": false, "insertDefaultColumn": false, "defaultColumnName": "column", "tabLoadTimeout": 30000}, "globalData": "{\n\t\"ultimateWorkflow\": true\n}", "description": "🚀 终极优化版自动登录工作流：智能填充、自动重试、应用跳转一体化解决方案", "includedWorkflows": {}}
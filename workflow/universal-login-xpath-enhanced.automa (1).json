{"extVersion": "1.29.10", "name": "universal-login-xpath-enhanced", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"nodes": [{"id": "trigger-config", "type": "BlockBasic", "initialized": false, "position": {"x": 96, "y": 36}, "data": {"activeInInput": false, "contextMenuName": "", "contextTypes": [], "date": "", "days": [], "delay": 2, "description": "🚀 通用登录配置触发器", "disableBlock": false, "interval": 60, "isUrlRegex": false, "observeElement": {"baseElOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}, "baseSelector": "", "matchPattern": "", "selector": "", "targetOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}}, "parameters": [{"data": {"required": true}, "defaultValue": "{\n  \"loginUrl\": \"https://test-aliuser.lotsmall.cn/usercenter/login\",\n  \"credentials\": {\n    \"username\": \"18766668891\",\n    \"password\": \"123456asd.\"\n  },\n  \"selectorMode\": \"css\",\n  \"selectors\":{\n    \"username\": \".sdi-form-item-content > .sdi-input-type-text > .sdi-input\",\n    \"password\": \".sdi-input-type-password > .sdi-input\",\n    \"captcha\": \".checkcode-warper .sdi-input\",\n    \"captchaImage\": \"img.code-img\",\n    \"captchaRefresh\": \"i.sdi-icon-ios-refresh\",\n    \"loginButton\": \"button.login-btn\"\n  },\n  \"postLoginAction\": {\n    \"type\": \"click_app_card\",\n    \"targetText\": \"智游宝数智平台\"\n  },\n  \"options\": {\n    \"maxRetries\": 3,\n    \"showProgress\": true,\n    \"skipPostAction\": false,\n    \"ocrConfig\": {\n      \"url\": \"https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize\",\n      \"headers\": {\n        \"Cookie\": \"tr_de_id=ZHYtSEhOUlZRWkVXSFZI\"\n      }\n    }\n  }\n}", "description": "🔧 登录配置（支持XPath简化字符串）", "id": "z2LA", "name": "loginConfig", "placeholder": "Universal Login Configuration", "type": "json"}], "preferParamsInTab": false, "shortcut": "", "time": "00:00", "type": "manual", "url": ""}, "label": "trigger"}, {"id": "config-processor", "type": "BlockBasic", "initialized": false, "position": {"x": 400, "y": 36}, "data": {"code": "// === 配置处理器：支持XPath简化字符串格式 ===\ntry {\n  console.log('🔧 === 配置处理开始 ===');\n  \n  const rawConfig = automaRefData('variables', 'loginConfig') || {};\n  console.log('原始配置:', rawConfig);\n  \n  // 1. 配置标准化\n  const config = normalizeConfig(rawConfig);\n  console.log('标准化后配置:', config);\n  \n  // 2. 配置验证\n  const validation = validateConfig(config);\n  if (!validation.valid) {\n    throw new Error('配置验证失败: ' + validation.errors.join(', '));\n  }\n  \n  // 3. 设置运行时配置\n  automaSetVariable('runtimeConfig', config);\n  automaSetVariable('currentRetry', 0);\n  automaSetVariable('loginSuccess', false);\n  \n  console.log('✅ 配置处理完成');\n  \n  // === 核心函数 ===\n  \n  function normalizeConfig(rawConfig) {\n    const config = JSON.parse(JSON.stringify(rawConfig));\n    \n    // 标准化选择器格式\n    if (config.selectors && config.selectorMode) {\n      config.selectors = normalizeSelectors(config.selectors, config.selectorMode);\n      console.log('✅ 选择器标准化完成');\n    }\n    \n    // 设置默认值\n    config.options = Object.assign({\n      maxRetries: 3,\n      showProgress: true,\n      skipPostAction: false\n    }, config.options || {});\n    \n    return config;\n  }\n  \n  function normalizeSelectors(selectors, selectorMode) {\n    const normalized = {};\n    \n    for (const [key, value] of Object.entries(selectors)) {\n      if (typeof value === 'string') {\n        // 简化字符串格式自动转换\n        if (selectorMode === 'xpath') {\n          normalized[key] = { xpath: value };\n          console.log(`📝 XPath字符串转换: ${key} -> ${value}`);\n        } else {\n          normalized[key] = { css: value };\n          console.log(`📝 CSS字符串转换: ${key} -> ${value}`);\n        }\n      } else if (typeof value === 'object' && value !== null) {\n        // 对象格式直接使用\n        normalized[key] = value;\n        console.log(`📋 对象格式保留: ${key}`);\n      } else {\n        console.warn(`⚠️ 跳过无效选择器: ${key} = ${value}`);\n      }\n    }\n    \n    return normalized;\n  }\n  \n  function validateConfig(config) {\n    const errors = [];\n    \n    // 必需字段检查\n    if (!config.loginUrl) errors.push('缺少loginUrl');\n    if (!config.credentials?.username) errors.push('缺少用户名');\n    if (!config.credentials?.password) errors.push('缺少密码');\n    if (!config.selectors) errors.push('缺少选择器配置');\n    \n    // 选择器验证\n    const requiredSelectors = ['username', 'password', 'loginButton'];\n    for (const selector of requiredSelectors) {\n      if (!config.selectors[selector]) {\n        errors.push(`缺少${selector}选择器`);\n      }\n    }\n    \n    // XPath格式验证\n    if (config.selectorMode === 'xpath') {\n      for (const [key, selectorConfig] of Object.entries(config.selectors)) {\n        if (selectorConfig.xpath && !selectorConfig.xpath.startsWith('/')) {\n          errors.push(`${key}的XPath格式错误，应以/开头`);\n        }\n      }\n    }\n    \n    return {\n      valid: errors.length === 0,\n      errors: errors\n    };\n  }\n  \n} catch (error) {\n  console.error('❌ 配置处理失败:', error);\n  automaSetVariable('configError', error.message);\n} finally {\n  automaNextBlock();\n}", "context": "background", "description": "⚙️ 配置处理器（XPath简化支持）", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "timeout": 5000}, "label": "javascript-code"}, {"id": "open-login-page", "type": "BlockBasic", "initialized": false, "position": {"x": 700, "y": 36}, "data": {"active": true, "customUserAgent": false, "description": "🌐 打开登录页面", "disableBlock": false, "inGroup": false, "settings": {"blockTimeout": 20000, "debugMode": false}, "tabZoom": 1, "updatePrevTab": false, "url": "{{variables.runtimeConfig.loginUrl}}", "userAgent": "", "waitTabLoaded": true}, "label": "new-tab"}, {"id": "enhanced-login-engine", "type": "BlockBasic", "initialized": false, "position": {"x": 1000, "y": 36}, "data": {"code": "// === 增强登录引擎：支持XPath简化字符串 ===\n(async () => {\n  try {\n    const config = automaRefData('variables', 'runtimeConfig') || {};\n    let currentRetry = automaRefData('variables', 'currentRetry') || 0;\n    const maxRetries = config.options?.maxRetries || 3;\n    \n    console.log(`🚀 === 登录尝试 ${currentRetry + 1}/${maxRetries} ===`);\n    \n    // 显示进度\n    if (config.options?.showProgress) {\n      showProgress(`登录尝试 ${currentRetry + 1}/${maxRetries}`, '正在准备登录...');\n    }\n    \n    // 等待页面准备\n    await waitForPageReady();\n    \n    // 刷新验证码（重试时）\n    if (currentRetry > 0) {\n      await refreshCaptcha();\n    }\n    \n    // 识别验证码\n    const captcha = await recognizeCaptcha();\n    console.log(`✅ 验证码识别: ${captcha}`);\n    \n    // 填充表单\n    await fillLoginForm({\n      username: config.credentials.username,\n      password: config.credentials.password,\n      captcha: captcha\n    });\n    \n    // 点击登录\n    await clickLoginButton();\n    \n    // 检查登录结果\n    const result = await checkLoginResult();\n    \n    if (result.success) {\n      console.log('🎉 登录成功！');\n      automaSetVariable('loginSuccess', true);\n      if (config.options?.showProgress) {\n        showProgress('登录成功', '准备执行后续动作...');\n      }\n    } else {\n      // 登录失败处理\n      currentRetry++;\n      automaSetVariable('currentRetry', currentRetry);\n      \n      if (currentRetry < maxRetries) {\n        console.log(`❌ 登录失败: ${result.error}`);\n        console.log(`🔄 准备第 ${currentRetry + 1} 次重试...`);\n        automaSetVariable('shouldRetry', true);\n        \n        if (config.options?.showProgress) {\n          showProgress('登录失败', `准备重试 ${currentRetry + 1}/${maxRetries}`);\n        }\n      } else {\n        console.error(`💥 登录最终失败，已尝试 ${maxRetries} 次`);\n        automaSetVariable('loginSuccess', false);\n        automaSetVariable('finalError', result.error);\n      }\n    }\n    \n    // === 核心功能函数 ===\n    \n    async function waitForPageReady() {\n      const timeout = 15000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        if (document.querySelector('.sdi-form-item-content, .form-warper, form')) {\n          await sleep(1000);\n          return;\n        }\n        await sleep(200);\n      }\n      throw new Error('页面加载超时');\n    }\n    \n    async function recognizeCaptcha() {\n      const captchaImageConfig = config.selectors?.captchaImage;\n      if (!captchaImageConfig) {\n        console.log('⚠️ 未配置验证码图片选择器，跳过识别');\n        return '';\n      }\n      \n      const img = await findElementEnhanced(captchaImageConfig);\n      if (!img) throw new Error('验证码图片未找到');\n      \n      // 转换为Base64\n      const response = await fetch(img.src);\n      const blob = await response.blob();\n      const base64 = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsDataURL(blob);\n      });\n      \n      // 调用OCR\n      const ocrConfig = config.options?.ocrConfig || {};\n      const ocr = await automaFetch('json', {\n        url: ocrConfig.url || 'https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize',\n        method: 'POST',\n        headers: Object.assign({\n          'Content-Type': 'application/json'\n        }, ocrConfig.headers || {}),\n        body: JSON.stringify({ imageData: base64 })\n      });\n      \n      if (!ocr?.success || !ocr.data?.text) {\n        throw new Error('OCR识别失败');\n      }\n      \n      return ocr.data.text.trim();\n    }\n    \n    async function fillLoginForm(data) {\n      const fieldMappings = {\n        username: data.username,\n        password: data.password,\n        captcha: data.captcha\n      };\n      \n      for (const [fieldName, value] of Object.entries(fieldMappings)) {\n        if (!value || !config.selectors[fieldName]) continue;\n        \n        const element = await findElementEnhanced(config.selectors[fieldName]);\n        if (!element) {\n          console.warn(`⚠️ ${fieldName}字段未找到，跳过`);\n          continue;\n        }\n        \n        // 聚焦并填充\n        element.focus();\n        element.click();\n        await sleep(200);\n        \n        element.value = '';\n        element.value = value;\n        \n        // 触发事件\n        ['input', 'change', 'blur'].forEach(eventType => {\n          element.dispatchEvent(new Event(eventType, { bubbles: true }));\n        });\n        \n        console.log(`✅ ${fieldName}填充完成`);\n        await sleep(300);\n      }\n    }\n    \n    async function clickLoginButton() {\n      const buttonConfig = config.selectors?.loginButton;\n      if (!buttonConfig) throw new Error('未配置登录按钮选择器');\n      \n      const button = await findElementEnhanced(buttonConfig);\n      if (!button) throw new Error('登录按钮未找到');\n      \n      button.scrollIntoView({ block: 'center' });\n      await sleep(500);\n      button.click();\n      console.log('✅ 登录按钮已点击');\n    }\n    \n    async function checkLoginResult() {\n      await sleep(3000);\n      \n      // 检查URL变化\n      if (window.location.href.includes('/worktable') || \n          window.location.href.includes('/personal') ||\n          window.location.href.includes('/dashboard')) {\n        return { success: true };\n      }\n      \n      // 检查页面内容\n      const pageText = document.body.textContent || '';\n      if (pageText.includes('我的应用') || \n          pageText.includes('工作台') || \n          pageText.includes('欢迎')) {\n        return { success: true };\n      }\n      \n      // 检查错误信息\n      const errorSelectors = ['.error-message', '[class*=\"error\"]', '.ant-message-error'];\n      for (const selector of errorSelectors) {\n        const errorEl = document.querySelector(selector);\n        if (errorEl && errorEl.textContent.trim()) {\n          return { success: false, error: errorEl.textContent.trim() };\n        }\n      }\n      \n      // 检查是否还在登录页面\n      const loginButtonConfig = config.selectors?.loginButton;\n      if (loginButtonConfig && findElementEnhanced(loginButtonConfig)) {\n        return { success: false, error: '仍在登录页面，可能是验证码错误' };\n      }\n      \n      return { success: false, error: '未知登录状态' };\n    }\n    \n    async function refreshCaptcha() {\n      const refreshSelectors = [\n        'i.sdi-icon-ios-refresh',\n        '.captcha-refresh',\n        '[class*=\"refresh\"]'\n      ];\n      \n      for (const selector of refreshSelectors) {\n        const refresh = document.querySelector(selector);\n        if (refresh) {\n          refresh.click();\n          await sleep(2000);\n          console.log('✅ 验证码已刷新');\n          return;\n        }\n      }\n    }\n    \n    // === 增强的元素查找函数 ===\n    function findElementEnhanced(selectorConfig) {\n      if (!selectorConfig) return null;\n      \n      // 支持直接传入字符串（自动判断类型）\n      if (typeof selectorConfig === 'string') {\n        if (selectorConfig.startsWith('//') || selectorConfig.startsWith('/')) {\n          return getElementByXPath(selectorConfig);\n        } else {\n          return document.querySelector(selectorConfig);\n        }\n      }\n      \n      // 对象格式处理\n      if (typeof selectorConfig === 'object') {\n        // 优先使用xpath\n        if (selectorConfig.xpath) {\n          const element = getElementByXPath(selectorConfig.xpath);\n          if (element) return element;\n        }\n        \n        // 回退到css\n        if (selectorConfig.css) {\n          try {\n            const element = document.querySelector(selectorConfig.css);\n            if (element) return element;\n          } catch (error) {\n            console.warn('CSS选择器错误:', selectorConfig.css, error);\n          }\n        }\n        \n        // 其他备用选择器\n        if (selectorConfig.id) {\n          return document.getElementById(selectorConfig.id);\n        }\n      }\n      \n      return null;\n    }\n    \n    function getElementByXPath(xpath) {\n      try {\n        const result = document.evaluate(\n          xpath, \n          document, \n          null, \n          XPathResult.FIRST_ORDERED_NODE_TYPE, \n          null\n        );\n        return result.singleNodeValue;\n      } catch (error) {\n        console.error('XPath执行错误:', xpath, error);\n        return null;\n      }\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n    function showProgress(title, message) {\n      let div = document.getElementById('universal-progress');\n      if (!div) {\n        div = document.createElement('div');\n        div.id = 'universal-progress';\n        div.style.cssText = `\n          position: fixed; top: 20px; right: 20px;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white; padding: 15px; border-radius: 8px;\n          z-index: 99999; font-size: 14px;\n          box-shadow: 0 4px 15px rgba(0,0,0,0.3);\n          max-width: 300px;\n        `;\n        document.body.appendChild(div);\n      }\n      div.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 5px;\">🤖 ${title}</div>\n        <div style=\"font-size: 12px; opacity: 0.9;\">${message}</div>\n      `;\n    }\n    \n  } catch (error) {\n    console.error('❌ 登录引擎执行失败:', error);\n    \n    const currentRetry = automaRefData('variables', 'currentRetry') || 0;\n    const maxRetries = (automaRefData('variables', 'runtimeConfig') || {}).options?.maxRetries || 3;\n    \n    if (currentRetry < maxRetries - 1) {\n      automaSetVariable('shouldRetry', true);\n    } else {\n      automaSetVariable('loginSuccess', false);\n      automaSetVariable('finalError', error.message);\n    }\n  } finally {\n    automaNextBlock();\n  }\n})();", "context": "website", "description": "🧠 增强登录引擎（XPath智能支持）", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "timeout": 40000}, "label": "javascript-code"}, {"id": "post-login-handler", "type": "BlockBasic", "initialized": false, "position": {"x": 1370.5474508023758, "y": 29.402460446135507}, "data": {"code": "// === 登录后动作处理器 ===\n(async () => {\n  try {\n    const config = automaRefData('variables', 'runtimeConfig') || {};\n    const postAction = config.postLoginAction || {};\n    \n    if (config.options?.skipPostAction || postAction.type === 'none') {\n      console.log('⏩ 跳过后续动作');\n      return;\n    }\n    \n    console.log('🎯 执行登录后动作:', postAction.type);\n    \n    switch (postAction.type) {\n      case 'direct_redirect':\n        await handleDirectRedirect(postAction);\n        break;\n      case 'click_app_card':\n        await handleClickAppCard(postAction);\n        break;\n      case 'click_menu_item':\n        await handleClickMenuItem(postAction);\n        break;\n      case 'custom_click':\n        await handleCustomClick(postAction);\n        break;\n      default:\n        console.log('ℹ️ 无需执行后续动作');\n    }\n    \n    // === 动作处理函数 ===\n    \n    async function handleDirectRedirect(action) {\n      if (action.redirectUrl) {\n        console.log(`🔗 直接跳转: ${action.redirectUrl}`);\n        window.location.href = action.redirectUrl;\n      }\n    }\n    \n    async function handleClickAppCard(action) {\n      const targetText = action.targetText;\n      if (!targetText) throw new Error('未指定目标应用名称');\n      \n      console.log(`📱 查找应用卡片: ${targetText}`);\n      \n      // 等待应用列表加载\n      await waitForApps();\n      \n      // 查找目标应用\n      const appData = findTargetApp(targetText);\n      if (!appData.found) {\n        throw new Error(`应用未找到: ${targetText}`);\n      }\n      \n      // 生成并点击\n      const selector = `.my-app__item:nth-child(${appData.position}) .my-app__item-footer`;\n      const element = document.querySelector(selector);\n      if (element) {\n        element.click();\n        console.log(`✅ 已点击应用: ${targetText}`);\n      } else {\n        throw new Error('应用点击失败');\n      }\n    }\n    \n    async function handleClickMenuItem(action) {\n      const targetText = action.targetText;\n      if (!targetText) throw new Error('未指定目标菜单');\n      \n      console.log(`📋 查找菜单项: ${targetText}`);\n      \n      const menuSelectors = [\n        `a[href*=\"${targetText.toLowerCase()}\"]`,\n        `*[text()*=\"${targetText}\"]`,\n        `.menu-item:contains(\"${targetText}\")`,\n        `[title*=\"${targetText}\"]`\n      ];\n      \n      for (const selector of menuSelectors) {\n        const element = document.querySelector(selector);\n        if (element) {\n          element.click();\n          console.log(`✅ 已点击菜单: ${targetText}`);\n          return;\n        }\n      }\n      \n      throw new Error(`菜单项未找到: ${targetText}`);\n    }\n    \n    async function handleCustomClick(action) {\n      const targetText = action.targetText;\n      const targetUrl = action.targetUrl;\n      \n      if (targetUrl) {\n        console.log(`🔗 跳转到: ${targetUrl}`);\n        window.location.href = targetUrl;\n      } else if (targetText) {\n        console.log(`🎯 查找并点击: ${targetText}`);\n        \n        const element = document.querySelector(`*:contains(\"${targetText}\")`) ||\n                       document.evaluate(`//*[contains(text(), \"${targetText}\")]`, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;\n        \n        if (element) {\n          element.click();\n          console.log(`✅ 已点击: ${targetText}`);\n        } else {\n          throw new Error(`目标元素未找到: ${targetText}`);\n        }\n      }\n    }\n    \n    // === 辅助函数 ===\n    \n    async function waitForApps() {\n      const timeout = 10000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        const apps = document.querySelectorAll('div.my-app__item');\n        if (apps.length > 0) {\n          await sleep(1000);\n          return;\n        }\n        await sleep(500);\n      }\n      throw new Error('应用列表加载超时');\n    }\n    \n    function findTargetApp(target) {\n      const apps = document.querySelectorAll('div.my-app__item');\n      \n      for (let i = 0; i < apps.length; i++) {\n        const text = apps[i].textContent.replace(/\\s+/g, ' ').trim();\n        console.log(`检查应用 ${i + 1}: ${text}`);\n        \n        if (text.includes(target)) {\n          return {\n            found: true,\n            position: i + 1,\n            element: apps[i],\n            text: text\n          };\n        }\n      }\n      \n      return { found: false };\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n  } catch (error) {\n    console.error('❌ 登录后动作执行失败:', error);\n  } finally {\n    automaNextBlock();\n  }\n})();", "context": "website", "description": "🎯 登录后动作处理器", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "timeout": 20000}, "label": "javascript-code"}], "edges": [{"id": "trigger-to-processor", "type": "custom", "source": "trigger-config", "target": "config-processor", "sourceHandle": "trigger-config-output-1", "targetHandle": "config-processor-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 312, "sourceY": 71.99997182992789, "targetX": 376.00003756009613, "targetY": 71.99997182992789}, {"id": "processor-to-open", "type": "custom", "source": "config-processor", "target": "open-login-page", "sourceHandle": "config-processor-output-1", "targetHandle": "open-login-page-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 616, "sourceY": 71.99997182992789, "targetX": 676.0000375600962, "targetY": 71.99997182992789}, {"id": "open-to-engine", "type": "custom", "source": "open-login-page", "target": "enhanced-login-engine", "sourceHandle": "open-login-page-output-1", "targetHandle": "enhanced-login-engine-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 916.0000469501201, "sourceY": 71.99997182992789, "targetX": 975.9999436598557, "targetY": 71.99997182992789}, {"id": "vueflow__edge-enhanced-login-engineenhanced-login-engine-output-1-post-login-handlerpost-login-handler-input-1", "type": "custom", "source": "enhanced-login-engine", "target": "post-login-handler", "sourceHandle": "enhanced-login-engine-output-1", "targetHandle": "post-login-handler-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed", "sourceX": 1216, "sourceY": 71.99997182992789, "targetX": 1346.547488362472, "targetY": 65.40245575112348}], "position": [-297.73734505493485, 142.05221167755735], "zoom": 1.3, "viewport": {"x": -297.73734505493485, "y": 142.05221167755735, "zoom": 1.3}}, "settings": {"publicId": "universal-login-xpath-enhanced", "restartTimes": 3, "notification": true, "tabLoadTimeout": 30000, "inputAutocomplete": true, "insertDefaultColumn": false, "defaultColumnName": "column", "blockDelay": 0, "debugMode": false, "execContext": "popup", "executedBlockOnWeb": false, "onError": "stop-workflow", "reuseLastState": false, "saveLog": true}, "globalData": "{\n\t\"workflowType\": \"universal-login-xpath-enhanced\",\n\t\"version\": \"2.0.0\",\n\t\"features\": [\"xpath-string-support\", \"intelligent-retry\", \"universal-config\"]\n}", "description": "🚀 通用登录工作流（XPath简化增强版）：支持XPath字符串简化格式、智能重试、多种后续动作处理", "includedWorkflows": {}}
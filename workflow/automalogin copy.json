{"extVersion": "1.29.10", "name": "autologin-fixed-ultimate", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"nodes": [{"id": "trigger-start", "type": "BlockBasic", "initialized": false, "position": {"x": 96, "y": 36}, "data": {"disableBlock": false, "description": "🚀 启动触发器", "type": "manual", "interval": 60, "delay": 2, "date": "", "time": "00:00", "url": "", "shortcut": "", "activeInInput": false, "isUrlRegex": false, "days": [], "contextMenuName": "", "contextTypes": [], "parameters": [{"name": "initParams", "type": "json", "description": "🔧 配置参数", "defaultValue": "{\n  \"loginUrl\": \"https://test-aliuser.lotsmall.cn/usercenter/login\",\n  \"username\": \"18766668891\",\n  \"password\": \"123456asd.\",\n  \"targetApp\": \"智游宝数智平台\",\n  \"maxRetries\": 3,\n  \"showProgress\": true\n}", "placeholder": "Configuration", "data": {"required": false}, "id": "config-params"}], "preferParamsInTab": false}, "label": "trigger"}, {"id": "init-state", "type": "BlockBasic", "initialized": false, "position": {"x": 380, "y": 36}, "data": {"disableBlock": false, "description": "🎯 初始化状态", "timeout": 3000, "context": "background", "code": "// 初始化工作流状态\ntry {\n  console.log('🚀 === 终极自动登录系统启动 ===');\n  \n  // 重置重试计数\n  automaSetVariable('loginRetryCount', 0);\n  automaSetVariable('loginSuccess', false);\n  automaSetVariable('shouldRetry', false);\n  automaSetVariable('workflowStartTime', Date.now());\n  \n  console.log('✅ 状态初始化完成');\n  \n} catch (error) {\n  console.error('❌ 初始化失败:', error);\n} finally {\n  automaNextBlock();\n}", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "open-page", "type": "BlockBasic", "initialized": false, "position": {"x": 680, "y": 36}, "data": {"active": true, "customUserAgent": false, "description": "🌐 打开登录页面", "disableBlock": false, "inGroup": false, "settings": {"blockTimeout": 20000, "debugMode": false}, "tabZoom": 1, "updatePrevTab": false, "url": "{{variables.initParams.loginUrl}}", "userAgent": "", "waitTabLoaded": true}, "label": "new-tab"}, {"id": "smart-login-attempt", "type": "BlockBasic", "initialized": false, "position": {"x": 980, "y": 36}, "data": {"disableBlock": false, "description": "🧠 智能登录尝试", "timeout": 35000, "context": "website", "code": "// === 智能登录尝试（包含重试逻辑）===\n(async () => {\n  try {\n    const config = automaRefData('variables', 'initParams') || {};\n    let retryCount = automaRefData('variables', 'loginRetryCount') || 0;\n    const maxRetries = config.maxRetries || 3;\n    \n    console.log(`🚀 === 第 ${retryCount + 1} 次登录尝试 ===`);\n    \n    // 显示进度\n    if (config.showProgress) {\n      showProgress(`登录尝试 ${retryCount + 1}/${maxRetries}`, '正在准备登录...');\n    }\n    \n    // 等待页面准备\n    await waitForPageReady();\n    \n    // 如果是重试，刷新验证码\n    if (retryCount > 0) {\n      await refreshCaptcha();\n      if (config.showProgress) {\n        showProgress(`登录尝试 ${retryCount + 1}/${maxRetries}`, '验证码已刷新');\n      }\n    }\n    \n    // 检测表单字段\n    const selectors = await detectFormFields();\n    console.log('✅ 表单字段检测:', selectors);\n    \n    // 识别验证码\n    if (config.showProgress) {\n      showProgress(`登录尝试 ${retryCount + 1}/${maxRetries}`, '正在识别验证码...');\n    }\n    const captcha = await recognizeCaptcha();\n    console.log(`✅ 验证码识别成功: ${captcha}`);\n    \n    // 填充表单\n    if (config.showProgress) {\n      showProgress(`登录尝试 ${retryCount + 1}/${maxRetries}`, '正在填充表单...');\n    }\n    await fillAllFields({\n      username: { selector: selectors.username, value: config.username },\n      password: { selector: selectors.password, value: config.password },\n      captcha: { selector: selectors.captcha, value: captcha }\n    });\n    \n    // 点击登录\n    if (config.showProgress) {\n      showProgress(`登录尝试 ${retryCount + 1}/${maxRetries}`, '正在提交登录...');\n    }\n    await clickLoginButton();\n    \n    // 检测登录结果\n    const loginResult = await checkLoginResult();\n    \n    if (loginResult.success) {\n      console.log('🎉 登录成功！');\n      automaSetVariable('loginSuccess', true);\n      automaSetVariable('shouldRetry', false);\n      \n      if (config.showProgress) {\n        showProgress('登录成功', '正在跳转到工作台...');\n      }\n      \n    } else {\n      // 登录失败，准备重试\n      retryCount++;\n      automaSetVariable('loginRetryCount', retryCount);\n      \n      if (retryCount < maxRetries) {\n        console.log(`❌ 登录失败: ${loginResult.error}`);\n        console.log(`🔄 准备第 ${retryCount + 1} 次重试...`);\n        automaSetVariable('shouldRetry', true);\n        \n        if (config.showProgress) {\n          showProgress('登录失败', `准备第 ${retryCount + 1} 次重试...`);\n        }\n        \n      } else {\n        console.error(`💥 登录最终失败，已尝试 ${maxRetries} 次`);\n        automaSetVariable('loginSuccess', false);\n        automaSetVariable('shouldRetry', false);\n        automaSetVariable('finalError', `登录失败: ${loginResult.error}`);\n      }\n    }\n    \n    // === 核心功能函数 ===\n    \n    async function waitForPageReady() {\n      const timeout = 15000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        if (document.querySelector('.sdi-form-item-content, .form-warper')) {\n          await sleep(1000);\n          return;\n        }\n        await sleep(200);\n      }\n      throw new Error('页面加载超时');\n    }\n    \n    async function detectFormFields() {\n      const fieldMappings = {\n        username: [\n          '.sdi-form-item-content > .sdi-input-type-text > .sdi-input',\n          'input[type=\"text\"]',\n          'input[name*=\"user\"]'\n        ],\n        password: [\n          '.sdi-input-type-password > .sdi-input',\n          'input[type=\"password\"]'\n        ],\n        captcha: [\n          '.checkcode-warper .sdi-input',\n          'input[name*=\"captcha\"]',\n          'input[placeholder*=\"验证码\"]'\n        ]\n      };\n      \n      const result = {};\n      for (const [field, selectors] of Object.entries(fieldMappings)) {\n        for (const selector of selectors) {\n          const element = document.querySelector(selector);\n          if (element && element.offsetParent !== null) {\n            result[field] = selector;\n            break;\n          }\n        }\n        if (!result[field]) {\n          throw new Error(`${field}字段未找到`);\n        }\n      }\n      return result;\n    }\n    \n    async function recognizeCaptcha() {\n      // 等待验证码图片\n      await waitForElement('img.code-img', 5000);\n      const img = document.querySelector('img.code-img');\n      \n      // 转换为Base64\n      const response = await fetch(img.src);\n      const blob = await response.blob();\n      const base64 = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsDataURL(blob);\n      });\n      \n      // 调用OCR\n      const ocr = await automaFetch('json', {\n        url: 'https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize',\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Cookie': 'tr_de_id=ZHYtSEhOUlZRWkVXSFZI'\n        },\n        body: JSON.stringify({ imageData: base64 })\n      });\n      \n      if (!ocr?.success || !ocr.data?.text) {\n        throw new Error('OCR识别失败');\n      }\n      \n      return ocr.data.text.trim();\n    }\n    \n    async function fillAllFields(fields) {\n      for (const [fieldName, {selector, value}] of Object.entries(fields)) {\n        const element = await waitForElement(selector, 3000);\n        \n        // 聚焦并清空\n        element.focus();\n        element.click();\n        await sleep(200);\n        \n        element.value = '';\n        element.value = value;\n        \n        // 触发事件\n        ['input', 'change', 'blur'].forEach(eventType => {\n          element.dispatchEvent(new Event(eventType, { bubbles: true }));\n        });\n        \n        console.log(`✅ ${fieldName}填充完成: ${value}`);\n        await sleep(300);\n      }\n    }\n    \n    async function clickLoginButton() {\n      const buttonSelectors = [\n        'button.login-btn',\n        'button[type=\"submit\"]',\n        'input[type=\"submit\"]'\n      ];\n      \n      let button = null;\n      for (const selector of buttonSelectors) {\n        button = document.querySelector(selector);\n        if (button && button.offsetParent !== null) break;\n      }\n      \n      if (!button) throw new Error('登录按钮未找到');\n      \n      button.scrollIntoView({ block: 'center' });\n      await sleep(500);\n      button.click();\n      console.log('✅ 登录按钮已点击');\n    }\n    \n    async function checkLoginResult() {\n      // 等待响应\n      await sleep(3000);\n      \n      // 检查URL变化\n      if (window.location.href.includes('/worktable') || \n          window.location.href.includes('/personal')) {\n        return { success: true };\n      }\n      \n      // 检查页面内容\n      const pageText = document.body.textContent || '';\n      if (pageText.includes('我的应用') || pageText.includes('工作台')) {\n        return { success: true };\n      }\n      \n      // 检查错误信息\n      const errorSelectors = ['.error-message', '[class*=\"error\"]', '.ant-message-error'];\n      for (const selector of errorSelectors) {\n        const errorEl = document.querySelector(selector);\n        if (errorEl && errorEl.textContent.trim()) {\n          return { success: false, error: errorEl.textContent.trim() };\n        }\n      }\n      \n      // 如果还在登录页面\n      if (document.querySelector('button.login-btn')) {\n        return { success: false, error: '仍在登录页面，可能是验证码错误' };\n      }\n      \n      return { success: false, error: '未知登录状态' };\n    }\n    \n    async function refreshCaptcha() {\n      const refresh = document.querySelector('i.sdi-icon-ios-refresh');\n      if (refresh) {\n        refresh.click();\n        await sleep(2000);\n        console.log('✅ 验证码已刷新');\n      }\n    }\n    \n    async function waitForElement(selector, timeout = 5000) {\n      const start = Date.now();\n      while (Date.now() - start < timeout) {\n        const element = document.querySelector(selector);\n        if (element && element.offsetParent !== null) return element;\n        await sleep(200);\n      }\n      throw new Error(`元素等待超时: ${selector}`);\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n    function showProgress(title, message) {\n      let div = document.getElementById('login-progress');\n      if (!div) {\n        div = document.createElement('div');\n        div.id = 'login-progress';\n        div.style.cssText = `\n          position: fixed; top: 20px; right: 20px;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white; padding: 15px; border-radius: 8px;\n          z-index: 99999; font-size: 14px;\n          box-shadow: 0 4px 15px rgba(0,0,0,0.3);\n          max-width: 300px;\n        `;\n        document.body.appendChild(div);\n      }\n      div.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 5px;\">🤖 ${title}</div>\n        <div style=\"font-size: 12px; opacity: 0.9;\">${message}</div>\n      `;\n    }\n    \n  } catch (error) {\n    console.error('❌ 登录尝试失败:', error);\n    \n    const retryCount = automaRefData('variables', 'loginRetryCount') || 0;\n    const maxRetries = (automaRefData('variables', 'initParams') || {}).maxRetries || 3;\n    \n    if (retryCount < maxRetries - 1) {\n      automaSetVariable('shouldRetry', true);\n      console.log('🔄 将进行重试...');\n    } else {\n      automaSetVariable('loginSuccess', false);\n      automaSetVariable('shouldRetry', false);\n      console.log('💥 最终失败');\n    }\n    \n  } finally {\n    automaNextBlock();\n  }\n})();", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "retry-check", "type": "BlockBasic", "initialized": false, "position": {"x": 1280, "y": 36}, "data": {"disableBlock": false, "description": "🔄 重试检查", "conditions": [{"type": "javascript", "value": "return automaRefData('variables', 'shouldRetry') === true;"}], "fallback": false}, "label": "conditions"}, {"id": "success-check", "type": "BlockBasic", "initialized": false, "position": {"x": 1580, "y": 36}, "data": {"disableBlock": false, "description": "✅ 成功检查", "conditions": [{"type": "javascript", "value": "return automaRefData('variables', 'loginSuccess') === true;"}], "fallback": true}, "label": "conditions"}, {"id": "goto-worktable", "type": "BlockBasic", "initialized": false, "position": {"x": 1880, "y": 36}, "data": {"disableBlock": false, "description": "🎯 跳转工作台", "url": "https://test-aliuser.lotsmall.cn/usercenter/personal/worktable", "userAgent": "", "active": true, "tabZoom": 1, "inGroup": false, "waitTabLoaded": true, "updatePrevTab": false, "customUserAgent": false}, "label": "new-tab"}, {"id": "find-app", "type": "BlockBasic", "initialized": false, "position": {"x": 2180, "y": 36}, "data": {"disableBlock": false, "description": "🔍 查找目标应用", "timeout": 15000, "context": "website", "code": "// === 查找目标应用 ===\n(async () => {\n  try {\n    const config = automaRefData('variables', 'initParams') || {};\n    const targetApp = config.targetApp || '智游宝数智平台';\n    \n    console.log(`🔍 查找应用: ${targetApp}`);\n    \n    // 等待应用加载\n    await waitForApps();\n    \n    // 查找应用\n    const result = findApp(targetApp);\n    \n    if (!result.found) {\n      throw new Error(`应用未找到: ${targetApp}`);\n    }\n    \n    // 生成选择器\n    const selector = `.my-app__item:nth-child(${result.position}) .my-app__item-footer`;\n    automaSetVariable('dynamicClickSelector', selector);\n    \n    console.log(`✅ 应用已找到，选择器: ${selector}`);\n    \n    async function waitForApps() {\n      const start = Date.now();\n      while (Date.now() - start < 10000) {\n        const apps = document.querySelectorAll('div.my-app__item');\n        if (apps.length > 0) {\n          await sleep(1000);\n          return;\n        }\n        await sleep(500);\n      }\n      throw new Error('应用列表加载超时');\n    }\n    \n    function findApp(target) {\n      const apps = document.querySelectorAll('div.my-app__item');\n      \n      for (let i = 0; i < apps.length; i++) {\n        const text = apps[i].textContent.replace(/\\s+/g, ' ').trim();\n        console.log(`检查应用 ${i + 1}: ${text}`);\n        \n        if (text.includes(target)) {\n          return { found: true, position: i + 1, text };\n        }\n      }\n      \n      return { found: false };\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n  } catch (error) {\n    console.error('❌ 应用查找失败:', error);\n  } finally {\n    automaNextBlock();\n  }\n})();", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "click-app", "type": "BlockBasic", "initialized": false, "position": {"x": 2480, "y": 36}, "data": {"disableBlock": false, "description": "🎯 点击应用", "findBy": "cssSelector", "waitForSelector": true, "waitSelectorTimeout": 8000, "selector": "{{variables.dynamicClickSelector}}", "markEl": false, "multiple": false}, "label": "event-click"}], "edges": [{"id": "start-to-init", "type": "custom", "source": "trigger-start", "target": "init-state", "sourceHandle": "trigger-start-output-1", "targetHandle": "init-state-input-1", "markerEnd": "arrowclosed"}, {"id": "init-to-open", "type": "custom", "source": "init-state", "target": "open-page", "sourceHandle": "init-state-output-1", "targetHandle": "open-page-input-1", "markerEnd": "arrowclosed"}, {"id": "open-to-login", "type": "custom", "source": "open-page", "target": "smart-login-attempt", "sourceHandle": "open-page-output-1", "targetHandle": "smart-login-attempt-input-1", "markerEnd": "arrowclosed"}, {"id": "login-to-retry", "type": "custom", "source": "smart-login-attempt", "target": "retry-check", "sourceHandle": "smart-login-attempt-output-1", "targetHandle": "retry-check-input-1", "markerEnd": "arrowclosed"}, {"id": "retry-to-login", "type": "custom", "source": "retry-check", "target": "smart-login-attempt", "sourceHandle": "retry-check-output-1", "targetHandle": "smart-login-attempt-input-1", "markerEnd": "arrowclosed"}, {"id": "retry-to-success", "type": "custom", "source": "retry-check", "target": "success-check", "sourceHandle": "retry-check-output-2", "targetHandle": "success-check-input-1", "markerEnd": "arrowclosed"}, {"id": "success-to-worktable", "type": "custom", "source": "success-check", "target": "goto-worktable", "sourceHandle": "success-check-output-1", "targetHandle": "goto-worktable-input-1", "markerEnd": "arrowclosed"}, {"id": "worktable-to-find", "type": "custom", "source": "goto-worktable", "target": "find-app", "sourceHandle": "goto-worktable-output-1", "targetHandle": "find-app-input-1", "markerEnd": "arrowclosed"}, {"id": "find-to-click", "type": "custom", "source": "find-app", "target": "click-app", "sourceHandle": "find-app-output-1", "targetHandle": "click-app-input-1", "markerEnd": "arrowclosed"}], "position": [-200, 50], "zoom": 0.8, "viewport": {"x": -200, "y": 50, "zoom": 0.8}}, "settings": {"publicId": "autologin-fixed-ultimate", "blockDelay": 0, "saveLog": true, "debugMode": false, "restartTimes": 3, "notification": true, "execContext": "popup", "reuseLastState": false, "inputAutocomplete": true, "onError": "stop-workflow", "executedBlockOnWeb": false, "insertDefaultColumn": false, "defaultColumnName": "column", "tabLoadTimeout": 30000}, "globalData": "{\n\t\"workflowType\": \"ultimate-login\"\n}", "description": "🚀 修复版终极自动登录：去掉复杂循环，用条件块实现重试", "includedWorkflows": {}}
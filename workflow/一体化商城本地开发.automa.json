{"extVersion": "1.29.10", "name": "一体化商城本地开发", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"edges": [{"class": "connected-edges", "data": {}, "id": "vueflow__edge-02pBUI6PycykS4yVkFJeb02pBUI6PycykS4yVkFJeb-output-1-a991sbta991sbt-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "02pBUI6PycykS4yVkFJeb", "sourceHandle": "02pBUI6PycykS4yVkFJeb-output-1", "sourceX": 242.04986721772335, "sourceY": 212.96536270901373, "target": "a991sbt", "targetHandle": "a991sbt-input-1", "targetX": 248.15755036849512, "targetY": 203.54258567973426, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-mt4v6iemt4v6ie-output-1-ibobooyibobooy-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "mt4v6ie", "sourceHandle": "mt4v6ie-output-1", "sourceX": 294.0131903135996, "sourceY": 792.8690802073933, "target": "<PERSON><PERSON><PERSON><PERSON>", "targetHandle": "ibobooy-input-1", "targetX": 358.94140379682307, "targetY": 776.2722804421549, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-ibobooyibobooy-output-1-3st5obj3st5obj-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "<PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "ibobooy-output-1", "sourceX": 598.9177128510999, "sourceY": 776.2722804421549, "target": "3st5obj", "targetHandle": "3st5obj-input-1", "targetX": 631.0155488629472, "targetY": 714.0917097402269, "type": "custom", "updatable": true}, {"class": "connected-edges", "data": {}, "id": "vueflow__edge-a991sbta991sbt-output-1-uunolg1uunolg1-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "a991sbt", "sourceHandle": "a991sbt-output-1", "sourceX": 488.1337984936324, "sourceY": 203.54258567973426, "target": "uunolg1", "targetHandle": "uunolg1-input-1", "targetX": 518.6490543220301, "targetY": 197.75536121027596, "type": "custom", "updatable": true}, {"class": "connected-edges", "data": {}, "id": "vueflow__edge-dkcm9ucdkcm9uc-output-1-fmirqwifmirqwi-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "dkcm9uc", "sourceHandle": "dkcm9uc-output-1", "sourceX": 475.59869408744964, "sourceY": 383.20476289307766, "target": "<PERSON><PERSON><PERSON><PERSON>", "targetHandle": "fmirqwi-input-1", "targetX": 554.8250640482631, "targetY": 393.95336244648394, "type": "custom", "updatable": true}, {"class": "connected-edges", "data": {}, "id": "vueflow__edge-uunolg1uunolg1-output-1-dkcm9ucdkcm9uc-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "uunolg1", "sourceHandle": "uunolg1-output-1", "sourceX": 758.6253024471673, "sourceY": 197.75536121027596, "target": "dkcm9uc", "targetHandle": "dkcm9uc-input-1", "targetX": 235.62244596231236, "targetY": 383.20476289307766, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-fmirqwifmirqwi-output-1-i0oaieyi0oaiey-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "<PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "fmirqwi-output-1", "sourceX": 794.8013121734004, "sourceY": 393.95336244648394, "target": "i0oaiey", "targetHandle": "i0oaiey-input-1", "targetX": 128.75639549541756, "targetY": 528.4325940717655, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-i0oaieyi0oaiey-output-1-l8lyhoal8lyhoa-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "i0oaiey", "sourceHandle": "i0oaiey-output-1", "sourceX": 368.7326436205548, "sourceY": 528.4325940717655, "target": "l8lyhoa", "targetHandle": "l8lyhoa-input-1", "targetX": 270.7143221484628, "targetY": 625.8664423897307, "type": "custom", "updatable": true}], "nodes": [{"data": {"activeInInput": false, "contextMenuName": "", "contextTypes": [], "date": "", "days": [], "delay": 5, "description": "", "disableBlock": false, "interval": 60, "isUrlRegex": false, "observeElement": {"baseElOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}, "baseSelector": "", "matchPattern": "", "selector": "", "targetOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}}, "parameters": [], "preferParamsInTab": false, "shortcut": "", "time": "00:00", "type": "manual", "url": ""}, "id": "02pBUI6PycykS4yVkFJeb", "initialized": false, "label": "trigger", "position": {"x": 26.05803976506911, "y": 176.98785948259646}, "type": "BlockBasic"}, {"data": {"active": true, "customUserAgent": false, "description": "打开生产商城", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "https://test-aliwww.lotsmall.cn/manage/workspace", "userAgent": "", "waitTabLoaded": false}, "id": "a991sbt", "initialized": false, "label": "new-tab", "position": {"x": 272.1420015055479, "y": 167.54773288083092}, "type": "BlockBasic"}, {"data": {"active": true, "customUserAgent": false, "description": "打开GITHUB仓库", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "https://github.com/{{globalData.githubName}}/weread2notion-pro/settings/secrets/actions/WEREAD_COOKIE", "userAgent": "", "waitTabLoaded": false}, "id": "mt4v6ie", "initialized": false, "label": "new-tab", "position": {"x": 78.02136286094537, "y": 756.874212176205}, "type": "BlockBasic"}, {"data": {"assignVariable": false, "clearValue": true, "dataColumn": "", "delay": 0, "description": "填写<PERSON><PERSON>", "disableBlock": false, "events": [], "findBy": "cssSelector", "getValue": false, "markEl": false, "multiple": false, "optionPosition": "1", "saveData": false, "selectOptionBy": "value", "selected": true, "selector": "[id=\"secret_value\"]", "type": "text-field", "value": "{{variables.cookie}} ", "variableName": "", "waitForSelector": true, "waitSelectorTimeout": 5000}, "id": "<PERSON><PERSON><PERSON><PERSON>", "initialized": false, "label": "forms", "position": {"x": 382.9258549338758, "y": 740.2774124109666}, "type": "BlockBasic"}, {"data": {"description": "更新<PERSON>ie", "disableBlock": false, "findBy": "cssSelector", "markEl": false, "multiple": false, "selector": "#repo-content-pjax-container > div > div > div.Layout-main > div > div > form > div:nth-child(4) > button", "waitForSelector": true, "waitSelectorTimeout": 5000}, "id": "3st5obj", "initialized": false, "label": "event-click", "position": {"x": 655, "y": 678.0968417090386}, "type": "BlockBasic"}, {"data": {"active": true, "customUserAgent": false, "description": "", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "http://127.0.0.1:8080/manage/login", "userAgent": "", "waitTabLoaded": true}, "id": "dkcm9uc", "initialized": false, "label": "new-tab", "position": {"x": 259.60689709936514, "y": 347.2272292020906}, "type": "BlockBasic"}, {"data": {"code": "/*\n * Automa Script: Get Precise Data from Supabase and Apply\n */\n\n// --- 配置 (不变) ---\nconst GET_URL = 'https://okkgchwzppghiyfgmrlj.supabase.co/functions/v1/get-data?key=my-session';\nconst API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ra2djaHd6cHBnaGl5ZmdtcmxqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTQwNTAsImV4cCI6MjA2NTIzMDA1MH0.LMhY-7H3ySiXEZt2cjLXhhicL4idx0A6xurvxynqJf8';\n\n// --- 核心逻辑 (精准注入版) ---\nasync function getAndApplyPreciseData() {\n    console.log('Automa: Attempting to retrieve precise data from Supabase...');\n    try {\n        const response = await fetch(GET_URL, {\n            method: 'GET',\n            headers: { 'apikey': API_KEY, 'Authorization': 'Bearer ' + API_KEY }\n        });\n\n        if (!response.ok) throw new Error(`Network response was not ok: ${response.status}`);\n        \n        const data = await response.json();\n      // 关键改动：将取回的字符串值，解析成一个真正的JavaScript对象\n        const preciseData = JSON.parse(data.value); \n        // const preciseData = data.value; // value现在是包含三个特定字段的对象\n        // alert(preciseData)\n        if (preciseData) {\n            console.log('Automa: Successfully retrieved precise data.', preciseData);\n            console.log('Automa: SESSIONID cookie：'+preciseData.sessionId);\n\n            // 1. 精确设置Cookie: SESSIONID\n            if (preciseData.sessionId) {\n                // 设置cookie时最好指定path=/，确保全站可用\n                document.cookie = `SESSIONID=${preciseData.sessionId}; path=/`;\n                console.log('Automa: SESSIONID cookie applied.');\n            }\n\n            // 2. 精确设置Local Storage\n            if (preciseData.token) {\n                localStorage.setItem('xjsc_2018_token', preciseData.token);\n                console.log('Automa: xjsc_2018_token applied.');\n            }\n            if (preciseData.userId) {\n                localStorage.setItem('xjsc_2018_userId', preciseData.userId);\n                console.log('Automa: xjsc_2018_userId applied.');\n            }\n\n            // 3. 刷新页面以应用所有更改\n            console.log('Automa: Reloading page...');\n            // location.reload();\n\n        } else {\n            console.error('Automa: No data found in Supabase response.');\n            alert('未能从云端获取到有效的数据。');\n        }\n    } catch (error) {\n        console.error('Automa: Error getting or applying data.', error);\n        alert('从云端获取数据失败，请按F12打开浏览器控制台查看详细错误信息。');\n    }\n}\n\ngetAndApplyPreciseData();", "context": "website", "description": "设置<PERSON><PERSON>", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "settings": {"blockTimeout": 0, "debugMode": false}, "timeout": 20000}, "id": "<PERSON><PERSON><PERSON><PERSON>", "initialized": false, "label": "javascript-code", "position": {"x": 578.809515185316, "y": 357.9585248798655}, "type": "BlockBasic"}, {"data": {"code": "/*\n * Automa Script: Precisely Extract and Send Data to Supabase\n */\n\n// --- 配置 (不变) ---\nconst SET_URL = 'https://okkgchwzppghiyfgmrlj.supabase.co/functions/v1/set-data';\nconst API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ra2djaHd6cHBnaGl5ZmdtcmxqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTQwNTAsImV4cCI6MjA2NTIzMDA1MH0.LMhY-7H3ySiXEZt2cjLXhhicL4idx0A6xurvxynqJf8';\n\n// --- 核心逻辑 (精准提取版) ---\n\n// 1. 定义一个函数，用于从cookie字符串中精确查找某个key的值\nfunction getCookieValue(name) {\n  const cookieString = \"; \" + document.cookie;\n  const parts = cookieString.split(`; ${name}=`);\n  if (parts.length === 2) {\n    return parts.pop().split(';').shift();\n  }\n}\n\n// 2. 精确提取需要的数据\nconst sessionId = getCookieValue('SESSIONID');\nconst token = localStorage.getItem('xjsc_2018_token');\nconst userId = localStorage.getItem('xjsc_2018_userId');\n\n// 3. 将提取到的三项数据打包成一个新对象\nconst payloadValue = {\n  sessionId: sessionId,\n  token: token,\n  userId: userId,\n};\n\n// 4. 发送打包后的精准数据到Supabase\nconsole.log('Automa: Sending precise data to Supabase...', payloadValue);\nfetch(SET_URL, {\n    method: 'POST',\n    headers: {\n        'Content-Type': 'application/json',\n        'apikey': API_KEY,\n        'Authorization': 'Bearer ' + API_KEY\n    },\n    body: JSON.stringify({\n        key: 'my-session',\n        value: payloadValue\n    })\n})\n.then(response => response.json())\n.then(data => console.log('Automa: Successfully sent precise data.', data))\n.catch(error => console.error('Automa: Error sending precise data.', error));", "context": "website", "description": "设置<PERSON><PERSON>", "disableBlock": false, "everyNewTab": false, "preloadScripts": [], "runBeforeLoad": false, "settings": {"blockTimeout": 0, "debugMode": false}, "timeout": 20000}, "id": "uunolg1", "initialized": false, "label": "javascript-code", "position": {"x": 542.6334445299433, "y": 161.76050841137263}, "type": "BlockBasic"}, {"data": {"active": true, "customUserAgent": false, "description": "", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "http://127.0.0.1:8080/manage/workspace", "userAgent": "", "waitTabLoaded": true}, "id": "l8lyhoa", "initialized": false, "label": "new-tab", "position": {"x": 294.6987732855156, "y": 589.8890000924529}, "type": "BlockBasic"}, {"data": {"disableBlock": false, "time": "2000"}, "id": "i0oaiey", "initialized": false, "label": "delay", "position": {"x": 152.7407857033308, "y": 470.1807378679664}, "type": "BlockDelay"}], "position": [0, 0], "viewport": {"x": 0, "y": 0, "zoom": 1.0017424408659286}, "zoom": 1.0017424408659286}, "settings": {"publicId": "localdev", "restartTimes": 3, "notification": true, "tabLoadTimeout": 30000, "inputAutocomplete": true, "insertDefaultColumn": false, "defaultColumnName": "column", "blockDelay": 0, "debugMode": false, "execContext": "popup", "executedBlockOnWeb": false, "onError": "stop-workflow", "reuseLastState": false, "saveLog": true}, "globalData": "{\n  \"githubName\": \"Yg<PERSON>\"\n}", "description": "sessionId共享", "includedWorkflows": {}}
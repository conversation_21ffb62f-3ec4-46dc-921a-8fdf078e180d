/**
 * 一次性同步所有文件到生产服务器脚本
 * 将所有需要同步的文件一次性复制到***************服务器
 * 
 * 使用方法:
 * node scripts/sync-all-files.js
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 配置
const TARGET_SERVER = '***************';
const TARGET_DIR = '/home/<USER>';
const ROOT_DIR = path.resolve(__dirname, '..');

// 定义需要同步的文件和目录
const filesToSync = [
  // 应用页面和组件
  'app/page.tsx',
  'app/layout.tsx',
  'app/globals.css',
  'app/ocr-test',
  'app/plugin-upload', // 新增的插件上传页面
  'app/plugin-list', // 新增的插件列表页面
  'components',
  
  // API 路由
  'app/api/ocr',
  'app/api/plugin', // 新增的插件 API 路由
  'app/api/account',
  'app/api/extension',
  
  // 库文件
  'lib',
  'types',
  
  // 配置文件
  'next.config.js',
  'tailwind.config.js',
  'postcss.config.js',
  'tsconfig.json',
  
  // 包管理文件
  'package.json'
];

// 需要同步的目录
const SYNC_DIRS = [
  'database',
  'data',
  'lib',
  'config',
  'api',
  'server',
  'routes',
  'controllers',
  'models',
  'public/data',
  'middleware',
  'utils',
  'hooks',
  'services',
  'components/server',
  'app/plugin-upload', // 新增的插件上传页面
  'app/plugin-list', // 新增的插件列表页面
  'app/api/plugin' // 新增的插件 API 路由
];

// 需要同步的特定文件
const SYNC_FILES = [
  'lib/config.js',
  '.env'
];

// 忽略的文件和目录
const IGNORE_PATTERNS = [
  '.git',
  'node_modules',
  'chrome-extension',  // 忽略Chrome插件文件
  '.next',
  'dist',
  'build',
  '*.log',
  'migration-temp',
  'temp',
  '*.lock',
  'package-lock.json'
];

console.log('=== 一次性文件同步工具 ===');
console.log(`目标服务器: ${TARGET_SERVER}`);
console.log(`目标目录: ${TARGET_DIR}`);

// 递归获取目录中的所有文件
function getAllFiles(dir, fileList = [], basePath = '') {
  try {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const relativePath = path.join(basePath, file);
      
      // 检查是否应该忽略
      if (IGNORE_PATTERNS.some(pattern => {
        if (pattern.includes('*')) {
          const regexPattern = pattern.replace(/\*/g, '.*');
          return new RegExp(regexPattern).test(relativePath);
        }
        return relativePath.includes(pattern);
      })) {
        return;
      }
      
      if (fs.statSync(filePath).isDirectory()) {
        getAllFiles(filePath, fileList, relativePath);
      } else {
        fileList.push({
          fullPath: filePath,
          relativePath: relativePath
        });
      }
    });
  } catch (err) {
    console.error(`读取目录 ${dir} 失败:`, err.message);
  }
  
  return fileList;
}

// 收集所有需要同步的文件
let allFiles = [];
let allDirs = new Set();

// 添加指定目录中的所有文件
SYNC_DIRS.forEach(dirPath => {
  const fullPath = path.join(ROOT_DIR, dirPath);
  if (fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory()) {
    const files = getAllFiles(fullPath, [], dirPath);
    allFiles = [...allFiles, ...files];
    
    // 添加目录及其父目录
    let currentPath = dirPath;
    while (currentPath !== '.') {
      allDirs.add(currentPath);
      currentPath = path.dirname(currentPath);
    }
  }
});

// 添加特定文件
SYNC_FILES.forEach(filePath => {
  const fullPath = path.join(ROOT_DIR, filePath);
  if (fs.existsSync(fullPath) && fs.statSync(fullPath).isFile()) {
    allFiles.push({
      fullPath: fullPath,
      relativePath: filePath
    });
    
    // 添加文件所在目录
    const dirPath = path.dirname(filePath);
    if (dirPath !== '.') {
      allDirs.add(dirPath);
      
      // 添加所有父目录
      let currentPath = dirPath;
      while (currentPath !== '.') {
        allDirs.add(currentPath);
        currentPath = path.dirname(currentPath);
      }
    }
  }
});

// 去重并排序
const allDirsArray = [...allDirs].sort();

console.log(`找到 ${allFiles.length} 个文件和 ${allDirsArray.length} 个目录需要同步`);

// 创建远程目录
console.log('\n正在创建远程目录...');
try {
  // 先创建所有目录
  allDirsArray.sort().forEach(dir => {
    const remoteDirPath = path.join(TARGET_DIR, dir).replace(/\\/g, '/');
    try {
      execSync(`ssh root@${TARGET_SERVER} "mkdir -p ${remoteDirPath}"`, { stdio: 'pipe' });
      console.log(`✅ 创建目录: ${dir}`);
    } catch (error) {
      console.error(`❌ 无法创建目录 ${dir}: ${error.message}`);
    }
  });
} catch (error) {
  console.error('❌ 创建目录时出错:', error.message);
}

// 同步文件
console.log('\n正在同步文件...');

// 按目录分组文件，以便批量同步
const filesByDir = {};
allFiles.forEach(file => {
  const dir = path.dirname(file.relativePath);
  if (!filesByDir[dir]) {
    filesByDir[dir] = [];
  }
  filesByDir[dir].push(file);
});

// 同步每个目录的文件
let syncedCount = 0;
let failedCount = 0;

Object.entries(filesByDir).forEach(([dir, files]) => {
  console.log(`\n同步目录 ${dir} 中的 ${files.length} 个文件...`);
  
  // 为每个文件创建本地路径和远程路径
  files.forEach(file => {
    const localPath = file.fullPath;
    const remotePath = path.join(TARGET_DIR, file.relativePath).replace(/\\/g, '/');
    
    try {
      execSync(`scp "${localPath}" root@${TARGET_SERVER}:"${remotePath}"`, { stdio: 'pipe' });
      console.log(`✅ 同步成功: ${file.relativePath}`);
      syncedCount++;
    } catch (error) {
      console.error(`❌ 同步失败 ${file.relativePath}: ${error.message}`);
      failedCount++;
    }
  });
});

// 同步完成后重启Docker容器
console.log('\n正在重启Docker容器...');
try {
  execSync(`ssh root@${TARGET_SERVER} "cd ${TARGET_DIR} && docker-compose restart"`, { stdio: 'inherit' });
  console.log('✅ Docker容器已重启');
} catch (error) {
  console.error('❌ 重启Docker容器失败:', error.message);
}

// 同步结果
console.log('\n=== 同步完成 ===');
console.log(`✅ 成功同步: ${syncedCount} 个文件`);
if (failedCount > 0) {
  console.error(`❌ 同步失败: ${failedCount} 个文件`);
}

console.log('\n如果您想更新Docker配置，请运行:');
console.log('npm run update-docker-config'); 
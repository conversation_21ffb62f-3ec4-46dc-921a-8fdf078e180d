const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function moveAccountsToTestEnv() {
  console.log('🔄 移动账号到测试环境...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ 环境变量未配置');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 登录用户
    console.log('👤 登录用户...');
    const { error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginError) {
      console.error('❌ 登录失败:', loginError.message);
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    console.log('✅ 用户:', user.email);

    // 获取环境信息
    const { data: environments } = await supabase
      .from('environments')
      .select('*')
      .eq('user_id', user.id);

    const testEnv = environments.find(env => env.name === '测试环境');
    const youHubeiEnv = environments.find(env => env.name === '游湖北');

    if (!testEnv || !youHubeiEnv) {
      console.error('❌ 未找到目标环境');
      return;
    }

    console.log('🏗️  环境信息:');
    console.log(`   测试环境ID: ${testEnv.id}`);
    console.log(`   游湖北环境ID: ${youHubeiEnv.id}`);

    // 查找游湖北环境下的账号
    const { data: accounts, error: accountError } = await supabase
      .from('accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('environment_id', youHubeiEnv.id);

    if (accountError) {
      console.error('❌ 查询账号失败:', accountError.message);
      return;
    }

    console.log(`\n📋 找到 ${accounts.length} 个账号需要移动:`);
    accounts.forEach((acc, index) => {
      console.log(`   ${index + 1}. ${acc.name} (${acc.username})`);
    });

    if (accounts.length === 0) {
      console.log('ℹ️  游湖北环境下没有账号需要移动');
      return;
    }

    // 移动账号到测试环境
    console.log('\n🔄 开始移动账号...');
    for (const account of accounts) {
      const { error: updateError } = await supabase
        .from('accounts')
        .update({
          environment_id: testEnv.id
        })
        .eq('id', account.id)
        .eq('user_id', user.id);

      if (updateError) {
        console.error(`❌ 移动账号 ${account.name} 失败:`, updateError.message);
      } else {
        console.log(`✅ 成功移动账号: ${account.name}`);
      }
    }

    // 验证移动结果
    console.log('\n🔍 验证移动结果...');
    const { data: testAccounts } = await supabase
      .from('accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('environment_id', testEnv.id);

    console.log(`✅ 测试环境现在有 ${testAccounts.length} 个账号:`);
    testAccounts.forEach(acc => {
      console.log(`   - ${acc.name} (${acc.username})`);
    });

    console.log('\n🎉 移动完成！现在在应用中选择"测试环境"即可看到账号');

  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  }
}

moveAccountsToTestEnv().catch(console.error); 
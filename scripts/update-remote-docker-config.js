/**
 * 更新远程服务器Docker配置脚本
 * 将本地的docker-compose-dev.yml配置文件上传到远程服务器，并应用更改
 * 
 * 使用方法:
 * node scripts/update-remote-docker-config.js
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 配置
const TARGET_SERVER = '***************';
const TARGET_DIR = '/home/<USER>';
const ROOT_DIR = path.resolve(__dirname, '..');
const DOCKER_COMPOSE_FILE = path.join(ROOT_DIR, 'docker-compose-dev.yml');

console.log('=== 更新远程Docker配置 ===');
console.log(`目标服务器: ${TARGET_SERVER}`);
console.log(`目标目录: ${TARGET_DIR}`);

// 确认Docker配置文件存在
if (!fs.existsSync(DOCKER_COMPOSE_FILE)) {
  console.error('❌ Docker配置文件不存在，请先创建docker-compose-dev.yml');
  process.exit(1);
}

try {
  // 上传Docker配置文件
  console.log('正在上传Docker配置文件...');
  execSync(`scp "${DOCKER_COMPOSE_FILE}" root@${TARGET_SERVER}:"${TARGET_DIR}/docker-compose.yml"`, { stdio: 'inherit' });
  console.log('✅ Docker配置文件已上传');
  
  // 确保远程目录结构正确
  console.log('正在确保远程目录结构...');
  execSync(`ssh root@${TARGET_SERVER} "mkdir -p ${TARGET_DIR}/database ${TARGET_DIR}/lib ${TARGET_DIR}/chrome-extension"`, { stdio: 'inherit' });
  console.log('✅ 远程目录结构已确认');
  
  // 同步初始文件
  console.log('正在同步初始文件...');
  
  // 同步数据库文件
  if (fs.existsSync(path.join(ROOT_DIR, 'database'))) {
    execSync(`scp -r "${path.join(ROOT_DIR, 'database')}/"* root@${TARGET_SERVER}:"${TARGET_DIR}/database/"`, { stdio: 'inherit' });
    console.log('✅ 数据库文件已同步');
  }
  
  // 同步配置文件
  if (fs.existsSync(path.join(ROOT_DIR, 'lib/config.js'))) {
    execSync(`scp "${path.join(ROOT_DIR, 'lib/config.js')}" root@${TARGET_SERVER}:"${TARGET_DIR}/lib/config.js"`, { stdio: 'inherit' });
    console.log('✅ 前端配置文件已同步');
  }
  
  if (fs.existsSync(path.join(ROOT_DIR, 'chrome-extension/config.js'))) {
    execSync(`scp "${path.join(ROOT_DIR, 'chrome-extension/config.js')}" root@${TARGET_SERVER}:"${TARGET_DIR}/chrome-extension/config.js"`, { stdio: 'inherit' });
    console.log('✅ 插件配置文件已同步');
  }
  
  // 重启Docker容器
  console.log('正在重启Docker容器...');
  execSync(`ssh root@${TARGET_SERVER} "cd ${TARGET_DIR} && docker-compose down && docker-compose up -d"`, { stdio: 'inherit' });
  console.log('✅ Docker容器已重启');
  
  console.log('\n✅ 远程Docker配置已更新');
  console.log('现在您可以运行 node scripts/sync-to-prod.js 来自动同步文件变化');
  
} catch (error) {
  console.error('❌ 更新失败:');
  console.error(error.message);
  process.exit(1);
} 
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function checkPasswordFormat() {
  console.log('🔍 检查数据库中的密码格式...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ 环境变量未配置');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 登录用户
    const { error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginError) {
      console.error('❌ 登录失败:', loginError.message);
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    console.log('✅ 用户:', user.email);

    // 查询所有账号的密码
    const { data: accounts, error: accountError } = await supabase
      .from('accounts')
      .select('id, name, encrypted_password')
      .eq('user_id', user.id);

    if (accountError) {
      console.error('❌ 查询账号失败:', accountError.message);
      return;
    }

    console.log(`📋 找到 ${accounts.length} 个账号，检查密码格式:\n`);

    accounts.forEach((acc, index) => {
      console.log(`账号 ${index + 1}: ${acc.name}`);
      console.log(`  密码长度: ${acc.encrypted_password.length}`);
      console.log(`  密码内容: ${acc.encrypted_password}`);
      console.log(`  是否包含冒号: ${acc.encrypted_password.includes(':') ? '✅ 是' : '❌ 否'}`);
      
      if (acc.encrypted_password.includes(':')) {
        const parts = acc.encrypted_password.split(':');
        console.log(`  冒号分割部分: ${parts.length}`);
        if (parts.length === 2) {
          console.log(`  IV长度: ${parts[0].length}`);
          console.log(`  加密数据长度: ${parts[1].length}`);
        }
      } else {
        console.log(`  ⚠️  密码格式不正确，应该是 IV:加密数据 格式`);
      }
      console.log('');
    });

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  }
}

checkPasswordFormat().catch(console.error); 
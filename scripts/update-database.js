const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function updateDatabase() {
  console.log('🔄 更新数据库结构...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ 环境变量未配置');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 登录用户
    console.log('👤 登录用户...');
    const { error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginError) {
      console.error('❌ 登录失败:', loginError.message);
      return;
    }

    console.log('✅ 登录成功');

    // 执行数据库更新
    console.log('\n📝 添加 captcha_input_xpath 字段...');
    
    const { error: alterError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE public.accounts 
        ADD COLUMN IF NOT EXISTS captcha_input_xpath TEXT;
        
        COMMENT ON COLUMN public.accounts.captcha_input_xpath IS '验证码输入框的XPath选择器';
      `
    });

    if (alterError) {
      console.error('❌ 数据库更新失败:', alterError.message);
      console.log('\n💡 请手动在Supabase SQL编辑器中执行以下SQL:');
      console.log('ALTER TABLE public.accounts ADD COLUMN IF NOT EXISTS captcha_input_xpath TEXT;');
      console.log('COMMENT ON COLUMN public.accounts.captcha_input_xpath IS \'验证码输入框的XPath选择器\';');
    } else {
      console.log('✅ 数据库更新成功');
    }

  } catch (error) {
    console.error('❌ 更新失败:', error.message);
    console.log('\n💡 请手动在Supabase SQL编辑器中执行以下SQL:');
    console.log('ALTER TABLE public.accounts ADD COLUMN IF NOT EXISTS captcha_input_xpath TEXT;');
    console.log('COMMENT ON COLUMN public.accounts.captcha_input_xpath IS \'验证码输入框的XPath选择器\';');
  }
}

updateDatabase(); 
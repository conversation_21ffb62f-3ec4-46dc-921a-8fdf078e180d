/**
 * 环境切换脚本
 * 用于在开发环境和生产环境之间切换
 * 
 * 使用方法:
 * node scripts/switch-env.js dev  # 切换到开发环境
 * node scripts/switch-env.js prod # 切换到生产环境
 */

const fs = require('fs');
const path = require('path');

// 配置文件路径
const configPath = path.join(__dirname, '../lib/config.js');
const chromeConfigPath = path.join(__dirname, '../chrome-extension/config.js');

// 检查参数
const args = process.argv.slice(2);
if (args.length !== 1 || (args[0] !== 'dev' && args[0] !== 'prod')) {
  console.error('使用方法: node scripts/switch-env.js [dev|prod]');
  process.exit(1);
}

const targetEnv = args[0];
const isProd = targetEnv === 'prod';

// 修改前端配置文件
try {
  console.log(`正在将前端配置切换到${isProd ? '生产' : '开发'}环境...`);
  
  // 读取配置文件
  let configContent = fs.readFileSync(configPath, 'utf8');
  
  // 替换当前环境设置
  configContent = configContent.replace(
    /const isDevelopment = .*;/,
    `const isDevelopment = ${!isProd};`
  );
  
  // 写入文件
  fs.writeFileSync(configPath, configContent, 'utf8');
  console.log(`✅ 前端配置已切换到${isProd ? '生产' : '开发'}环境`);
} catch (error) {
  console.error('❌ 修改前端配置文件失败:', error);
}

// 修改Chrome插件配置文件
try {
  console.log(`正在将Chrome插件配置切换到${isProd ? '生产' : '开发'}环境...`);
  
  // 读取配置文件
  let chromeConfigContent = fs.readFileSync(chromeConfigPath, 'utf8');
  
  // 替换当前环境设置
  chromeConfigContent = chromeConfigContent.replace(
    /const currentEnvironment = ['"].*['"]/,
    `const currentEnvironment = '${isProd ? 'production' : 'development'}'`
  );
  
  // 写入文件
  fs.writeFileSync(chromeConfigPath, chromeConfigContent, 'utf8');
  console.log(`✅ Chrome插件配置已切换到${isProd ? '生产' : '开发'}环境`);
} catch (error) {
  console.error('❌ 修改Chrome插件配置文件失败:', error);
}

console.log(`\n🎉 环境已成功切换到${isProd ? '生产' : '开发'}环境`);
console.log(`请重新构建应用和插件以应用更改。`);
console.log(`前端: npm run build`);
console.log(`插件: 重新加载Chrome扩展`); 
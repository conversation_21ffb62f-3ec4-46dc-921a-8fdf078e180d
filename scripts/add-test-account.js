const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');
require('dotenv').config({ path: '.env.local' });

// 加密函数 - 与Electron应用程序保持一致
function encryptPassword(password) {
  const algorithm = 'aes-256-cbc';
  // 使用与Electron相同的密钥生成方式
  const key = crypto.scryptSync('account-manage-secret', 'salt', 32);
  const iv = crypto.randomBytes(16);  // 16字节IV
  
  const cipher = crypto.createCipheriv(algorithm, key, iv);
  let encrypted = cipher.update(password, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  // 返回 IV + 加密数据，用冒号分隔
  return iv.toString('hex') + ':' + encrypted;
}

async function addTestAccount() {
  console.log('🚀 正在添加测试账号...\n');

  // 获取 Supabase 配置
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ 环境变量未配置，请检查 .env.local 文件');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 首先需要创建一个用户（如果使用的是演示模式）
    console.log('👤 检查用户状态...');
    
    // 尝试登录演示用户
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (authError) {
      console.log('ℹ️  需要先注册演示用户...');
      
      // 注册演示用户
      const { error: signUpError } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'password123'
      });

      if (signUpError) {
        console.error('❌ 用户注册失败:', signUpError.message);
        return;
      }

      console.log('✅ 演示用户注册成功！');
      
      // 重新登录
      const { error: loginError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'password123'
      });

      if (loginError) {
        console.error('❌ 登录失败:', loginError.message);
        return;
      }
    }

    console.log('✅ 用户登录成功');

    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('❌ 获取用户信息失败');
      return;
    }

    console.log(`👤 当前用户: ${user.email}`);

    // 获取测试环境ID
    console.log('🏗️  查找测试环境...');
    const { data: environments, error: envError } = await supabase
      .from('environments')
      .select('*')
      .eq('user_id', user.id)
      .eq('name', '测试环境')
      .limit(1);

    if (envError) {
      console.error('❌ 查询环境失败:', envError.message);
      return;
    }

    if (environments.length === 0) {
      console.error('❌ 未找到测试环境，请确保环境已创建');
      return;
    }

    const testEnvironment = environments[0];
    console.log('✅ 找到测试环境:', testEnvironment.name);

    // 检查是否已存在测试账号
    const { data: existingAccounts, error: checkError } = await supabase
      .from('accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('name', 'LotSmall测试账号');

    if (checkError) {
      console.error('❌ 检查现有账号失败:', checkError.message);
      return;
    }

    if (existingAccounts.length > 0) {
      console.log('ℹ️  测试账号已存在，跳过创建');
      console.log('🎉 完成！现在可以在应用中看到测试账号了');
      return;
    }

    // 加密密码
    console.log('🔐 加密密码...');
    const encryptedPassword = encryptPassword('Admin@123');

    // 创建测试账号
    console.log('📝 创建测试账号...');
    const { data: newAccount, error: createError } = await supabase
      .from('accounts')
      .insert({
        user_id: user.id,
        environment_id: testEnvironment.id,
        name: 'LotSmall测试账号',
        login_url: 'https://test-aliuser.lotsmall.cn/usercenter/login',
        username: '***********',
        encrypted_password: encryptedPassword,
        username_xpath: '//input[@placeholder="请输入手机号/邮箱"]',
        password_xpath: '//input[@placeholder="请输入密码"]',
        captcha_xpath: '//img[contains(@src,"captcha") or contains(@src,"verify") or contains(@alt,"验证码")]',
        captcha_input_xpath: '//input[@placeholder="请输入验证码"]',
        login_button_xpath: '//button[contains(text(),"登录")]'
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ 创建账号失败:', createError.message);
      return;
    }

    console.log('✅ 测试账号创建成功！');
    console.log('📋 账号信息:');
    console.log(`   名称: ${newAccount.name}`);
    console.log(`   URL: ${newAccount.login_url}`);
    console.log(`   用户名: ${newAccount.username}`);
    console.log(`   环境: 测试环境`);
    
    console.log('\n🎉 完成！现在刷新应用页面就可以看到测试账号了');
    console.log('💡 提示：在应用中选择"测试环境"即可看到刚添加的账号');

  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  }
}

// 执行脚本
addTestAccount().catch(console.error); 
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function testAccountUpdate() {
  console.log('🧪 测试账号更新功能...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ 环境变量未配置');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 登录用户
    console.log('👤 登录用户...');
    const { error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginError) {
      console.error('❌ 登录失败:', loginError.message);
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    console.log('✅ 用户:', user.email);

    // 查询现有账号
    console.log('\n📋 查询现有账号...');
    const { data: accounts, error: queryError } = await supabase
      .from('accounts')
      .select('*')
      .eq('user_id', user.id)
      .limit(1);

    if (queryError) {
      console.error('❌ 查询失败:', queryError.message);
      return;
    }

    if (accounts.length === 0) {
      console.log('❌ 没有找到账号进行测试');
      return;
    }

    const testAccount = accounts[0];
    console.log('📝 找到测试账号:', testAccount.name);
    console.log('   ID:', testAccount.id);

    // 测试更新账号名称
    console.log('\n🔄 测试更新账号...');
    const newName = `测试更新_${Date.now()}`;
    
    const { data: updatedAccount, error: updateError } = await supabase
      .from('accounts')
      .update({ 
        name: newName,
        username_xpath: '//input[@name="username"]',
        password_xpath: '//input[@name="password"]'
      })
      .eq('id', testAccount.id)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('❌ 更新失败:', updateError.message);
      console.error('   详细错误:', updateError);
      return;
    }

    console.log('✅ 更新成功!');
    console.log('   新名称:', updatedAccount.name);
    console.log('   用户名XPath:', updatedAccount.username_xpath);
    console.log('   密码XPath:', updatedAccount.password_xpath);

    // 恢复原名称
    console.log('\n🔄 恢复原名称...');
    const { error: restoreError } = await supabase
      .from('accounts')
      .update({ name: testAccount.name })
      .eq('id', testAccount.id)
      .eq('user_id', user.id);

    if (restoreError) {
      console.error('❌ 恢复失败:', restoreError.message);
    } else {
      console.log('✅ 名称已恢复');
    }

    console.log('\n🎉 账号更新测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('   详细错误:', error);
  }
}

testAccountUpdate().catch(console.error); 
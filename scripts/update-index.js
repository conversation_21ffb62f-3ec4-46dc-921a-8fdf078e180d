#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 创建目录
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// 生成时间线索引内容
function generateTimelineIndex() {
  const timeDir = path.join('docs', 'by-time');
  let content = '# 项目开发时间线\n\n';
  content += `*最后更新: ${new Date().toLocaleString()}*\n\n`;
  
  if (!fs.existsSync(timeDir)) return content;
  
  const years = fs.readdirSync(timeDir)
    .filter(item => {
      const fullPath = path.join(timeDir, item);
      return fs.statSync(fullPath).isDirectory() && item !== 'timeline.md';
    })
    .sort().reverse();
  
  years.forEach(year => {
    content += `## ${year}年\n\n`;
    
    const yearDir = path.join(timeDir, year);
    const projects = fs.readdirSync(yearDir)
      .filter(item => fs.statSync(path.join(yearDir, item)).isDirectory())
      .sort().reverse();
    
    projects.forEach(project => {
      const projectPath = path.join(yearDir, project);
      const docs = fs.readdirSync(projectPath).filter(f => f.endsWith('.md'));
      
      content += `### [${project}](./${year}/${project}/)\n`;
      content += `📁 包含文档: ${docs.map(d => d.replace('.md', '')).join(', ')}\n\n`;
    });
    
    content += '\n';
  });
  
  return content;
}

// 生成功能模块索引内容
function generateFeatureIndex() {
  const featureDir = path.join('docs', 'by-feature');
  let content = '# 项目功能模块总览\n\n';
  content += `*最后更新: ${new Date().toLocaleString()}*\n\n`;
  
  if (!fs.existsSync(featureDir)) return content;
  
  const moduleDescriptions = {
    '账号管理': '用户登录、权限控制、账号类型管理等功能',
    '验证码识别': 'OCR服务、滑块验证码、图像识别等技术',
    '插件管理': 'Chrome扩展、插件注册、ID获取等功能',
    '部署运维': 'Docker部署、Nginx配置、服务监控等运维'
  };
  
  const modules = fs.readdirSync(featureDir)
    .filter(item => {
      const fullPath = path.join(featureDir, item);
      return fs.statSync(fullPath).isDirectory() && item !== 'projects.md';
    });
  
  modules.forEach(module => {
    content += `## 🎯 ${module}\n\n`;
    
    if (moduleDescriptions[module]) {
      content += `${moduleDescriptions[module]}\n\n`;
    }
    
    const moduleDir = path.join(featureDir, module);
    const projects = fs.readdirSync(moduleDir)
      .filter(item => fs.statSync(path.join(moduleDir, item)).isDirectory())
      .sort().reverse();
    
    if (projects.length === 0) {
      content += '*暂无项目*\n\n';
      return;
    }
    
    projects.forEach(project => {
      const projectPath = path.join(moduleDir, project);
      const docs = fs.readdirSync(projectPath).filter(f => f.endsWith('.md'));
      
      content += `### [${project}](./${module}/${project}/)\n`;
      content += `📚 文档: ${docs.map(d => d.replace('.md', '')).join(', ')}\n\n`;
    });
    
    content += '\n';
  });
  
  return content;
}

// 生成总索引内容
function generateMainIndex() {
  const timeDir = path.join('docs', 'by-time');
  const featureDir = path.join('docs', 'by-feature');
  
  let content = `# 账号管理项目文档总览

> 📚 项目文档管理系统 - 双重索引结构
> 
> *最后更新: ${new Date().toLocaleString()}*

## 🔍 快速导航

### 按时间查找
想查看项目发展历程？按时间顺序浏览开发记录？

👉 [时间线索引](./by-time/timeline.md)

### 按功能查找
想了解特定功能模块？查找相关技术文档？

👉 [功能模块索引](./by-feature/projects.md)

## 📊 统计信息

`;

  // 生成统计信息
  if (fs.existsSync(timeDir)) {
    const totalYears = fs.readdirSync(timeDir)
      .filter(item => fs.statSync(path.join(timeDir, item)).isDirectory()).length;
    
    let totalProjects = 0;
    fs.readdirSync(timeDir).forEach(year => {
      const yearPath = path.join(timeDir, year);
      if (fs.statSync(yearPath).isDirectory()) {
        const projects = fs.readdirSync(yearPath)
          .filter(item => fs.statSync(path.join(yearPath, item)).isDirectory());
        totalProjects += projects.length;
      }
    });
    
    content += `- **总项目数**: ${totalProjects}个\n`;
    content += `- **时间跨度**: ${totalYears}年\n`;
  }
  
  if (fs.existsSync(featureDir)) {
    const totalModules = fs.readdirSync(featureDir)
      .filter(item => fs.statSync(path.join(featureDir, item)).isDirectory()).length;
    content += `- **功能模块**: ${totalModules}个\n`;
  }
  
  content += `- **文档类型**: PRD、TSD、DevLog\n\n`;

  content += `## 📋 文档类型说明

| 类型 | 说明 | 包含内容 |
|------|------|----------|
| **PRD** | 产品需求文档 | 需求背景、功能要求、验收标准 |
| **TSD** | 技术方案文档 | 架构设计、技术实现、风险评估 |
| **DevLog** | 开发日志 | 实现过程、问题解决、测试验证 |

## 🛠 工具使用

### 创建新文档
\`\`\`bash
node scripts/create-docs.js --name "功能名称" --type "账号管理" --date "2025-01-31"
\`\`\`

### 更新索引
\`\`\`bash
node scripts/update-index.js
\`\`\`

### 迁移现有文档
\`\`\`bash
node scripts/migrate-docs.js
\`\`\`

---

💡 **提示**: 这是一个动态维护的文档系统，索引会自动更新。如果发现问题或有改进建议，请更新对应的自动化脚本。`;

  return content;
}

// 更新所有索引文件
function updateAllIndexes() {
  console.log('🔄 开始更新所有索引文件...\n');
  
  try {
    // 确保目录存在
    ensureDir('docs/by-time');
    ensureDir('docs/by-feature');
    
    // 更新时间线索引
    console.log('📅 更新时间线索引...');
    const timelineContent = generateTimelineIndex();
    const timelineFile = path.join('docs', 'by-time', 'timeline.md');
    fs.writeFileSync(timelineFile, timelineContent);
    console.log(`✅ 创建: ${timelineFile}`);
    
    // 更新功能模块索引
    console.log('🎯 更新功能模块索引...');
    const featureContent = generateFeatureIndex();
    const featureFile = path.join('docs', 'by-feature', 'projects.md');
    fs.writeFileSync(featureFile, featureContent);
    console.log(`✅ 创建: ${featureFile}`);
    
    // 更新总索引
    console.log('📚 更新总索引...');
    const mainContent = generateMainIndex();
    const mainFile = path.join('docs', 'index.md');
    fs.writeFileSync(mainFile, mainContent);
    console.log(`✅ 创建: ${mainFile}`);
    
    console.log('\n✨ 所有索引更新完成！');
    
  } catch (error) {
    console.error('❌ 索引更新失败:', error.message);
    console.error(error.stack);
  }
}

// 检查文档完整性
function checkDocumentIntegrity() {
  console.log('🔍 检查文档完整性...\n');
  
  const issues = [];
  const timeDir = path.join('docs', 'by-time');
  
  if (!fs.existsSync(timeDir)) {
    console.log('⚠️  时间索引目录不存在');
    return;
  }
  
  // 检查每个项目是否有完整的PRD/TSD/DevLog
  fs.readdirSync(timeDir).forEach(year => {
    const yearPath = path.join(timeDir, year);
    if (!fs.statSync(yearPath).isDirectory()) return;
    
    fs.readdirSync(yearPath).forEach(project => {
      const projectPath = path.join(yearPath, project);
      if (!fs.statSync(projectPath).isDirectory()) return;
      
      const docs = fs.readdirSync(projectPath).filter(f => f.endsWith('.md'));
      const expectedDocs = ['prd.md', 'tsd.md', 'devlog.md'];
      
      expectedDocs.forEach(expected => {
        if (!docs.includes(expected)) {
          issues.push(`${year}/${project} 缺少 ${expected}`);
        }
      });
    });
  });
  
  if (issues.length === 0) {
    console.log('✅ 文档完整性检查通过！');
  } else {
    console.log('⚠️  发现以下问题:');
    issues.forEach(issue => console.log(`   - ${issue}`));
  }
  
  return issues;
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--check')) {
    checkDocumentIntegrity();
  } else {
    updateAllIndexes();
  }
}

if (require.main === module) {
  main();
}

module.exports = { 
  updateAllIndexes, 
  checkDocumentIntegrity,
  generateTimelineIndex,
  generateFeatureIndex,
  generateMainIndex
}; 
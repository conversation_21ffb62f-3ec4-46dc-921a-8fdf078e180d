#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始静态构建过程...');

// 1. 设置环境变量并构建
console.log('📦 正在构建静态版本...');
try {
  // Windows 系统使用 set，Unix 系统使用 export
  const isWindows = process.platform === 'win32';
  const envCommand = isWindows ? 'set BUILD_MODE=static &&' : 'BUILD_MODE=static';
  
  // 临时重命名配置文件
  const originalConfig = 'next.config.js';
  const staticConfig = 'next.config.static.js';
  
  // 备份原配置
  if (fs.existsSync(originalConfig)) {
    fs.copyFileSync(originalConfig, 'next.config.js.backup');
  }
  
  // 使用静态配置
  fs.copyFileSync(staticConfig, originalConfig);
  
  try {
    execSync(`npx next build`, { 
      stdio: 'inherit',
      env: { 
        ...process.env, 
        BUILD_MODE: 'static'
      }
    });
  } finally {
    // 恢复原配置
    if (fs.existsSync('next.config.js.backup')) {
      fs.copyFileSync('next.config.js.backup', originalConfig);
      fs.unlinkSync('next.config.js.backup');
    }
  }
  
  console.log('✅ 构建完成！');
  
  // 2. 检查输出目录
  const outDir = path.join(__dirname, '../out');
  if (fs.existsSync(outDir)) {
    console.log('📂 静态文件已生成到 out/ 目录');
    
    // 列出生成的文件
    const files = fs.readdirSync(outDir);
    console.log('生成的文件：');
    files.forEach(file => {
      console.log(`  - ${file}`);
    });
    
    // 检查是否有 index.html
    if (files.includes('index.html')) {
      console.log('✅ index.html 文件已成功生成');
    } else {
      console.log('❌ 未找到 index.html 文件');
    }
  } else {
    console.log('❌ 输出目录不存在');
  }
  
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
} 
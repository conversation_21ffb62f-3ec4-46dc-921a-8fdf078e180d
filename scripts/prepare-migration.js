/**
 * 数据迁移准备脚本
 * 将需要迁移到生产服务器(***************)的文件复制到临时目录
 * 
 * 使用方法:
 * node scripts/prepare-migration.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const MIGRATION_DIR = path.join(__dirname, '../migration-temp');
const DATABASE_DIR = path.join(__dirname, '../database');
const TARGET_SERVER = '***************';
const TARGET_DIR = '/home/<USER>/database';

// 确保迁移目录存在
if (fs.existsSync(MIGRATION_DIR)) {
  console.log('清理已存在的迁移目录...');
  fs.rmSync(MIGRATION_DIR, { recursive: true, force: true });
}

console.log('创建迁移临时目录...');
fs.mkdirSync(MIGRATION_DIR, { recursive: true });
fs.mkdirSync(path.join(MIGRATION_DIR, 'database'), { recursive: true });

// 复制数据库文件
console.log('复制数据库文件...');
if (fs.existsSync(DATABASE_DIR)) {
  const dbFiles = fs.readdirSync(DATABASE_DIR);
  
  dbFiles.forEach(file => {
    const sourcePath = path.join(DATABASE_DIR, file);
    const targetPath = path.join(MIGRATION_DIR, 'database', file);
    
    // 检查是否是文件
    if (fs.statSync(sourcePath).isFile()) {
      fs.copyFileSync(sourcePath, targetPath);
      console.log(`✅ 已复制: ${file}`);
    } else {
      console.log(`⚠️ 跳过目录: ${file}`);
    }
  });
} else {
  console.log('⚠️ 数据库目录不存在，跳过复制');
}

// 创建README文件
const readmeContent = `# 数据迁移文件

这些文件准备用于迁移到生产服务器(${TARGET_SERVER})。

## 迁移步骤

1. 将这些文件复制到生产服务器:

\`\`\`bash
scp -r database/* root@${TARGET_SERVER}:${TARGET_DIR}/
\`\`\`

2. 确认文件已成功复制:

\`\`\`bash
ssh root@${TARGET_SERVER} "ls -la ${TARGET_DIR}"
\`\`\`

3. 重启Docker容器以应用更改:

\`\`\`bash
ssh root@${TARGET_SERVER} "cd /home/<USER>"
\`\`\`

## 文件清单

`;

// 添加文件清单到README
let fileList = '';
if (fs.existsSync(path.join(MIGRATION_DIR, 'database'))) {
  const dbFiles = fs.readdirSync(path.join(MIGRATION_DIR, 'database'));
  dbFiles.forEach(file => {
    const filePath = path.join(MIGRATION_DIR, 'database', file);
    const stats = fs.statSync(filePath);
    const fileSizeKB = Math.ceil(stats.size / 1024);
    fileList += `- ${file} (${fileSizeKB} KB)\n`;
  });
}

fs.writeFileSync(path.join(MIGRATION_DIR, 'README.md'), readmeContent + fileList);

// 创建一键复制脚本
const scriptContent = `#!/bin/bash
echo "开始将数据库文件复制到${TARGET_SERVER}..."
scp -r database/* root@${TARGET_SERVER}:${TARGET_DIR}/
echo "复制完成，正在重启容器..."
ssh root@${TARGET_SERVER} "cd /home/<USER>"
echo "✅ 迁移完成!"
`;

fs.writeFileSync(path.join(MIGRATION_DIR, 'migrate.sh'), scriptContent);
fs.chmodSync(path.join(MIGRATION_DIR, 'migrate.sh'), '755');

// 创建Windows批处理脚本
const batchContent = `@echo off
echo 开始将数据库文件复制到${TARGET_SERVER}...
scp -r database/* root@${TARGET_SERVER}:${TARGET_DIR}/
echo 复制完成，正在重启容器...
ssh root@${TARGET_SERVER} "cd /home/<USER>"
echo ✅ 迁移完成!
pause
`;

fs.writeFileSync(path.join(MIGRATION_DIR, 'migrate.bat'), batchContent);

// 完成
console.log('\n✅ 迁移准备完成!');
console.log(`迁移文件已准备好，位于: ${MIGRATION_DIR}`);
console.log('您可以手动将这些文件复制到生产服务器，或使用提供的脚本。');
console.log('\n在Linux/Mac上:');
console.log(`cd ${MIGRATION_DIR} && ./migrate.sh`);
console.log('\n在Windows上:');
console.log(`cd ${MIGRATION_DIR} && migrate.bat`);

// 尝试打开文件夹
try {
  if (process.platform === 'win32') {
    execSync(`explorer "${MIGRATION_DIR}"`);
  } else if (process.platform === 'darwin') {
    execSync(`open "${MIGRATION_DIR}"`);
  } else {
    execSync(`xdg-open "${MIGRATION_DIR}"`);
  }
  console.log('\n已为您打开迁移文件夹。');
} catch (error) {
  console.log('\n无法自动打开文件夹，请手动导航到该目录。');
} 
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function testCaptchaConfig() {
  console.log('🧪 测试验证码配置...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ 环境变量未配置');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 登录用户
    console.log('👤 登录用户...');
    const { error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginError) {
      console.error('❌ 登录失败:', loginError.message);
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    console.log('✅ 用户:', user.email);

    // 查询所有账号的验证码配置
    console.log('\n📋 查询账号验证码配置...');
    const { data: accounts, error: queryError } = await supabase
      .from('accounts')
      .select('id, name, login_url, captcha_xpath, captcha_input_xpath')
      .eq('user_id', user.id);

    if (queryError) {
      console.error('❌ 查询失败:', queryError.message);
      return;
    }

    console.log(`\n📊 找到 ${accounts.length} 个账号:`);
    
    accounts.forEach((account, index) => {
      console.log(`\n${index + 1}. ${account.name}`);
      console.log(`   URL: ${account.login_url}`);
      console.log(`   验证码图片XPath: ${account.captcha_xpath || '(未设置)'}`);
      console.log(`   验证码输入框XPath: ${account.captcha_input_xpath || '(未设置)'}`);
      
      // 验证配置
      const hasValidConfig = account.captcha_xpath && account.captcha_input_xpath;
      console.log(`   配置状态: ${hasValidConfig ? '✅ 完整' : '❌ 不完整'}`);
      
      if (account.captcha_xpath && account.captcha_xpath.includes('input')) {
        console.log(`   ⚠️  警告: 验证码图片XPath似乎指向输入框`);
      }
    });

    console.log('\n🎯 验证码识别流程说明:');
    console.log('1. captcha_xpath 用于定位验证码图片进行OCR识别');
    console.log('2. captcha_input_xpath 用于定位验证码输入框进行填充');
    console.log('3. 两个字段都需要正确配置才能实现自动识别填充');

    console.log('\n🎉 测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testCaptchaConfig(); 
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 默认的功能模块配置
const DEFAULT_MODULES = {
  '账号管理': ['账号', '登录', '权限', '管理', 'account', 'login', 'auth', '用户'],
  '验证码识别': ['验证码', '滑块', 'ocr', 'captcha', '识别', 'baidu', '图像'],
  '插件管理': ['插件', 'plugin', 'chrome', '扩展', 'extension', 'id获取'],
  '部署运维': ['docker', 'nginx', '部署', 'deploy', '服务', '静态', '环境', '配置']
};

// 配置文件路径
const CONFIG_FILE = path.join('docs', 'modules-config.json');

// 获取模块配置
function getModulesConfig() {
  if (fs.existsSync(CONFIG_FILE)) {
    try {
      const config = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
      return { ...DEFAULT_MODULES, ...config.customModules };
    } catch (error) {
      console.log('⚠️  配置文件格式错误，使用默认配置');
    }
  }
  
  return DEFAULT_MODULES;
}

// 保存模块配置
function saveModulesConfig(customModules) {
  const config = {
    lastUpdated: new Date().toISOString(),
    customModules: customModules
  };
  
  // 确保docs目录存在
  if (!fs.existsSync('docs')) {
    fs.mkdirSync('docs', { recursive: true });
  }
  
  fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
  console.log(`✅ 模块配置已保存: ${CONFIG_FILE}`);
}

// 从现有文档学习功能模块
function learnFromExistingDocs() {
  const archiveDir = path.join('docs', 'archive', 'by-feature');
  const learnedModules = {};
  
  if (!fs.existsSync(archiveDir)) {
    return learnedModules;
  }
  
  // 扫描现有的功能模块目录
  const existingModules = fs.readdirSync(archiveDir)
    .filter(item => fs.statSync(path.join(archiveDir, item)).isDirectory());
  
  existingModules.forEach(module => {
    if (!DEFAULT_MODULES[module]) {
      // 这是一个新发现的模块，尝试从文档中提取关键词
      const keywords = extractKeywordsFromModule(module);
      if (keywords.length > 0) {
        learnedModules[module] = keywords;
        console.log(`🧠 学习到新模块: ${module} -> [${keywords.join(', ')}]`);
      }
    }
  });
  
  return learnedModules;
}

// 从模块文档中提取关键词
function extractKeywordsFromModule(module) {
  const moduleDir = path.join('docs', 'archive', 'by-feature', module);
  const keywords = new Set();
  
  if (!fs.existsSync(moduleDir)) {
    return [];
  }
  
  // 扫描该模块下的所有项目
  const projects = fs.readdirSync(moduleDir)
    .filter(item => fs.statSync(path.join(moduleDir, item)).isDirectory());
  
  projects.forEach(project => {
    // 从项目名称中提取关键词
    const projectKeywords = extractKeywordsFromText(project);
    projectKeywords.forEach(keyword => keywords.add(keyword));
    
    // 从文档内容中提取关键词
    const projectDir = path.join(moduleDir, project);
    ['prd.md', 'tsd.md', 'devlog.md'].forEach(docFile => {
      const docPath = path.join(projectDir, docFile);
      if (fs.existsSync(docPath)) {
        const content = fs.readFileSync(docPath, 'utf8');
        const contentKeywords = extractKeywordsFromText(content);
        contentKeywords.slice(0, 5).forEach(keyword => keywords.add(keyword)); // 只取前5个
      }
    });
  });
  
  return Array.from(keywords).slice(0, 10); // 最多保留10个关键词
}

// 从文本中提取关键词
function extractKeywordsFromText(text) {
  // 简单的关键词提取逻辑
  const cleanText = text.toLowerCase()
    .replace(/[^\u4e00-\u9fa5a-zA-Z\s]/g, ' ') // 只保留中文、英文和空格
    .split(/\s+/)
    .filter(word => word.length >= 2 && word.length <= 10) // 长度过滤
    .filter(word => !['的', '和', '是', '在', '有', 'the', 'and', 'is', 'in', 'of'].includes(word)); // 停用词过滤
  
  // 统计词频
  const wordCount = {};
  cleanText.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });
  
  // 按频率排序，返回前10个
  return Object.entries(wordCount)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([word]) => word);
}

// 智能分类功能
function classifyIntelligently(name, description = '') {
  const text = (name + ' ' + description).toLowerCase();
  const modules = getModulesConfig();
  const scores = {};
  
  // 计算每个模块的匹配分数
  Object.entries(modules).forEach(([module, keywords]) => {
    let score = 0;
    keywords.forEach(keyword => {
      if (text.includes(keyword.toLowerCase())) {
        score += 1;
      }
    });
    scores[module] = score;
  });
  
  // 找到最高分的模块
  const bestMatch = Object.entries(scores).reduce((best, [module, score]) => {
    return score > best.score ? { module, score } : best;
  }, { module: '其他', score: 0 });
  
  // 如果最高分太低，可能是新模块
  if (bestMatch.score === 0) {
    return suggestNewModule(name, description);
  }
  
  return bestMatch.module;
}

// 建议新模块
function suggestNewModule(name, description = '') {
  const suggestedKeywords = extractKeywordsFromText(name + ' ' + description);
  
  if (suggestedKeywords.length >= 2) {
    const newModuleName = inferModuleName(name, description);
    
    console.log(`💡 检测到可能的新功能模块:`);
    console.log(`   模块名: ${newModuleName}`);
    console.log(`   关键词: [${suggestedKeywords.slice(0, 5).join(', ')}]`);
    console.log(`   是否添加到配置? (可手动编辑 ${CONFIG_FILE})`);
    
    // 自动添加到配置（可选）
    const currentConfig = getModulesConfig();
    if (!currentConfig[newModuleName]) {
      const customModules = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8') || '{"customModules":{}}');
      customModules.customModules = customModules.customModules || {};
      customModules.customModules[newModuleName] = suggestedKeywords.slice(0, 5);
      saveModulesConfig(customModules.customModules);
      
      return newModuleName;
    }
  }
  
  return '其他';
}

// 推断模块名称
function inferModuleName(name, description) {
  const text = name + ' ' + description;
  
  // 简单的模块名推断逻辑
  if (text.includes('支付') || text.includes('订单')) return '支付管理';
  if (text.includes('消息') || text.includes('通知')) return '消息系统';
  if (text.includes('数据') || text.includes('统计')) return '数据分析';
  if (text.includes('搜索') || text.includes('检索')) return '搜索功能';
  if (text.includes('文件') || text.includes('上传')) return '文件管理';
  if (text.includes('API') || text.includes('接口')) return 'API管理';
  
  // 默认使用功能的第一个关键词
  const keywords = extractKeywordsFromText(text);
  return keywords.length > 0 ? keywords[0] + '管理' : '其他功能';
}

// 更新模块配置
function updateModuleConfig(moduleName, keywords) {
  const config = fs.existsSync(CONFIG_FILE) ? 
    JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8')) : 
    { customModules: {} };
  
  config.customModules = config.customModules || {};
  config.customModules[moduleName] = keywords;
  
  saveModulesConfig(config.customModules);
}

// 列出所有模块
function listAllModules() {
  const modules = getModulesConfig();
  
  console.log('📋 当前功能模块配置:\n');
  
  console.log('🔧 默认模块:');
  Object.entries(DEFAULT_MODULES).forEach(([module, keywords]) => {
    console.log(`   ${module}: [${keywords.join(', ')}]`);
  });
  
  const customModules = getCustomModules();
  if (Object.keys(customModules).length > 0) {
    console.log('\n🆕 自定义模块:');
    Object.entries(customModules).forEach(([module, keywords]) => {
      console.log(`   ${module}: [${keywords.join(', ')}]`);
    });
  }
}

// 获取自定义模块
function getCustomModules() {
  if (fs.existsSync(CONFIG_FILE)) {
    try {
      const config = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
      return config.customModules || {};
    } catch (error) {
      return {};
    }
  }
  return {};
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'classify':
      const result = classifyIntelligently(args[1], args[2]);
      console.log(`分类结果: ${result}`);
      break;
      
    case 'learn':
      const learned = learnFromExistingDocs();
      if (Object.keys(learned).length > 0) {
        const currentCustom = getCustomModules();
        const merged = { ...currentCustom, ...learned };
        saveModulesConfig(merged);
      } else {
        console.log('🤷 没有发现新的功能模块');
      }
      break;
      
    case 'add':
      const moduleName = args[1];
      const keywords = args.slice(2);
      if (moduleName && keywords.length > 0) {
        updateModuleConfig(moduleName, keywords);
      } else {
        console.log('使用方法: node smart-classifier.js add "模块名" "关键词1" "关键词2" ...');
      }
      break;
      
    case 'list':
      listAllModules();
      break;
      
    default:
      console.log(`
智能功能模块分类器

使用方法:
  node smart-classifier.js classify "功能名称" "描述"  # 分类功能
  node smart-classifier.js learn                      # 从现有文档学习
  node smart-classifier.js add "模块名" "关键词1" ...   # 添加新模块
  node smart-classifier.js list                       # 列出所有模块
      `);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  classifyIntelligently,
  learnFromExistingDocs,
  updateModuleConfig,
  getModulesConfig,
  listAllModules
}; 
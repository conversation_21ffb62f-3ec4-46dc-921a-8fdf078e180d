/**
 * 高级数据迁移准备脚本
 * 将需要迁移到生产服务器(***************)的文件复制到临时目录
 * 支持选择性迁移不同类型的文件
 * 
 * 使用方法:
 * node scripts/prepare-migration-advanced.js [--all] [--db] [--accounts] [--config]
 * 
 * 参数:
 * --all: 迁移所有数据（默认）
 * --db: 只迁移数据库文件
 * --accounts: 只迁移账号相关文件
 * --config: 只迁移配置文件
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 解析命令行参数
const args = process.argv.slice(2);
const options = {
  all: args.includes('--all') || args.length === 0,
  db: args.includes('--db'),
  accounts: args.includes('--accounts'),
  config: args.includes('--config')
};

// 配置
const MIGRATION_DIR = path.join(__dirname, '../migration-temp');
const DATABASE_DIR = path.join(__dirname, '../database');
const TARGET_SERVER = '***************';
const TARGET_DIR = '/home/<USER>';

// 确保迁移目录存在
if (fs.existsSync(MIGRATION_DIR)) {
  console.log('清理已存在的迁移目录...');
  fs.rmSync(MIGRATION_DIR, { recursive: true, force: true });
}

console.log('创建迁移临时目录...');
fs.mkdirSync(MIGRATION_DIR, { recursive: true });

// 复制数据库文件
if (options.all || options.db) {
  console.log('复制数据库文件...');
  fs.mkdirSync(path.join(MIGRATION_DIR, 'database'), { recursive: true });
  
  if (fs.existsSync(DATABASE_DIR)) {
    const dbFiles = fs.readdirSync(DATABASE_DIR);
    
    dbFiles.forEach(file => {
      const sourcePath = path.join(DATABASE_DIR, file);
      const targetPath = path.join(MIGRATION_DIR, 'database', file);
      
      // 检查是否是文件
      if (fs.statSync(sourcePath).isFile()) {
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`✅ 已复制数据库文件: ${file}`);
      } else {
        // 如果是目录，递归复制
        if (!fs.existsSync(targetPath)) {
          fs.mkdirSync(targetPath, { recursive: true });
        }
        
        const nestedFiles = fs.readdirSync(sourcePath);
        nestedFiles.forEach(nestedFile => {
          const nestedSource = path.join(sourcePath, nestedFile);
          const nestedTarget = path.join(targetPath, nestedFile);
          
          if (fs.statSync(nestedSource).isFile()) {
            fs.copyFileSync(nestedSource, nestedTarget);
            console.log(`✅ 已复制数据库子文件: ${file}/${nestedFile}`);
          }
        });
      }
    });
  } else {
    console.log('⚠️ 数据库目录不存在，跳过复制');
  }
}

// 复制账号相关文件
if (options.all || options.accounts) {
  console.log('复制账号相关文件...');
  const accountsDir = path.join(DATABASE_DIR, 'accounts');
  const targetAccountsDir = path.join(MIGRATION_DIR, 'database', 'accounts');
  
  if (fs.existsSync(accountsDir)) {
    if (!fs.existsSync(targetAccountsDir)) {
      fs.mkdirSync(targetAccountsDir, { recursive: true });
    }
    
    const accountFiles = fs.readdirSync(accountsDir);
    accountFiles.forEach(file => {
      const sourcePath = path.join(accountsDir, file);
      const targetPath = path.join(targetAccountsDir, file);
      
      if (fs.statSync(sourcePath).isFile()) {
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`✅ 已复制账号文件: ${file}`);
      }
    });
  } else {
    console.log('⚠️ 账号目录不存在，跳过复制');
  }
}

// 复制配置文件
if (options.all || options.config) {
  console.log('复制配置文件...');
  const configFiles = [
    { src: path.join(__dirname, '../lib/config.js'), dest: path.join(MIGRATION_DIR, 'lib/config.js') },
    { src: path.join(__dirname, '../chrome-extension/config.js'), dest: path.join(MIGRATION_DIR, 'chrome-extension/config.js') }
  ];
  
  configFiles.forEach(({ src, dest }) => {
    if (fs.existsSync(src)) {
      const destDir = path.dirname(dest);
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }
      
      fs.copyFileSync(src, dest);
      console.log(`✅ 已复制配置文件: ${path.basename(src)}`);
    } else {
      console.log(`⚠️ 配置文件不存在: ${src}`);
    }
  });
}

// 创建README文件
const readmeContent = `# 数据迁移文件

这些文件准备用于迁移到生产服务器(${TARGET_SERVER})。

## 迁移步骤

1. 将数据库文件复制到生产服务器:

\`\`\`bash
scp -r database/* root@${TARGET_SERVER}:${TARGET_DIR}/database/
\`\`\`

2. 如果有配置文件，复制它们:

\`\`\`bash
# 如果有lib/config.js
scp -r lib/config.js root@${TARGET_SERVER}:${TARGET_DIR}/lib/

# 如果有chrome-extension/config.js
scp -r chrome-extension/config.js root@${TARGET_SERVER}:${TARGET_DIR}/chrome-extension/
\`\`\`

3. 确认文件已成功复制:

\`\`\`bash
ssh root@${TARGET_SERVER} "ls -la ${TARGET_DIR}/database"
\`\`\`

4. 重启Docker容器以应用更改:

\`\`\`bash
ssh root@${TARGET_SERVER} "cd ${TARGET_DIR} && docker-compose restart"
\`\`\`

## 文件清单

`;

// 添加文件清单到README
let fileList = '';

// 添加数据库文件
if (fs.existsSync(path.join(MIGRATION_DIR, 'database'))) {
  fileList += '### 数据库文件\n\n';
  const addFilesToList = (dir, prefix = '') => {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      if (fs.statSync(filePath).isFile()) {
        const stats = fs.statSync(filePath);
        const fileSizeKB = Math.ceil(stats.size / 1024);
        fileList += `- ${prefix}${file} (${fileSizeKB} KB)\n`;
      } else {
        addFilesToList(filePath, `${prefix}${file}/`);
      }
    });
  };
  
  addFilesToList(path.join(MIGRATION_DIR, 'database'));
  fileList += '\n';
}

// 添加配置文件
if (fs.existsSync(path.join(MIGRATION_DIR, 'lib')) || fs.existsSync(path.join(MIGRATION_DIR, 'chrome-extension'))) {
  fileList += '### 配置文件\n\n';
  
  if (fs.existsSync(path.join(MIGRATION_DIR, 'lib/config.js'))) {
    const stats = fs.statSync(path.join(MIGRATION_DIR, 'lib/config.js'));
    const fileSizeKB = Math.ceil(stats.size / 1024);
    fileList += `- lib/config.js (${fileSizeKB} KB)\n`;
  }
  
  if (fs.existsSync(path.join(MIGRATION_DIR, 'chrome-extension/config.js'))) {
    const stats = fs.statSync(path.join(MIGRATION_DIR, 'chrome-extension/config.js'));
    const fileSizeKB = Math.ceil(stats.size / 1024);
    fileList += `- chrome-extension/config.js (${fileSizeKB} KB)\n`;
  }
  
  fileList += '\n';
}

fs.writeFileSync(path.join(MIGRATION_DIR, 'README.md'), readmeContent + fileList);

// 创建一键复制脚本
const scriptContent = `#!/bin/bash
echo "开始将文件复制到${TARGET_SERVER}..."

# 复制数据库文件
if [ -d "database" ]; then
  echo "复制数据库文件..."
  ssh root@${TARGET_SERVER} "mkdir -p ${TARGET_DIR}/database"
  scp -r database/* root@${TARGET_SERVER}:${TARGET_DIR}/database/
fi

# 复制配置文件
if [ -d "lib" ]; then
  echo "复制lib配置文件..."
  ssh root@${TARGET_SERVER} "mkdir -p ${TARGET_DIR}/lib"
  scp -r lib/config.js root@${TARGET_SERVER}:${TARGET_DIR}/lib/
fi

if [ -d "chrome-extension" ]; then
  echo "复制chrome-extension配置文件..."
  ssh root@${TARGET_SERVER} "mkdir -p ${TARGET_DIR}/chrome-extension"
  scp -r chrome-extension/config.js root@${TARGET_SERVER}:${TARGET_DIR}/chrome-extension/
fi

echo "复制完成，正在重启容器..."
ssh root@${TARGET_SERVER} "cd ${TARGET_DIR} && docker-compose restart"
echo "✅ 迁移完成!"
`;

fs.writeFileSync(path.join(MIGRATION_DIR, 'migrate.sh'), scriptContent);
fs.chmodSync(path.join(MIGRATION_DIR, 'migrate.sh'), '755');

// 创建Windows批处理脚本
const batchContent = `@echo off
echo 开始将文件复制到${TARGET_SERVER}...

rem 复制数据库文件
if exist database (
  echo 复制数据库文件...
  ssh root@${TARGET_SERVER} "mkdir -p ${TARGET_DIR}/database"
  scp -r database/* root@${TARGET_SERVER}:${TARGET_DIR}/database/
)

rem 复制配置文件
if exist lib (
  echo 复制lib配置文件...
  ssh root@${TARGET_SERVER} "mkdir -p ${TARGET_DIR}/lib"
  scp -r lib/config.js root@${TARGET_SERVER}:${TARGET_DIR}/lib/
)

if exist chrome-extension (
  echo 复制chrome-extension配置文件...
  ssh root@${TARGET_SERVER} "mkdir -p ${TARGET_DIR}/chrome-extension"
  scp -r chrome-extension/config.js root@${TARGET_SERVER}:${TARGET_DIR}/chrome-extension/
)

echo 复制完成，正在重启容器...
ssh root@${TARGET_SERVER} "cd ${TARGET_DIR} && docker-compose restart"
echo ✅ 迁移完成!
pause
`;

fs.writeFileSync(path.join(MIGRATION_DIR, 'migrate.bat'), batchContent);

// 完成
console.log('\n✅ 迁移准备完成!');
console.log(`迁移文件已准备好，位于: ${MIGRATION_DIR}`);
console.log('您可以手动将这些文件复制到生产服务器，或使用提供的脚本。');
console.log('\n在Linux/Mac上:');
console.log(`cd ${MIGRATION_DIR} && ./migrate.sh`);
console.log('\n在Windows上:');
console.log(`cd ${MIGRATION_DIR} && migrate.bat`);

// 尝试打开文件夹
try {
  if (process.platform === 'win32') {
    execSync(`explorer "${MIGRATION_DIR}"`);
  } else if (process.platform === 'darwin') {
    execSync(`open "${MIGRATION_DIR}"`);
  } else {
    execSync(`xdg-open "${MIGRATION_DIR}"`);
  }
  console.log('\n已为您打开迁移文件夹。');
} catch (error) {
  console.log('\n无法自动打开文件夹，请手动导航到该目录。');
} 
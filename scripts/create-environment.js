#!/usr/bin/env node

/**
 * 环境创建脚本
 * 
 * 使用方法:
 * node scripts/create-environment.js "环境名称" "用户邮箱"
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase 配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:8000';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// 创建 Supabase 客户端实例
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 创建环境函数
async function createEnvironment(name, userId) {
  if (!name) {
    console.error('请提供环境名称');
    process.exit(1);
  }

  if (!userId) {
    console.error('请提供用户ID');
    process.exit(1);
  }

  try {
    const { data, error } = await supabase
      .from('environments')
      .insert({
        user_id: userId,
        name
      })
      .select()
      .single();

    if (error) throw error;
    console.log(`成功创建环境: ${name}，ID: ${data.id}`);
    return data;
  } catch (error) {
    console.error(`创建环境失败: ${error.message}`);
    process.exit(1);
  }
}

// 获取用户ID
async function getUserId(email) {
  if (!email) {
    console.error('请提供用户邮箱');
    process.exit(1);
  }

  try {
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (error) throw error;
    if (!data) throw new Error(`未找到邮箱为 ${email} 的用户`);
    
    return data.id;
  } catch (error) {
    console.error(`获取用户ID失败: ${error.message}`);
    process.exit(1);
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const environmentName = args[0];
  const userEmail = args[1];

  if (!environmentName || !userEmail) {
    console.log('使用方法: node scripts/create-environment.js "环境名称" "用户邮箱"');
    process.exit(1);
  }

  try {
    const userId = await getUserId(userEmail);
    await createEnvironment(environmentName, userId);
  } catch (error) {
    console.error('执行失败:', error);
    process.exit(1);
  }
}

main(); 
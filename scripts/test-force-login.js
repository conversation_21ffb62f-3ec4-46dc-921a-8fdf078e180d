#!/usr/bin/env node

/**
 * 测试强制登录功能 - 直接操作Supabase版本
 * 验证新的toggleForceLogin函数是否正常工作
 */

const { createClient } = require('@supabase/supabase-js');

// 模拟浏览器环境
global.window = {};
global.document = {};

async function testForceLogin() {
  console.log('🧪 测试强制登录功能 (直接操作Supabase版本)...\n');

  // 使用实际的 Supabase 配置
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://192.168.202.230:8000';
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

  console.log('🔧 使用 Supabase 配置:');
  console.log('   URL:', supabaseUrl);
  console.log('   Key:', supabaseKey.substring(0, 20) + '...');

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 登录用户
    console.log('👤 登录用户...');
    const { error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginError) {
      console.error('❌ 登录失败:', loginError.message);
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    console.log('✅ 用户:', user.email);

    // 查询现有账号
    console.log('\n📋 查询现有账号...');
    const { data: accounts, error: queryError } = await supabase
      .from('accounts')
      .select('*')
      .eq('user_id', user.id)
      .limit(1);

    if (queryError) {
      console.error('❌ 查询失败:', queryError.message);
      return;
    }

    if (accounts.length === 0) {
      console.log('❌ 没有找到账号进行测试');
      return;
    }

    const testAccount = accounts[0];
    console.log('📝 找到测试账号:', testAccount.name);
    console.log('   ID:', testAccount.id);
    console.log('   当前强制登录状态:', testAccount.force_login);

    // 测试切换强制登录状态
    console.log('\n🔄 测试切换强制登录状态...');
    
    // 模拟toggleForceLogin函数
    async function toggleForceLogin(id) {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('用户未登录');

      // 检查是否为超管
      const isAdmin = user.email === '<EMAIL>';
      
      // 1. 先获取当前账号的强制登录状态
      let query = supabase
        .from('accounts')
        .select('force_login, user_id')
        .eq('id', id)
        .eq('is_deleted', false)
        .single();

      // 权限控制：超管可以更新任何账号，普通用户只能更新自己的账号
      if (!isAdmin) {
        query = query.eq('user_id', user.id);
      }

      const { data: currentAccount, error: fetchError } = await query;
      
      if (fetchError) throw fetchError;
      if (!currentAccount) throw new Error('账号不存在或无权限访问');

      // 2. 切换强制登录状态
      const newForceLogin = !currentAccount.force_login;
      
      // 3. 更新强制登录状态
      const { data, error } = await supabase
        .from('accounts')
        .update({ 
          force_login: newForceLogin,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('is_deleted', false)
        .select()
        .single();

      if (error) throw error;
      return data;
    }

    // 执行切换
    const updatedAccount = await toggleForceLogin(testAccount.id);
    console.log('✅ 切换成功!');
    console.log('   新状态:', updatedAccount.force_login);
    console.log('   更新时间:', updatedAccount.updated_at);

    // 验证切换结果
    console.log('\n🔍 验证切换结果...');
    const { data: verifyAccount } = await supabase
      .from('accounts')
      .select('force_login, updated_at')
      .eq('id', testAccount.id)
      .single();

    console.log('   数据库中的状态:', verifyAccount.force_login);
    console.log('   数据库中的更新时间:', verifyAccount.updated_at);

    if (verifyAccount.force_login === updatedAccount.force_login) {
      console.log('✅ 验证成功: 状态已正确更新');
    } else {
      console.log('❌ 验证失败: 状态不一致');
    }

    console.log('\n🎉 强制登录功能测试完成!');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testForceLogin(); 
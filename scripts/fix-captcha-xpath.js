const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function fixCaptchaXPath() {
  console.log('🔧 修复验证码XPath配置...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ 环境变量未配置');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 登录用户
    console.log('👤 登录用户...');
    const { error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginError) {
      console.error('❌ 登录失败:', loginError.message);
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    console.log('✅ 用户:', user.email);

    // 查找需要修复的账号
    console.log('\n🔍 查找需要修复的账号...');
    const { data: accounts, error: queryError } = await supabase
      .from('accounts')
      .select('*')
      .eq('user_id', user.id)
      .or('captcha_xpath.like.%input%,captcha_input_xpath.is.null');

    if (queryError) {
      console.error('❌ 查询失败:', queryError.message);
      return;
    }

    console.log(`📋 找到 ${accounts.length} 个需要修复的账号`);

    for (const account of accounts) {
      console.log(`\n🔧 修复账号: ${account.name}`);
      console.log(`   当前验证码图片XPath: ${account.captcha_xpath || '(空)'}`);
      console.log(`   当前验证码输入框XPath: ${account.captcha_input_xpath || '(空)'}`);

      // 修复配置
      const updateData = {};
      
      // 如果 captcha_xpath 指向的是输入框，需要修复
      if (account.captcha_xpath && account.captcha_xpath.includes('input')) {
        // 将错误的输入框XPath移动到正确的字段
        if (!account.captcha_input_xpath) {
          updateData.captcha_input_xpath = account.captcha_xpath;
        }
        // 设置正确的验证码图片XPath
        updateData.captcha_xpath = '//img[contains(@src,"captcha") or contains(@src,"verify") or contains(@alt,"验证码")]';
      }

      // 如果没有验证码输入框XPath，设置默认值
      if (!account.captcha_input_xpath && !updateData.captcha_input_xpath) {
        updateData.captcha_input_xpath = '//input[@placeholder="请输入验证码"]';
      }

      if (Object.keys(updateData).length > 0) {
        const { error: updateError } = await supabase
          .from('accounts')
          .update(updateData)
          .eq('id', account.id)
          .eq('user_id', user.id);

        if (updateError) {
          console.error(`❌ 更新失败:`, updateError.message);
        } else {
          console.log(`✅ 更新成功:`);
          if (updateData.captcha_xpath) {
            console.log(`   新验证码图片XPath: ${updateData.captcha_xpath}`);
          }
          if (updateData.captcha_input_xpath) {
            console.log(`   新验证码输入框XPath: ${updateData.captcha_input_xpath}`);
          }
        }
      } else {
        console.log(`✅ 配置已正确，无需修复`);
      }
    }

    console.log('\n🎉 验证码XPath修复完成！');

  } catch (error) {
    console.error('❌ 修复失败:', error.message);
  }
}

fixCaptchaXPath(); 
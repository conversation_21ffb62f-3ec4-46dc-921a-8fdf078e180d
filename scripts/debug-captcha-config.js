const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function debugCaptchaConfig() {
  console.log('🔍 调试验证码配置...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ 环境变量未配置');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 登录用户
    console.log('👤 登录用户...');
    const { error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginError) {
      console.error('❌ 登录失败:', loginError.message);
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    console.log('✅ 用户:', user.email);

    // 查询所有账号
    console.log('\n📋 查询所有账号的验证码配置...');
    const { data: accounts, error: queryError } = await supabase
      .from('accounts')
      .select('id, name, login_url, captcha_xpath, captcha_input_xpath')
      .eq('user_id', user.id);

    if (queryError) {
      console.error('❌ 查询失败:', queryError.message);
      
      // 如果是字段不存在的错误，尝试不查询 captcha_input_xpath
      if (queryError.message.includes('captcha_input_xpath')) {
        console.log('\n🔄 字段不存在，尝试查询基本信息...');
        const { data: basicAccounts, error: basicError } = await supabase
          .from('accounts')
          .select('id, name, login_url, captcha_xpath')
          .eq('user_id', user.id);

        if (basicError) {
          console.error('❌ 基本查询也失败:', basicError.message);
          return;
        }

        console.log('\n📊 当前账号配置:');
        basicAccounts.forEach(account => {
          console.log(`\n🔹 ${account.name}`);
          console.log(`   ID: ${account.id}`);
          console.log(`   URL: ${account.login_url}`);
          console.log(`   验证码XPath: ${account.captcha_xpath || '(未设置)'}`);
          console.log(`   验证码输入框XPath: (字段不存在)`);
        });

        console.log('\n⚠️  需要先添加 captcha_input_xpath 字段到数据库');
        console.log('请在Supabase SQL编辑器中执行:');
        console.log('ALTER TABLE public.accounts ADD COLUMN IF NOT EXISTS captcha_input_xpath TEXT;');
      }
      return;
    }

    console.log('\n📊 当前账号配置:');
    accounts.forEach(account => {
      console.log(`\n🔹 ${account.name}`);
      console.log(`   ID: ${account.id}`);
      console.log(`   URL: ${account.login_url}`);
      console.log(`   验证码图片XPath: ${account.captcha_xpath || '(未设置)'}`);
      console.log(`   验证码输入框XPath: ${account.captcha_input_xpath || '(未设置)'}`);
      
      // 检查配置是否正确
      if (account.captcha_xpath && account.captcha_xpath.includes('input')) {
        console.log(`   ⚠️  验证码图片XPath配置错误 - 指向了输入框`);
      }
      if (!account.captcha_input_xpath && account.captcha_xpath) {
        console.log(`   ⚠️  缺少验证码输入框XPath配置`);
      }
    });

    // 提供修复建议
    const problemAccounts = accounts.filter(acc => 
      (acc.captcha_xpath && acc.captcha_xpath.includes('input')) || 
      (!acc.captcha_input_xpath && acc.captcha_xpath)
    );

    if (problemAccounts.length > 0) {
      console.log('\n🔧 修复建议:');
      problemAccounts.forEach(account => {
        console.log(`\n📝 ${account.name}:`);
        if (account.captcha_xpath && account.captcha_xpath.includes('input')) {
          console.log(`   - 将验证码图片XPath改为: //img[contains(@src,"captcha") or contains(@src,"verify") or contains(@alt,"验证码")]`);
          console.log(`   - 将验证码输入框XPath设为: ${account.captcha_xpath}`);
        } else if (!account.captcha_input_xpath) {
          console.log(`   - 添加验证码输入框XPath: //input[@placeholder="请输入验证码"]`);
        }
      });
    }

    console.log('\n🎉 调试完成！');

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
  }
}

debugCaptchaConfig(); 
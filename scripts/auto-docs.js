#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 自动创建新需求文档
function createActiveDoc(name, module, description = '') {
  const activeDir = path.join('docs', 'active', name);
  
  if (!fs.existsSync(activeDir)) {
    fs.mkdirSync(activeDir, { recursive: true });
  }
  
  const date = new Date().toISOString().split('T')[0];
  
  // PRD模板
  const prdContent = `# ${name} - PRD

## 基本信息
- 创建时间: ${date}
- 状态: 开发中
- 模块: ${module}

## 需求描述
${description}

## 功能要求
待AI分析补充...

## 验收标准
待AI生成...

## 相关文档
- [TSD技术方案](./tsd.md)
- [DevLog开发日志](./devlog.md)
`;

  // TSD模板
  const tsdContent = `# ${name} - TSD

## 基本信息
- 创建时间: ${date}
- 对应PRD: [需求文档](./prd.md)
- 模块: ${module}

## 技术方案
待AI根据代码分析生成...

## 实现方案
开发过程中自动完善...

## 风险点
待AI自动识别...

## 相关文档
- [PRD需求文档](./prd.md)
- [DevLog开发日志](./devlog.md)
`;

  // DevLog模板
  const devlogContent = `# ${name} - DevLog

## 基本信息
- 创建时间: ${date}
- 对应PRD: [需求文档](./prd.md)
- 对应TSD: [技术方案](./tsd.md)

## ${date} 开发记录

### 实现功能
开始开发...

### 技术细节
待AI自动提取关键实现...

### 问题解决
待AI自动记录问题和解决过程...

## 相关文档
- [PRD需求文档](./prd.md)
- [TSD技术方案](./tsd.md)
`;

  // 写入文件
  fs.writeFileSync(path.join(activeDir, 'prd.md'), prdContent);
  fs.writeFileSync(path.join(activeDir, 'tsd.md'), tsdContent);
  fs.writeFileSync(path.join(activeDir, 'devlog.md'), devlogContent);
  
  console.log(`✅ 自动创建文档: docs/active/${name}/`);
  return activeDir;
}

// 更新DevLog
function updateDevLog(name, content) {
  const devlogPath = path.join('docs', 'active', name, 'devlog.md');
  
  if (!fs.existsSync(devlogPath)) {
    console.log(`⚠️  DevLog不存在: ${devlogPath}`);
    return;
  }
  
  const time = new Date().toLocaleTimeString();
  const existingContent = fs.readFileSync(devlogPath, 'utf8');
  const newEntry = `\n### ${time} 更新\n${content}\n`;
  
  fs.writeFileSync(devlogPath, existingContent + newEntry);
  console.log(`📝 更新DevLog: ${name}`);
}

// 归档完成的文档
function archiveDoc(name, module) {
  const activeDir = path.join('docs', 'active', name);
  
  if (!fs.existsSync(activeDir)) {
    console.log(`⚠️  活动文档不存在: ${activeDir}`);
    return;
  }
  
  const date = new Date().toISOString().split('T')[0];
  const year = date.split('-')[0];
  const month = date.split('-')[1];
  
  // 时间维度归档
  const timeArchiveDir = path.join('docs', 'archive', 'by-time', year, `${month}-${name}`);
  
  // 功能维度归档
  const featureArchiveDir = path.join('docs', 'archive', 'by-feature', module, `${date}-${name}`);
  
  // 确保目录存在
  fs.mkdirSync(timeArchiveDir, { recursive: true });
  fs.mkdirSync(featureArchiveDir, { recursive: true });
  
  // 移动文件
  ['prd.md', 'tsd.md', 'devlog.md'].forEach(file => {
    const srcPath = path.join(activeDir, file);
    const destPath = path.join(timeArchiveDir, file);
    
    if (fs.existsSync(srcPath)) {
      fs.renameSync(srcPath, destPath);
      
      // 在功能归档中创建符号链接
      const featureLinkPath = path.join(featureArchiveDir, file);
      try {
        if (process.platform === 'win32') {
          fs.linkSync(destPath, featureLinkPath);
        } else {
          const relativePath = path.relative(path.dirname(featureLinkPath), destPath);
          fs.symlinkSync(relativePath, featureLinkPath);
        }
      } catch (error) {
        fs.copyFileSync(destPath, featureLinkPath);
      }
    }
  });
  
  // 删除active目录
  fs.rmSync(activeDir, { recursive: true, force: true });
  
  console.log(`🗂️  文档已归档: ${name}`);
  return { timeArchiveDir, featureArchiveDir };
}

// 智能分类功能
function identifyModule(name, description = '') {
  const text = (name + ' ' + description).toLowerCase();
  
  // 默认模块映射
  const moduleMap = {
    '账号管理': ['账号', '登录', '权限', '管理', 'account', 'login', 'auth', '用户'],
    '验证码识别': ['验证码', '滑块', 'ocr', 'captcha', '识别', 'baidu', '图像'],
    '插件管理': ['插件', 'plugin', 'chrome', '扩展', 'extension'],
    '部署运维': ['docker', 'nginx', '部署', 'deploy', '服务', '静态', '环境', '配置'],
    '支付管理': ['支付', '订单', '交易', '结算', '费用'],
    '消息系统': ['消息', '通知', '推送', '邮件', 'sms'],
    '数据分析': ['数据', '统计', '报表', '分析', 'chart'],
    '文件管理': ['文件', '上传', '下载', '存储', 'upload']
  };
  
  // 尝试加载自定义配置
  try {
    const configPath = path.join('docs', 'modules-config.json');
    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      Object.assign(moduleMap, config.customModules || {});
    }
  } catch (error) {
    // 忽略配置加载错误
  }
  
  // 匹配模块
  for (const [module, keywords] of Object.entries(moduleMap)) {
    if (keywords.some(keyword => text.includes(keyword))) {
      return module;
    }
  }
  
  return '其他';
}

// 命令行接口
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'create':
      const name = args[1];
      const description = args[2] || '';
      const module = identifyModule(name, description);
      createActiveDoc(name, module, description);
      break;
      
    case 'update-devlog':
      updateDevLog(args[1], args[2]);
      break;
      
    case 'archive':
      const moduleName = identifyModule(args[1]);
      archiveDoc(args[1], moduleName);
      break;
      
    default:
      console.log(`
使用方法:
  node scripts/auto-docs.js create "功能名称" "描述"
  node scripts/auto-docs.js update-devlog "功能名称" "更新内容"
  node scripts/auto-docs.js archive "功能名称"
      `);
  }
}

if (require.main === module) {
  main();
}

module.exports = { createActiveDoc, updateDevLog, archiveDoc, identifyModule };

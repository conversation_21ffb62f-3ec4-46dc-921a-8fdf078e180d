/**
 * 文件自动同步脚本
 * 监控本地文件变化并自动同步到生产服务器(***************)
 * 
 * 使用方法:
 * node scripts/sync-to-prod.js
 */

const chokidar = require('chokidar');
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const chalk = require('chalk');

// 配置
const TARGET_SERVER = '***************';
const TARGET_DIR = '/home/<USER>';
const ROOT_DIR = path.resolve(__dirname, '..');

// 需要监控的目录和文件
const WATCH_PATHS = [
  // 数据库和数据文件
  'database/**/*',           // 数据库文件
  'data/**/*',               // 其他数据文件
  
  // 配置文件
  'lib/config.js',           // 前端配置
  '.env*',                   // 环境变量文件
  'config/**/*',             // 配置目录
  
  // API和后端文件
  'api/**/*',                // API文件
  'server/**/*',             // 服务器文件
  'routes/**/*',             // 路由文件
  'controllers/**/*',        // 控制器
  'models/**/*',             // 数据模型
  
  // 资源文件
  'public/**/*.json',        // 公共JSON资源
  'public/data/**/*',        // 公共数据文件
  
  // 工具脚本
  'scripts/prod-*.js',       // 生产环境相关脚本
  
  // 其他可能需要同步的文件
  'middleware/**/*',         // 中间件
  'utils/**/*',              // 工具函数
  'hooks/**/*',              // 钩子函数
  'services/**/*',           // 服务
  'lib/**/*.js',             // 库文件
  'components/server/**/*',  // 服务端组件
];

// 忽略的文件和目录模式
const IGNORE_PATTERNS = [
  '**/.git/**',
  '**/node_modules/**',
  '**/chrome-extension/**',  // 忽略Chrome插件文件
  '**/.next/**',
  '**/dist/**',
  '**/build/**',
  '**/*.log',
  '**/migration-temp/**',
  '**/temp/**',
  '**/*.lock',
  '**/package-lock.json',
];

// 确认chokidar已安装
try {
  require.resolve('chokidar');
  require.resolve('chalk');
} catch (e) {
  console.log('正在安装依赖...');
  execSync('npm install --save-dev chokidar chalk', { stdio: 'inherit' });
  console.log('依赖安装完成');
}

console.log(chalk.blue('=== 文件自动同步工具 ==='));
console.log(chalk.yellow(`目标服务器: ${TARGET_SERVER}`));
console.log(chalk.yellow(`目标目录: ${TARGET_DIR}`));
console.log(chalk.yellow('监控路径:'));
WATCH_PATHS.forEach(p => console.log(chalk.yellow(`  - ${p}`)));
console.log(chalk.yellow('\n忽略路径:'));
IGNORE_PATTERNS.forEach(p => console.log(chalk.yellow(`  - ${p}`)));

// 创建监控器
const watcher = chokidar.watch(WATCH_PATHS, {
  cwd: ROOT_DIR,
  ignored: IGNORE_PATTERNS,
  persistent: true,
  ignoreInitial: true, // 忽略初始扫描时的事件
  awaitWriteFinish: {
    stabilityThreshold: 1000, // 等待文件写入完成
    pollInterval: 100
  }
});

// 需要重启容器的文件类型
const shouldRestartContainer = (filePath) => {
  const restartPatterns = [
    /config\.js$/,
    /\.env/,
    /config\//,
    /api\//,
    /server\//,
    /routes\//,
    /controllers\//,
    /models\//,
    /middleware\//,
    /services\//
  ];
  
  return restartPatterns.some(pattern => pattern.test(filePath));
};

// 处理文件变化
const handleFileChange = (filePath) => {
  const localPath = path.join(ROOT_DIR, filePath);
  const remotePath = path.join(TARGET_DIR, filePath).replace(/\\/g, '/');
  const remoteDir = path.dirname(remotePath).replace(/\\/g, '/');
  
  try {
    // 确保远程目录存在
    execSync(`ssh root@${TARGET_SERVER} "mkdir -p ${remoteDir}"`, { stdio: 'inherit' });
    
    // 同步文件
    console.log(chalk.green(`正在同步: ${filePath}`));
    execSync(`scp "${localPath}" root@${TARGET_SERVER}:"${remotePath}"`, { stdio: 'inherit' });
    
    console.log(chalk.green(`✅ 文件已同步: ${filePath}`));
    
    // 检查是否需要重启容器
    if (shouldRestartContainer(filePath)) {
      console.log(chalk.yellow(`检测到关键文件变更(${filePath})，正在重启容器...`));
      execSync(`ssh root@${TARGET_SERVER} "cd ${TARGET_DIR} && docker-compose restart"`, { stdio: 'inherit' });
      console.log(chalk.green('✅ 容器已重启'));
    }
  } catch (error) {
    console.error(chalk.red(`❌ 同步失败: ${filePath}`));
    console.error(chalk.red(error.message));
  }
};

// 处理目录变化
const handleDirChange = (dirPath) => {
  const remoteDirPath = path.join(TARGET_DIR, dirPath).replace(/\\/g, '/');
  
  try {
    // 确保远程目录存在
    console.log(chalk.green(`创建远程目录: ${dirPath}`));
    execSync(`ssh root@${TARGET_SERVER} "mkdir -p ${remoteDirPath}"`, { stdio: 'inherit' });
    console.log(chalk.green(`✅ 远程目录已创建: ${dirPath}`));
  } catch (error) {
    console.error(chalk.red(`❌ 创建远程目录失败: ${dirPath}`));
    console.error(chalk.red(error.message));
  }
};

// 监控文件变化
watcher
  .on('add', handleFileChange)
  .on('change', handleFileChange)
  .on('addDir', handleDirChange)
  .on('unlink', (filePath) => {
    const remotePath = path.join(TARGET_DIR, filePath).replace(/\\/g, '/');
    
    try {
      console.log(chalk.yellow(`文件已删除，正在同步删除操作: ${filePath}`));
      execSync(`ssh root@${TARGET_SERVER} "rm -f ${remotePath}"`, { stdio: 'inherit' });
      console.log(chalk.green(`✅ 文件已删除: ${filePath}`));
      
      // 检查是否需要重启容器
      if (shouldRestartContainer(filePath)) {
        console.log(chalk.yellow(`检测到关键文件删除(${filePath})，正在重启容器...`));
        execSync(`ssh root@${TARGET_SERVER} "cd ${TARGET_DIR} && docker-compose restart"`, { stdio: 'inherit' });
        console.log(chalk.green('✅ 容器已重启'));
      }
    } catch (error) {
      console.error(chalk.red(`❌ 删除失败: ${filePath}`));
      console.error(chalk.red(error.message));
    }
  })
  .on('unlinkDir', (dirPath) => {
    const remoteDirPath = path.join(TARGET_DIR, dirPath).replace(/\\/g, '/');
    
    try {
      console.log(chalk.yellow(`目录已删除，正在同步删除操作: ${dirPath}`));
      execSync(`ssh root@${TARGET_SERVER} "rm -rf ${remoteDirPath}"`, { stdio: 'inherit' });
      console.log(chalk.green(`✅ 目录已删除: ${dirPath}`));
    } catch (error) {
      console.error(chalk.red(`❌ 删除目录失败: ${dirPath}`));
      console.error(chalk.red(error.message));
    }
  })
  .on('ready', () => {
    console.log(chalk.green('\n✅ 监控已启动，等待文件变化...'));
    console.log(chalk.cyan('按 Ctrl+C 停止监控\n'));
  })
  .on('error', (error) => {
    console.error(chalk.red(`❌ 监控错误: ${error}`));
  });

// 处理进程退出
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n正在停止监控...'));
  watcher.close().then(() => {
    console.log(chalk.green('监控已停止'));
    process.exit(0);
  });
}); 
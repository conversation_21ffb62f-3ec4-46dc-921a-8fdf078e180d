const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// 数据库连接检查脚本
async function checkDatabase() {
  console.log('🔍 正在检查数据库配置...\n');

  // 检查环境变量
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ 环境变量未配置');
    console.log('请检查 .env.local 文件中的以下配置：');
    console.log('NEXT_PUBLIC_SUPABASE_URL=你的Supabase项目URL');
    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY=你的Supabase匿名密钥');
    return;
  }

  console.log('✅ 环境变量配置正常');
  console.log(`URL: ${supabaseUrl}`);
  console.log(`Key: ${supabaseKey.substring(0, 20)}...`);

  // 创建客户端
  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 检查数据库连接
    console.log('\n🔗 测试数据库连接...');
    const { data, error } = await supabase.from('environments').select('count').limit(1);
    
    if (error) {
      console.error('❌ 数据库连接失败:', error.message);
      if (error.message.includes('relation "environments" does not exist')) {
        console.log('\n📝 请在 Supabase SQL 编辑器中执行以下步骤：');
        console.log('1. 前往 Supabase Dashboard > SQL Editor');
        console.log('2. 执行 database/setup.md 中的完整 SQL 语句');
        console.log('3. 重新运行此检查脚本');
      }
      return;
    }

    console.log('✅ 数据库连接正常');

    // 检查表结构
    console.log('\n📋 检查表结构...');
    
    // 检查 environments 表
    const { data: envs, error: envError } = await supabase
      .from('environments')
      .select('*')
      .limit(1);

    if (envError) {
      console.error('❌ environments 表查询失败:', envError.message);
      return;
    }

    console.log('✅ environments 表正常');

    // 检查 accounts 表及新字段
    const { data: accounts, error: accountError } = await supabase
      .from('accounts')
      .select('id, name, username_xpath, password_xpath')
      .limit(1);

    if (accountError) {
      console.error('❌ accounts 表查询失败:', accountError.message);
      if (accountError.message.includes('column "username_xpath" does not exist')) {
        console.log('\n📝 需要更新数据库结构：');
        console.log('请在 Supabase SQL 编辑器中执行：');
        console.log('ALTER TABLE accounts ADD COLUMN IF NOT EXISTS username_xpath TEXT;');
        console.log('ALTER TABLE accounts ADD COLUMN IF NOT EXISTS password_xpath TEXT;');
        return;
      }
      return;
    }

    console.log('✅ accounts 表结构正常（包含新增的 XPath 字段）');

    // 检查用户认证
    console.log('\n👤 检查用户认证状态...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.log('ℹ️  当前未登录（正常情况）');
    } else if (user) {
      console.log('✅ 用户已登录:', user.email);
    } else {
      console.log('ℹ️  当前未登录（正常情况）');
    }

    console.log('\n🎉 数据库配置检查完成！');
    console.log('📖 如有问题，请参考 README.md 中的详细配置说明');

  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error.message);
  }
}

// 执行检查
checkDatabase().catch(console.error); 
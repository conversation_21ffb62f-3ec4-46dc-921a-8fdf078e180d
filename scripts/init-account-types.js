const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// 初始化账号类型数据
async function initAccountTypes() {
  console.log('🔍 正在检查并初始化账号类型数据...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ 环境变量未配置');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 登录（使用管理员账号）
    const { error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginError) {
      console.error('❌ 登录失败:', loginError.message);
      return;
    }

    console.log('✅ 登录成功');

    // 1. 检查account_types表是否存在
    console.log('\n📋 检查account_types表...');
    const { data: typesCheck, error: typesCheckError } = await supabase
      .from('account_types')
      .select('*')
      .limit(1);

    if (typesCheckError) {
      console.error('❌ account_types表不存在或查询失败:', typesCheckError.message);
      console.log('\n💡 请先执行以下SQL创建表结构：');
      console.log(`
-- 创建账号类型表
CREATE TABLE IF NOT EXISTS account_types (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  color VARCHAR(7) DEFAULT '#6366f1',
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 为accounts表添加account_type字段（如果不存在）
ALTER TABLE accounts ADD COLUMN IF NOT EXISTS account_type INTEGER DEFAULT 0;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_accounts_account_type ON accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_account_types_sort_order ON account_types(sort_order);
      `);
      return;
    }

    console.log('✅ account_types表存在');

    // 2. 检查是否已有数据
    const { data: existingTypes, error: existingError } = await supabase
      .from('account_types')
      .select('*')
      .order('sort_order');

    if (existingError) {
      console.error('❌ 查询现有类型失败:', existingError.message);
      return;
    }

    console.log(`\n📊 当前已有 ${existingTypes.length} 个账号类型`);

    // 3. 定义默认账号类型
    const defaultTypes = [
      { name: '默认账号', description: '未分类的账号', color: '#6b7280', sort_order: 0 },
      { name: '个人账号', description: '个人使用的账号', color: '#3b82f6', sort_order: 1 },
      { name: '店铺账号', description: '店铺相关账号', color: '#10b981', sort_order: 2 },
      { name: '散客账号', description: '散客相关账号', color: '#f59e0b', sort_order: 3 },
      { name: '测试账号', description: '用于测试的账号', color: '#8b5cf6', sort_order: 4 },
      { name: '客服账号', description: '客服相关账号', color: '#ef4444', sort_order: 5 }
    ];

    // 4. 插入缺失的类型
    for (const type of defaultTypes) {
      const existing = existingTypes.find(t => t.name === type.name);
      if (!existing) {
        console.log(`📝 添加账号类型: ${type.name}`);
        const { error: insertError } = await supabase
          .from('account_types')
          .insert(type);

        if (insertError) {
          console.error(`❌ 添加${type.name}失败:`, insertError.message);
        } else {
          console.log(`✅ 成功添加: ${type.name}`);
        }
      } else {
        console.log(`ℹ️  已存在: ${type.name}`);
      }
    }

    // 5. 检查accounts表的account_type字段
    console.log('\n🔍 检查账号的类型分布...');
    const { data: accountStats, error: statsError } = await supabase
      .from('accounts')
      .select('account_type')
      .eq('is_deleted', false);

    if (statsError) {
      console.error('❌ 查询账号统计失败:', statsError.message);
      return;
    }

    const typeStats = {};
    accountStats.forEach(acc => {
      const type = acc.account_type || 0;
      typeStats[type] = (typeStats[type] || 0) + 1;
    });

    console.log('📊 当前账号类型分布:');
    Object.entries(typeStats).forEach(([type, count]) => {
      const typeName = defaultTypes.find(t => t.sort_order === parseInt(type))?.name || '未知类型';
      console.log(`   类型 ${type} (${typeName}): ${count} 个账号`);
    });

    // 6. 获取最新的类型列表
    const { data: finalTypes, error: finalError } = await supabase
      .from('account_types')
      .select('*')
      .order('sort_order');

    if (finalError) {
      console.error('❌ 获取最终类型列表失败:', finalError.message);
      return;
    }

    console.log('\n✅ 账号类型初始化完成！');
    console.log('📋 可用的账号类型:');
    finalTypes.forEach(type => {
      console.log(`   ${type.sort_order}. ${type.name} - ${type.description} (${type.color})`);
    });

    console.log('\n🎯 下一步:');
    console.log('1. 前端需要实现账号类型筛选组件');
    console.log('2. 账号编辑功能需要支持类型选择');
    console.log('3. 可以手动为现有账号分配类型');

  } catch (error) {
    console.error('❌ 初始化过程中出现错误:', error.message);
  }
}

// 执行初始化
initAccountTypes().catch(console.error); 
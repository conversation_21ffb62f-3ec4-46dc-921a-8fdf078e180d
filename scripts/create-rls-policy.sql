-- 为environments表创建RLS策略，允许所有用户查看数据
-- 此策略允许任何已认证或未认证用户查看所有环境数据

-- 确保environments表启用了RLS
ALTER TABLE public.environments ENABLE ROW LEVEL SECURITY;

-- 删除现有的策略（如果存在）
DROP POLICY IF EXISTS "允许所有用户查看环境" ON public.environments;

-- 创建新的策略，允许所有用户查看环境数据
CREATE POLICY "允许所有用户查看环境" 
ON public.environments
FOR SELECT 
USING (true);  -- 'true' 表示无条件允许，任何人都可以查看

-- 如果你想限制只有已认证用户可以查看，可以使用下面的语句替代上面的策略
-- CREATE POLICY "允许已认证用户查看环境" 
-- ON public.environments
-- FOR SELECT 
-- USING (auth.role() = 'authenticated');

-- 如果你还想让特定用户可以编辑自己的环境数据，可以添加以下策略
-- CREATE POLICY "允许用户编辑自己的环境"
-- ON public.environments
-- FOR UPDATE
-- USING (auth.uid() = user_id);

-- 如果你想让特定用户可以删除自己的环境数据，可以添加以下策略
-- CREATE POLICY "允许用户删除自己的环境"
-- ON public.environments
-- FOR DELETE
-- USING (auth.uid() = user_id);

-- 如果你想让特定用户可以插入环境数据，可以添加以下策略
-- CREATE POLICY "允许用户插入环境"
-- ON public.environments
-- FOR INSERT
-- WITH CHECK (auth.uid() = user_id); 
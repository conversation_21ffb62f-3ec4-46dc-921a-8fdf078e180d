const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function debugAccounts() {
  console.log('🔍 调试账号数据...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ 环境变量未配置');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // 登录演示用户
    console.log('👤 登录演示用户...');
    const { error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginError) {
      console.error('❌ 登录失败:', loginError.message);
      return;
    }

    const { data: { user } } = await supabase.auth.getUser();
    console.log('✅ 当前用户:', user.email);
    console.log('📝 用户ID:', user.id);

    // 查询用户的所有环境
    console.log('\n🏗️  查询用户环境...');
    const { data: environments, error: envError } = await supabase
      .from('environments')
      .select('*')
      .eq('user_id', user.id)
      .order('name');

    if (envError) {
      console.error('❌ 查询环境失败:', envError.message);
      return;
    }

    console.log(`✅ 找到 ${environments.length} 个环境:`);
    environments.forEach(env => {
      console.log(`   - ${env.name} (ID: ${env.id})`);
    });

    // 查询用户的所有账号
    console.log('\n📋 查询用户账号...');
    const { data: accounts, error: accountError } = await supabase
      .from('accounts')
      .select(`
        *,
        environments(name)
      `)
      .eq('user_id', user.id)
      .order('name');

    if (accountError) {
      console.error('❌ 查询账号失败:', accountError.message);
      return;
    }

    console.log(`✅ 找到 ${accounts.length} 个账号:`);
    if (accounts.length === 0) {
      console.log('   (无账号数据)');
    } else {
      accounts.forEach(account => {
        console.log(`   - ${account.name}`);
        console.log(`     URL: ${account.login_url}`);
        console.log(`     用户名: ${account.username}`);
        console.log(`     环境: ${account.environments?.name}`);
        console.log(`     环境ID: ${account.environment_id}`);
        console.log(`     自定义XPath: ${account.username_xpath ? '✅' : '❌'}`);
        console.log('');
      });
    }

    // 按环境分组显示账号
    console.log('\n📊 按环境分组的账号数据:');
    for (const env of environments) {
      const envAccounts = accounts.filter(acc => acc.environment_id === env.id);
      console.log(`\n   📁 ${env.name} (${envAccounts.length} 个账号):`);
      if (envAccounts.length === 0) {
        console.log('     (无账号)');
      } else {
        envAccounts.forEach(acc => {
          console.log(`     - ${acc.name} (${acc.username})`);
        });
      }
    }

    // 检查测试环境的账号
    const testEnv = environments.find(env => env.name === '测试环境');
    if (testEnv) {
      console.log(`\n🧪 测试环境详细信息:`);
      console.log(`   环境ID: ${testEnv.id}`);
      
      const testAccounts = accounts.filter(acc => acc.environment_id === testEnv.id);
      console.log(`   账号数量: ${testAccounts.length}`);
      
      if (testAccounts.length > 0) {
        console.log(`   账号列表:`);
        testAccounts.forEach(acc => {
          console.log(`     ✅ ${acc.name}`);
          console.log(`        - URL: ${acc.login_url}`);
          console.log(`        - 用户名: ${acc.username}`);
          console.log(`        - 用户名XPath: ${acc.username_xpath || '(默认)'}`);
          console.log(`        - 密码XPath: ${acc.password_xpath || '(默认)'}`);
        });
      }
    }

    console.log('\n🎉 调试完成！');

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
  }
}

debugAccounts().catch(console.error); 
@echo off
chcp 65001
echo Moving API directory...
if exist app\api (
    if exist api_backup_temp rmdir /s /q api_backup_temp
    move app\api api_backup_temp
    echo API moved
)

echo Setting environment...
set NODE_ENV=production
set STATIC_EXPORT=true

echo Building...
npx next build

echo Restoring API...
if exist api_backup_temp (
    move api_backup_temp app\api
    echo API restored
)

echo Done. Check out directory for index.html
if exist out\index.html (
    echo SUCCESS: index.html found
) else (
    echo ERROR: index.html not found
)
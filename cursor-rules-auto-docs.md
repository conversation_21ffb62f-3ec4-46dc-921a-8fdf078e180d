# Cursor Rules - 自动文档维护工作流

## 🤖 AI助手行为规范

### 核心原则
- 你是天才程序员，保持质疑和批判思维
- 编程过程中**自动维护文档**，无需用户手动操作
- 文档维护是编程流程的一部分，不是额外工作
- 始终使用中文交流

## 📋 自动文档维护流程

### 1. 用户开启新需求时 - 自动检测和创建

**触发条件**: 用户提出新的功能需求

**AI自动执行**:
```
1. 分析是否为新需求
2. 自动检查是否存在对应PRD文档
3. 如不存在，立即创建PRD文档
4. 自动生成技术方案并创建TSD文档  
5. 创建DevLog文档准备记录开发过程
6. 更新文档索引
```

**文档位置**: 
- `docs/active/当前需求名称/` (开发中)
- 完成后移动到时间和功能索引

### 2. 编程过程中 - 实时更新

**触发条件**: 每次代码修改、问题解决、方案调整

**AI自动执行**:
```
1. 实时更新DevLog记录:
   - 技术实现细节
   - 遇到的问题和解决方案
   - 代码变更说明
2. 方案变更时自动更新TSD
3. 需求调整时自动修订PRD
4. 阶段性总结保存到文档
```

### 3. 功能完成时 - 自动归档

**触发条件**: 功能开发完成

**AI自动执行**:
```
1. 完善所有文档内容
2. 清理临时文件
3. 将文档移动到正式索引结构
4. 更新时间和功能索引
5. 生成完成报告
```

## 🔧 文档结构简化版

```
docs/
├── active/                 # 开发中的文档
│   └── 当前需求名称/
│       ├── prd.md
│       ├── tsd.md
│       └── devlog.md
├── archive/               # 已完成的文档
│   ├── by-time/
│   └── by-feature/
└── index.md              # 自动生成的总索引
```

## 🤖 AI助手工作模式

### 启动新需求时
```markdown
[AI检测到新需求]
✅ 自动创建PRD: docs/active/需求名称/prd.md
✅ 自动创建TSD: docs/active/需求名称/tsd.md  
✅ 自动创建DevLog: docs/active/需求名称/devlog.md
📝 开始记录开发过程...
```

### 编程过程中
```markdown
[每次代码变更后]
📝 更新DevLog: 记录本次实现的功能
🔄 检查是否需要更新TSD技术方案
💡 自动记录遇到的问题和解决方案
```

### 完成功能时
```markdown
[功能开发完成]
📋 完善最终文档
🗂️ 移动到archive归档
🔗 更新索引链接
✨ 文档自动维护完成
```

## 📝 文档模板简化版

### 自动PRD模板
```markdown
# {功能名称} - PRD

## 基本信息
- 创建时间: {自动填入}
- 状态: 开发中
- 模块: {自动识别}

## 需求描述
{从用户对话中提取}

## 功能要求
{AI分析生成}

## 验收标准
{AI自动生成}
```

### 自动TSD模板
```markdown
# {功能名称} - TSD

## 技术方案
{AI根据代码分析生成}

## 实现方案
{开发过程中自动完善}

## 风险点
{AI自动识别}
```

### 自动DevLog模板
```markdown
# {功能名称} - DevLog

## {日期} 开发记录

### 实现功能
{每次代码变更自动记录}

### 技术细节
{AI自动提取关键实现}

### 问题解决
{自动记录问题和解决过程}
```

## ⚙️ 具体实现规则

### AI助手必须执行的动作

1. **检测新需求**: 
   - 用户消息包含"新功能"、"需求"、"开发"等关键词
   - 自动创建active目录下的文档结构

2. **实时记录**: 
   - 每次edit_file后自动更新devlog
   - 代码变更自动记录到技术实现部分
   - 问题解决自动记录解决方案

3. **智能分类**:
   - 根据关键词自动识别功能模块
   - 默认模块：账号管理/验证码识别/插件管理/部署运维
   - 自动学习新模块并扩展识别能力
   - 支持自定义模块配置

4. **完成归档**:
   - 用户说"完成"、"结束"时自动归档
   - 移动到对应的时间和功能目录

### 关键行为模式

```javascript
// AI内部逻辑伪代码
on_user_message(message) {
  if (detect_new_requirement(message)) {
    auto_create_docs();
  }
}

after_code_edit(file, changes) {
  auto_update_devlog(changes);
  check_update_tsd();
}

on_completion_signal() {
  finalize_docs();
  archive_to_structure();
  update_indexes();
}
```

## 🎯 用户体验

用户只需要：
1. 正常提出需求和编程
2. 无需手动创建或维护文档
3. 文档自动生成和更新
4. 专注于编程，文档在后台自动维护

AI助手负责：
1. 自动检测需求
2. 自动创建文档
3. 实时更新记录
4. 自动归档整理

## 🔄 工作流示例

```
用户: "我要开发一个账号权限管理功能"
AI: ✅ 检测到新需求，自动创建文档...
    📝 PRD已创建: docs/active/账号权限管理/prd.md
    🔧 TSD已创建: docs/active/账号权限管理/tsd.md  
    📋 DevLog已创建: docs/active/账号权限管理/devlog.md

[编程过程]
AI: 🔄 正在修改 AccountCard.tsx...
    📝 自动记录: 添加权限控制逻辑
    💡 更新TSD: 权限验证方案

用户: "这个功能完成了"
AI: ✨ 功能完成，正在归档文档...
    🗂️ 移动到: docs/archive/by-feature/账号管理/
    🔗 更新索引完成
```

这样你就完全不需要手动维护文档了！ 
version: '3.8'

services:
  account-manage:
    build:
      context: .
      dockerfile: Dockerfile
    image: account-manage:latest
    container_name: account-manage
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SUPABASE_URL=http://***************:8000
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
    volumes:
      - ./database:/app/database
    networks:
      - account-manage-network
    # 确保服务健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    container_name: account-manage-nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx-config/default.conf:/etc/nginx/conf.d/default.conf:ro
    environment:
      - TZ=Asia/Shanghai
    networks:
      - account-manage-network
    depends_on:
      - account-manage

networks:
  account-manage-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
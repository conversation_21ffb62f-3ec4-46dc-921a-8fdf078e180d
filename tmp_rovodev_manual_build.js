const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('开始手动构建静态文件...');

async function runBuild() {
  try {
    // 1. 临时移除API目录
    const apiPath = path.join(__dirname, 'app/api');
    const backupPath = path.join(__dirname, 'api_backup_temp');
    
    if (fs.existsSync(backupPath)) {
      fs.rmSync(backupPath, { recursive: true, force: true });
    }
    
    if (fs.existsSync(apiPath)) {
      fs.renameSync(apiPath, backupPath);
      console.log('API目录已临时移除');
    }

    // 2. 设置环境变量并构建
    process.env.NODE_ENV = 'production';
    process.env.STATIC_EXPORT = 'true';
    
    console.log('开始Next.js构建...');
    
    // 使用spawn而不是execSync来避免编码问题
    const buildProcess = spawn('npx', ['next', 'build'], {
      stdio: 'inherit',
      cwd: __dirname,
      env: { ...process.env }
    });

    await new Promise((resolve, reject) => {
      buildProcess.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Build failed with code ${code}`));
        }
      });
    });

    // 3. 检查结果
    const outDir = path.join(__dirname, 'out');
    if (fs.existsSync(outDir)) {
      console.log('✅ 构建成功！out目录已生成');
      
      const files = fs.readdirSync(outDir);
      console.log('生成的文件：', files);
      
      if (files.includes('index.html')) {
        console.log('✅ index.html已生成');
      } else {
        console.log('❌ index.html未找到');
      }
    } else {
      console.log('❌ out目录未生成');
    }

  } catch (error) {
    console.error('构建失败:', error.message);
  } finally {
    // 4. 恢复API目录
    const apiPath = path.join(__dirname, 'app/api');
    const backupPath = path.join(__dirname, 'api_backup_temp');
    
    if (fs.existsSync(backupPath)) {
      if (fs.existsSync(apiPath)) {
        fs.rmSync(apiPath, { recursive: true, force: true });
      }
      fs.renameSync(backupPath, apiPath);
      console.log('API目录已恢复');
    }
  }
}

runBuild();
{"extVersion": "1.29.10", "name": "autologin-optimized", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"nodes": [{"id": "bhGdWWgyn5cXQmo6vv92P", "type": "BlockBasic", "initialized": false, "position": {"x": 96, "y": 36}, "data": {"disableBlock": false, "description": "启动触发器", "type": "manual", "interval": 60, "delay": 5, "date": "", "time": "00:00", "url": "", "shortcut": "", "activeInInput": false, "isUrlRegex": false, "days": [], "contextMenuName": "", "contextTypes": [], "parameters": [{"name": "initParams", "type": "json", "description": "登录参数配置", "defaultValue": "{\n    \"loginUrl\": \"https://test-aliuser.lotsmall.cn/usercenter/login\",\n    \"username\": \"18766668891\",\n    \"password\": \"123456asd.\",\n    \"targetApp\": \"智游宝数智平台\"\n}", "placeholder": "Text", "data": {"required": false}, "id": "Lkj2"}], "preferParamsInTab": false}, "label": "trigger"}, {"id": "dOhsbTUuFxgs3Lyprg5mR", "type": "BlockBasic", "initialized": false, "position": {"x": 400, "y": 36}, "data": {"active": true, "customUserAgent": false, "description": "打开登录页面", "disableBlock": false, "inGroup": false, "settings": {"blockTimeout": 0, "debugMode": false}, "tabZoom": 1, "updatePrevTab": false, "url": "{{variables.initParams.loginUrl}}", "userAgent": "", "waitTabLoaded": true}, "label": "new-tab"}, {"id": "smart-login-fill", "type": "BlockBasic", "initialized": false, "position": {"x": 700, "y": 36}, "data": {"disableBlock": false, "description": "智能登录填充：一次性完成表单填充和验证码识别", "timeout": 30000, "context": "website", "code": "// === 智能登录填充块 ===\n// 功能：一次性填充用户名、密码、验证码，支持失败重试\n\n(async () => {\n  try {\n    // --- 配置参数 ---\n    const config = {\n      username: automaRefData('variables', 'initParams.username'),\n      password: automaRefData('variables', 'initParams.password'),\n      maxRetries: 3,\n      \n      selectors: {\n        usernameInput: '.sdi-form-item-content > .sdi-input-type-text > .sdi-input',\n        passwordInput: '.sdi-input-type-password > .sdi-input',\n        captchaInput: '.checkcode-warper .sdi-input',\n        captchaImage: 'img.code-img',\n        refreshBtn: 'i.sdi-icon-ios-refresh',\n        formWarper: 'div.form-warper'\n      }\n    };\n\n    console.log('=== 开始智能登录填充 ===');\n    \n    // 获取或初始化重试计数\n    let retryCount = automaRefData('variables', 'loginRetryCount') || 0;\n    console.log(`当前重试次数: ${retryCount}/${config.maxRetries}`);\n\n    // --- 1. 等待页面完全加载 ---\n    await waitForElement(config.selectors.formWarper, 10000);\n    console.log('✓ 登录表单加载完成');\n    \n    // 点击表单区域激活\n    const formWarper = document.querySelector(config.selectors.formWarper);\n    if (formWarper) formWarper.click();\n\n    // --- 2. 如果是重试，先刷新验证码 ---\n    if (retryCount > 0) {\n      console.log('重试登录，正在刷新验证码...');\n      const refreshBtn = document.querySelector(config.selectors.refreshBtn);\n      if (refreshBtn) {\n        refreshBtn.click();\n        await new Promise(resolve => setTimeout(resolve, 2000)); // 等待验证码刷新\n      }\n    }\n\n    // --- 3. 等待输入框可用 ---\n    await waitForElement(config.selectors.usernameInput, 5000);\n\n    // --- 4. 获取并识别验证码 ---\n    const captchaText = await getCaptchaText(config);\n    if (!captchaText) {\n      throw new Error('验证码识别失败');\n    }\n    console.log(`✓ 验证码识别成功: ${captchaText}`);\n\n    // --- 5. 一次性填充所有表单 ---\n    const formData = [\n      { selector: config.selectors.usernameInput, value: config.username, name: '用户名' },\n      { selector: config.selectors.passwordInput, value: config.password, name: '密码' },\n      { selector: config.selectors.captchaInput, value: captchaText, name: '验证码' }\n    ];\n\n    for (const field of formData) {\n      await fillField(field);\n      await new Promise(resolve => setTimeout(resolve, 500)); // 字段间小延迟\n    }\n    \n    console.log('✓ 所有表单填充完成');\n\n    // --- 6. 保存状态供下一步使用 ---\n    automaSetVariable('formFilled', true);\n    automaSetVariable('currentCaptcha', captchaText);\n    automaSetVariable('fillTimestamp', Date.now());\n    \n    // --- 辅助函数 ---\n    \n    // 等待元素出现\n    async function waitForElement(selector, timeout = 5000) {\n      const start = Date.now();\n      while (Date.now() - start < timeout) {\n        const element = document.querySelector(selector);\n        if (element && element.offsetParent !== null) return element;\n        await new Promise(resolve => setTimeout(resolve, 200));\n      }\n      throw new Error(`元素未找到或不可见: ${selector}`);\n    }\n\n    // 获取验证码文本\n    async function getCaptchaText(config) {\n      await waitForElement(config.selectors.captchaImage, 5000);\n      const captchaImg = document.querySelector(config.selectors.captchaImage);\n      if (!captchaImg) throw new Error('验证码图片未找到');\n      \n      const imageUrl = captchaImg.src;\n      console.log('获取验证码图片:', imageUrl);\n      \n      // 转换为Base64\n      const response = await fetch(imageUrl);\n      const blob = await response.blob();\n      const base64 = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsDataURL(blob);\n      });\n      \n      console.log('调用OCR API识别验证码...');\n      \n      // 调用OCR API\n      const ocrResponse = await automaFetch('json', {\n        url: 'https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize',\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Cookie': 'tr_de_id=ZHYtSEhOUlZRWkVXSFZI'\n        },\n        body: JSON.stringify({ imageData: base64 })\n      });\n      \n      if (ocrResponse?.success) {\n        console.log('OCR识别成功:', ocrResponse.data);\n        return ocrResponse.data.text;\n      } else {\n        console.error('OCR识别失败:', ocrResponse);\n        return null;\n      }\n    }\n\n    // 填充单个字段\n    async function fillField({ selector, value, name }) {\n      const element = await waitForElement(selector, 3000);\n      \n      // 聚焦并清空\n      element.focus();\n      element.click();\n      await new Promise(resolve => setTimeout(resolve, 200));\n      \n      // 清空现有内容\n      element.select();\n      element.value = '';\n      \n      // 填入新值\n      element.value = value;\n      \n      // 触发所有可能的事件\n      const events = ['input', 'change', 'blur', 'keyup'];\n      events.forEach(eventType => {\n        element.dispatchEvent(new Event(eventType, { bubbles: true, cancelable: true }));\n      });\n      \n      console.log(`✓ ${name}填充完成: ${value}`);\n    }\n\n  } catch (error) {\n    console.error('❌ 登录填充失败:', error);\n    automaSetVariable('loginError', error.message);\n    automaSetVariable('formFilled', false);\n  } finally {\n    automaNextBlock();\n  }\n})();", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "login-retry-control", "type": "BlockBasic", "initialized": false, "position": {"x": 1000, "y": 36}, "data": {"disableBlock": false, "description": "登录检测与重试控制：点击登录，检测结果，智能重试", "timeout": 20000, "context": "website", "code": "// === 登录检测与重试控制块 ===\n\n(async () => {\n  try {\n    const maxRetries = 3;\n    let retryCount = automaRefData('variables', 'loginRetryCount') || 0;\n    const formFilled = automaRefData('variables', 'formFilled');\n    \n    if (!formFilled) {\n      throw new Error('表单未填充，无法继续登录');\n    }\n\n    console.log(`=== 开始登录尝试 (${retryCount + 1}/${maxRetries}) ===`);\n\n    // --- 1. 等待并点击登录按钮 ---\n    const loginBtn = await waitForElement('button.login-btn', 5000);\n    console.log('找到登录按钮，准备点击...');\n    \n    // 确保按钮可点击\n    loginBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    loginBtn.click();\n    console.log('✓ 已点击登录按钮');\n    \n    // --- 2. 等待页面响应并检测结果 ---\n    console.log('等待登录响应...');\n    await new Promise(resolve => setTimeout(resolve, 3000)); // 等待响应\n    \n    const loginResult = await detectLoginResult();\n    \n    if (loginResult.success) {\n      console.log('🎉 登录成功！跳转到工作台');\n      automaSetVariable('loginSuccess', true);\n      automaSetVariable('loginRetryCount', 0); // 重置计数\n      automaSetVariable('shouldRetry', false);\n      automaNextBlock(); // 成功，继续下一步\n      \n    } else {\n      retryCount++;\n      automaSetVariable('loginRetryCount', retryCount);\n      \n      if (retryCount < maxRetries) {\n        console.log(`❌ 登录失败: ${loginResult.error}`);\n        console.log(`🔄 准备第 ${retryCount + 1} 次重试...`);\n        \n        // 设置重试标志\n        automaSetVariable('shouldRetry', true);\n        automaSetVariable('retryReason', loginResult.error);\n        \n        // 使用automaNextBlock跳转回填充块\n        // 注意：这需要在工作流中使用循环块或条件块来实现循环\n        automaNextBlock({ nextBlockId: 'smart-login-fill' });\n        \n      } else {\n        console.error(`💥 登录最终失败，已重试 ${maxRetries} 次`);\n        automaSetVariable('loginSuccess', false);\n        automaSetVariable('shouldRetry', false);\n        automaSetVariable('finalError', `登录失败超过最大重试次数: ${loginResult.error}`);\n        \n        // 即使失败也继续，让用户手动处理\n        automaNextBlock();\n      }\n    }\n\n    // --- 辅助函数 ---\n    \n    async function waitForElement(selector, timeout = 5000) {\n      const start = Date.now();\n      while (Date.now() - start < timeout) {\n        const element = document.querySelector(selector);\n        if (element && element.offsetParent !== null) return element;\n        await new Promise(resolve => setTimeout(resolve, 200));\n      }\n      throw new Error(`元素未找到: ${selector}`);\n    }\n\n    // 检测登录结果的函数\n    async function detectLoginResult() {\n      // 方法1: 检查URL变化 (最可靠)\n      if (window.location.href.includes('/worktable') || \n          window.location.href.includes('/dashboard') ||\n          window.location.href.includes('/personal')) {\n        return { success: true };\n      }\n      \n      // 方法2: 检查页面内容变化 (检查是否有工作台相关元素)\n      const workbenchElements = document.querySelectorAll('.my-app__item, [class*=\"app-item\"], [class*=\"worktable\"]');\n      if (workbenchElements.length > 0) {\n        console.log('检测到工作台元素，登录成功');\n        return { success: true };\n      }\n      \n      // 方法3: 检查是否有\"我的应用\"等文字\n      const pageText = document.body.textContent || '';\n      if (pageText.includes('我的应用') || pageText.includes('工作台') || pageText.includes('智游宝')) {\n        console.log('检测到工作台页面文字，登录成功');\n        return { success: true };\n      }\n      \n      // 方法4: 检查错误提示\n      const errorSelectors = [\n        '.error-message', '.login-error', '.ant-message-error',\n        '[class*=\"error\"]', '[class*=\"fail\"]', '.sdi-message'\n      ];\n      \n      for (const selector of errorSelectors) {\n        const errorEl = document.querySelector(selector);\n        if (errorEl && errorEl.textContent.trim()) {\n          const errorText = errorEl.textContent.trim();\n          console.log('检测到错误提示:', errorText);\n          return { success: false, error: errorText };\n        }\n      }\n      \n      // 方法5: 检查验证码是否刷新 (表示验证码错误)\n      const captchaImg = document.querySelector('img.code-img');\n      if (captchaImg) {\n        // 如果还在登录页面且能看到验证码，说明登录失败\n        const currentCaptcha = automaRefData('variables', 'currentCaptcha');\n        console.log('仍在登录页面，登录失败');\n        return { success: false, error: '登录失败，可能是验证码错误或账号密码错误' };\n      }\n      \n      // 方法6: 检查登录按钮是否还存在\n      const loginBtn = document.querySelector('button.login-btn');\n      if (loginBtn) {\n        console.log('登录按钮仍存在，登录失败');\n        return { success: false, error: '登录失败，页面未跳转' };\n      }\n      \n      // 如果无法明确判断，倾向于认为成功了 (因为页面发生了变化)\n      console.log('无法明确判断，倾向于成功');\n      return { success: true };\n    }\n\n  } catch (error) {\n    console.error('❌ 登录检测过程出错:', error);\n    automaSetVariable('loginError', error.message);\n    automaSetVariable('loginSuccess', false);\n    automaNextBlock();\n  }\n})();", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "xujtgk8", "type": "BlockBasic", "initialized": false, "position": {"x": 1300, "y": 36}, "data": {"disableBlock": false, "description": "确保在工作台页面", "url": "https://test-aliuser.lotsmall.cn/usercenter/personal/worktable", "userAgent": "", "active": true, "tabZoom": 1, "inGroup": false, "waitTabLoaded": true, "updatePrevTab": false, "customUserAgent": false}, "label": "new-tab"}, {"id": "j1e5uxw", "type": "BlockBasic", "initialized": false, "position": {"x": 1600, "y": 36}, "data": {"disableBlock": false, "description": "智能查找并生成应用点击选择器", "timeout": 20000, "context": "website", "code": "// === 智能应用查找块 ===\n// 根据配置的应用名称动态查找并生成点击选择器\n\ntry {\n    const itemSelector = 'div.my-app__item';\n    const targetCardName = automaRefData('variables', 'initParams.targetApp') || '智游宝数智平台';\n    const uniqueText = ''; // 如需要额外筛选条件可配置\n\n    console.log(`=== 开始查找应用: ${targetCardName} ===`);\n\n    // 等待页面加载\n    const waitForApps = async () => {\n      const maxWait = 10000; // 最多等10秒\n      const start = Date.now();\n      \n      while (Date.now() - start < maxWait) {\n        const apps = document.querySelectorAll(itemSelector);\n        if (apps.length > 0) {\n          console.log(`找到 ${apps.length} 个应用卡片`);\n          return apps;\n        }\n        await new Promise(resolve => setTimeout(resolve, 500));\n      }\n      throw new Error('等待应用列表超时');\n    };\n    \n    const allItems = await waitForApps();\n    let selectorCreated = false;\n\n    // 遍历查找目标应用\n    for (let i = 0; i < allItems.length; i++) {\n        const item = allItems[i];\n        const itemText = item.textContent || '';\n        \n        console.log(`检查应用 ${i + 1}: ${itemText.replace(/\\s+/g, ' ').trim()}`);\n\n        const nameMatch = itemText.includes(targetCardName);\n        const uniqueMatch = uniqueText ? itemText.includes(uniqueText) : true;\n\n        if (nameMatch && uniqueMatch) {\n            const itemIndex = i + 1;\n            \n            // 生成多个可能的点击选择器，优先级从高到低\n            const possibleSelectors = [\n                `.my-app__item:nth-child(${itemIndex}) .my-app__item-footer`,\n                `.my-app__item:nth-child(${itemIndex}) .my-app__item-inner`,\n                `.my-app__item:nth-child(${itemIndex})`,\n                `div.my-app__item:nth-child(${itemIndex})`\n            ];\n            \n            // 测试哪个选择器能找到可点击元素\n            let workingSelector = null;\n            for (const selector of possibleSelectors) {\n                const testElement = document.querySelector(selector);\n                if (testElement && testElement.offsetParent !== null) {\n                    workingSelector = selector;\n                    console.log(`✓ 找到可用选择器: ${selector}`);\n                    break;\n                }\n            }\n            \n            if (!workingSelector) {\n                // 如果都不行，用最基本的\n                workingSelector = possibleSelectors[2];\n                console.log(`⚠ 使用备用选择器: ${workingSelector}`);\n            }\n\n            console.log(`🎯 成功定位应用: \"${targetCardName}\" (第 ${itemIndex} 个)`);\n            console.log(`📍 生成的点击选择器: ${workingSelector}`);\n\n            automaSetVariable('dynamicClickSelector', workingSelector);\n            automaSetVariable('targetAppFound', true);\n            automaSetVariable('targetAppIndex', itemIndex);\n            selectorCreated = true;\n            break;\n        }\n    }\n\n    if (!selectorCreated) {\n        const errorMessage = `❌ 未找到应用: \"${targetCardName}\"`;\n        console.error(errorMessage);\n        console.log('可用的应用列表:');\n        allItems.forEach((item, index) => {\n            const text = item.textContent.replace(/\\s+/g, ' ').trim();\n            console.log(`  ${index + 1}. ${text}`);\n        });\n        \n        automaSetVariable('error', errorMessage);\n        automaSetVariable('targetAppFound', false);\n    }\n\n} catch (error) {\n    console.error('❌ 应用查找过程出错:', error);\n    automaSetVariable('error', '应用查找失败: ' + error.message);\n    automaSetVariable('targetAppFound', false);\n} finally {\n    automaNextBlock();\n}", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "92h742l", "type": "BlockBasic", "initialized": false, "position": {"x": 1900, "y": 36}, "data": {"disableBlock": false, "description": "点击目标应用", "findBy": "cssSelector", "waitForSelector": true, "waitSelectorTimeout": 5000, "selector": "{{variables.dynamicClickSelector}}", "markEl": false, "multiple": false}, "label": "event-click"}], "edges": [{"id": "trigger-to-newtab", "type": "custom", "source": "bhGdWWgyn5cXQmo6vv92P", "target": "dOhsbTUuFxgs3Lyprg5mR", "sourceHandle": "bhGdWWgyn5cXQmo6vv92P-output-1", "targetHandle": "dOhsbTUuFxgs3Lyprg5mR-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}, {"id": "newtab-to-smartfill", "type": "custom", "source": "dOhsbTUuFxgs3Lyprg5mR", "target": "smart-login-fill", "sourceHandle": "dOhsbTUuFxgs3Lyprg5mR-output-1", "targetHandle": "smart-login-fill-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}, {"id": "smartfill-to-retry", "type": "custom", "source": "smart-login-fill", "target": "login-retry-control", "sourceHandle": "smart-login-fill-output-1", "targetHandle": "login-retry-control-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}, {"id": "retry-to-worktable", "type": "custom", "source": "login-retry-control", "target": "xujtgk8", "sourceHandle": "login-retry-control-output-1", "targetHandle": "xujtgk8-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}, {"id": "worktable-to-find", "type": "custom", "source": "xujtgk8", "target": "j1e5uxw", "sourceHandle": "xujtgk8-output-1", "targetHandle": "j1e5uxw-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}, {"id": "find-to-click", "type": "custom", "source": "j1e5uxw", "target": "92h742l", "sourceHandle": "j1e5uxw-output-1", "targetHandle": "92h742l-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}], "position": [-5112.789634650061, 218.2644691347499], "zoom": 1.252550698332461, "viewport": {"x": -5112.789634650061, "y": 218.2644691347499, "zoom": 1.252550698332461}}, "settings": {"publicId": "autologin-optimized", "blockDelay": 0, "saveLog": true, "debugMode": true, "restartTimes": 3, "notification": true, "execContext": "popup", "reuseLastState": false, "inputAutocomplete": true, "onError": "stop-workflow", "executedBlockOnWeb": true, "insertDefaultColumn": false, "defaultColumnName": "column", "tabLoadTimeout": 30000}, "globalData": "{\n\t\"key\": \"value\"\n}", "description": "优化版自动登录工作流：智能填充、自动重试、应用跳转", "includedWorkflows": {}}
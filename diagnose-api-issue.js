#!/usr/bin/env node

/**
 * API问题诊断脚本
 * 用于诊断静态构建后API接口无法使用的问题
 */

const http = require('http');
const https = require('https');

const tests = [
  {
    name: '本地Next.js服务 (3001端口)',
    url: 'http://localhost:3001/api/health',
    method: 'GET'
  },
  {
    name: 'Nginx代理的API (80端口)',
    url: 'http://localhost/api/health',
    method: 'GET'
  },
  {
    name: '强制登录接口 (直接访问)',
    url: 'http://localhost:3001/api/account/update-force-login',
    method: 'POST',
    data: JSON.stringify({ accountId: 1, forceLogin: true })
  },
  {
    name: '强制登录接口 (通过Nginx)',
    url: 'http://localhost/api/account/update-force-login',
    method: 'POST',
    data: JSON.stringify({ accountId: 1, forceLogin: true })
  }
];

async function testEndpoint(test) {
  return new Promise((resolve) => {
    const url = new URL(test.url);
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname,
      method: test.method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'API-Diagnostic-Tool'
      }
    };

    if (test.data) {
      options.headers['Content-Length'] = Buffer.byteLength(test.data);
    }

    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          success: true,
          statusCode: res.statusCode,
          headers: res.headers,
          data: data.substring(0, 200) + (data.length > 200 ? '...' : '')
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });

    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        success: false,
        error: '请求超时'
      });
    });

    if (test.data) {
      req.write(test.data);
    }
    req.end();
  });
}

async function runDiagnostics() {
  console.log('🔍 API问题诊断开始...\n');

  for (const test of tests) {
    console.log(`📋 测试: ${test.name}`);
    console.log(`🔗 URL: ${test.url}`);
    console.log(`📝 方法: ${test.method}`);
    
    const result = await testEndpoint(test);
    
    if (result.success) {
      console.log(`✅ 状态: 成功 (${result.statusCode})`);
      console.log(`📄 响应: ${result.data}`);
    } else {
      console.log(`❌ 状态: 失败`);
      console.log(`🚨 错误: ${result.error}`);
    }
    
    console.log('─'.repeat(50));
  }

  console.log('\n📊 诊断总结:');
  console.log('1. 如果本地3001端口失败: Next.js服务未启动');
  console.log('2. 如果Nginx代理失败: Nginx配置或容器网络问题');
  console.log('3. 如果都成功: API工作正常');
  console.log('4. 如果静态构建后失败: 需要改用服务模式部署');
}

runDiagnostics().catch(console.error); 
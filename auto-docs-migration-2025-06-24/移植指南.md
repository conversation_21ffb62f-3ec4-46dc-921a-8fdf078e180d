# 🚀 自动文档维护系统 - 移植指南

## 🎯 移植概述

将自动文档维护功能移植到新项目，让AI在编程过程中自动创建、更新、归档文档。

## 📦 一键安装 (推荐)

### 方法1: 使用安装脚本

```bash
# 1. 下载安装脚本到新项目根目录
wget/curl https://your-repo/setup-auto-docs.js

# 2. 运行安装脚本
node setup-auto-docs.js

# 3. 配置Cursor Rules (见下方说明)
```

### 方法2: 手动复制文件

如果无法下载脚本，直接复制以下文件到新项目：

## 📂 需要复制的文件清单

```
新项目/
├── scripts/
│   └── auto-docs.js                    # 核心自动化脚本
├── docs/
│   ├── README.md                       # 使用说明
│   └── modules-config.json             # 功能模块配置
├── .cursor-rules-auto-docs             # Cursor Rules配置
└── setup-auto-docs.js                  # 一键安装脚本(可选)
```

## ⚙️ 手动安装步骤

### 1. 创建目录结构

```bash
mkdir -p docs/active
mkdir -p docs/archive/by-time
mkdir -p docs/archive/by-feature
mkdir -p scripts
```

### 2. 复制核心脚本

将 `scripts/auto-docs.js` 复制到新项目的 `scripts/` 目录

### 3. 复制配置文件

将 `docs/modules-config.json` 复制到新项目的 `docs/` 目录

### 4. 配置Cursor Rules

**关键步骤**: 将 `.cursor-rules-auto-docs` 的内容添加到新项目的Cursor Rules中

#### 方法A: 直接复制
```bash
# 如果新项目没有.cursor-rules
cp .cursor-rules-auto-docs 新项目/.cursor-rules
```

#### 方法B: 合并到现有Rules
```bash
# 如果新项目已有.cursor-rules，需要手动合并内容
cat .cursor-rules-auto-docs >> 新项目/.cursor-rules
```

#### 方法C: 在Cursor IDE中设置
1. 打开Cursor IDE
2. 进入Cursor Rules设置
3. 复制 `.cursor-rules-auto-docs` 内容
4. 粘贴到Cursor Rules编辑器中

## 🔧 项目特定配置

### 1. 自定义功能模块

根据新项目特点，修改 `docs/modules-config.json`：

```json
{
  "defaultModules": {
    "用户管理": ["用户", "登录", "权限", "账号"],
    "数据处理": ["数据", "处理", "分析", "导入"],
    "API接口": ["api", "接口", "服务", "后端"],
    "前端界面": ["前端", "ui", "页面", "组件"],
    "你的模块": ["关键词1", "关键词2", "..."]
  },
  "customModules": {}
}
```

### 2. 项目特定关键词

可以在配置文件中添加项目特定的功能模块：

```json
{
  "customModules": {
    "电商管理": ["商品", "订单", "购物车", "支付"],
    "内容管理": ["文章", "编辑", "发布", "内容"],
    "系统监控": ["监控", "日志", "报警", "性能"]
  }
}
```

## 🧪 测试安装

### 1. 验证脚本可用

```bash
# 测试创建文档
node scripts/auto-docs.js create "测试功能" "这是测试描述"

# 检查文档是否创建成功
ls docs/active/测试功能/
# 应该看到: prd.md  tsd.md  devlog.md
```

### 2. 验证AI自动化

在Cursor中开始对话：

```
你: "我要开发一个用户登录功能"
```

AI应该自动：
1. 检测到新需求
2. 运行创建文档命令
3. 告知文档已创建
4. 开始编程流程

## 📋 项目适配指南

### 不同项目类型的配置建议

#### 前端项目 (React/Vue)
```json
{
  "customModules": {
    "组件开发": ["组件", "component", "ui", "界面"],
    "路由管理": ["路由", "router", "导航", "页面"],
    "状态管理": ["状态", "store", "redux", "vuex"],
    "API集成": ["api", "请求", "接口", "数据"]
  }
}
```

#### 后端项目 (Node.js/Python)
```json
{
  "customModules": {
    "接口开发": ["api", "接口", "endpoint", "路由"],
    "数据库": ["数据库", "sql", "orm", "查询"],
    "认证授权": ["认证", "授权", "jwt", "session"],
    "中间件": ["中间件", "middleware", "拦截", "处理"]
  }
}
```

#### 全栈项目
```json
{
  "customModules": {
    "前端功能": ["页面", "组件", "ui", "交互"],
    "后端服务": ["api", "服务", "接口", "后端"],
    "数据库设计": ["数据库", "表", "模型", "数据"],
    "部署配置": ["部署", "配置", "docker", "服务器"]
  }
}
```

## 🎯 最佳实践

### 1. 目录命名规范

保持项目目录结构一致：
```
docs/
├── active/         # 开发中的文档
├── archive/        # 已完成的文档
└── modules-config.json
```

### 2. 功能命名规范

使用有意义的功能名称：
- ✅ "用户登录优化"
- ✅ "商品搜索功能"  
- ❌ "功能1"
- ❌ "修复bug"

### 3. 模块关键词设置

为每个模块设置足够的关键词：
```json
{
  "用户管理": [
    "用户", "user", "账号", "account", 
    "登录", "login", "注册", "register",
    "权限", "permission", "角色", "role"
  ]
}
```

## 🚨 常见问题

### Q: 脚本权限问题
```bash
# Linux/Mac需要添加执行权限
chmod +x scripts/auto-docs.js
```

### Q: Node.js版本兼容
确保Node.js版本 >= 14.0，支持现代JavaScript语法

### Q: Cursor Rules不生效
1. 检查文件名是否正确 (`.cursor-rules`)
2. 重启Cursor IDE
3. 确认文件内容格式正确

### Q: 文档路径问题
脚本假设在项目根目录运行，确保：
- `docs/` 目录在项目根目录
- `scripts/` 目录在项目根目录

## 🔄 版本升级

当原项目的自动文档系统升级时：

1. **备份配置**：先备份 `docs/modules-config.json`
2. **更新脚本**：替换 `scripts/auto-docs.js`
3. **合并配置**：将自定义模块配置合并回去
4. **更新Rules**：更新Cursor Rules配置

## 💡 移植建议

1. **渐进式采用**：先在小功能上测试，确认无误后全面使用
2. **团队培训**：确保团队成员了解新的文档工作流
3. **配置调优**：根据项目特点调整功能模块分类
4. **定期清理**：定期检查归档文档，确保组织合理

---

**🎉 移植完成后，你就可以在新项目中享受AI自动维护文档的便利了！**

只需要正常编程，AI会自动：
- 检测新需求 → 创建文档
- 代码修改 → 更新记录  
- 功能完成 → 自动归档

**真正的"编程即文档"体验！** 🚀 
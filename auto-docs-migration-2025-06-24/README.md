# 🚀 自动文档维护系统

## 快速安装

```bash
# 1. 运行安装脚本
node setup-auto-docs.js

# 2. 配置Cursor Rules
# 将 .cursor-rules-auto-docs 内容复制到你的Cursor Rules中

# 3. 开始使用
# 对AI说: "我要开发一个xxx功能"
```

## 文件说明

- setup-auto-docs.js - 一键安装脚本
- scripts/auto-docs.js - 核心自动化脚本  
- .cursor-rules-auto-docs - Cursor Rules配置
- docs/modules-config.json - 功能模块配置
- docs/README.md - 使用说明
- 移植指南.md - 详细移植指南

## 安装后效果

✅ AI自动检测新需求并创建文档
✅ 编程过程中实时更新DevLog
✅ 功能完成后自动归档整理
✅ 双重索引(时间+功能)便于查找

---
生成时间: 2025/6/24 14:26:37
源项目: E:\cursor-project\account-manage\account-manage

<!DOCTYPE html>
<html>
<head>
    <title>React App - 测试部署</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
        }
        h1 {
            color: #333;
        }
        p {
            color: #666;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>React应用部署成功！</h1>
        <p>这是一个测试页面，表明Docker Nginx部署已成功。</p>
        <p>服务器时间: <span id="server-time"></span></p>
        <p>部署时间: <script>document.write(new Date().toLocaleString())</script></p>
    </div>
    <script>
        // 显示服务器时间
        function updateTime() {
            document.getElementById('server-time').textContent = new Date().toLocaleString();
        }
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html> 
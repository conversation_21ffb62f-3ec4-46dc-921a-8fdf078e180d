# 滑块验证码距离计算服务

这是一个用于计算滑块验证码中滑块应该滑行距离的服务。通过图像处理和模板匹配算法，计算出滑块在背景图中的匹配位置。

## 功能特点

- 使用模板匹配算法计算滑块位置
- 支持边缘检测增强匹配精度
- 自动选择最佳匹配结果
- 提供REST API接口
- 支持健康检查

## 技术栈

- Node.js
- Express
- Jimp (图像处理)
- Sharp (图像处理和边缘检测)
- Axios (HTTP请求)
- Winston (日志记录)

## 安装和部署

### 前置条件

- Node.js (v14+)
- npm

### 安装步骤

1. 克隆或下载代码到服务器
2. 进入服务目录：`cd captcha-slider-service`
3. 运行部署脚本：`bash deploy.sh`

### 启动服务

```bash
bash start.sh
```

### 停止服务

```bash
bash stop.sh
```

### 查看服务状态

```bash
bash status.sh
```

## API 文档

### 计算滑块距离

**请求**：

```
POST /calculate
```

**请求体**：

```json
{
  "backgroundUrl": "http://example.com/background.jpg",
  "sliderUrl": "http://example.com/slider.jpg"
}
```

**响应**：

```json
{
  "success": true,
  "distance": 150,
  "confidence": 0.8765,
  "message": "滑块距离计算成功"
}
```

### 健康检查

**请求**：

```
GET /health
```

**响应**：

```json
{
  "status": "ok",
  "service": "captcha-slider-service"
}
```

## 测试

运行测试脚本：

```bash
node test.js
```

## 日志

服务日志位于以下位置：

- 错误日志：`error.log`
- 综合日志：`combined.log`
- 服务启动日志：`logs/captcha-slider-service.log` 
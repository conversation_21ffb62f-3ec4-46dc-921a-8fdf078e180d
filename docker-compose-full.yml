version: '3.8'

services:
  # Next.js API服务
  account-manage:
    build:
      context: .
      dockerfile: Dockerfile
    image: account-manage:latest
    container_name: account-manage
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SUPABASE_URL=http://192.168.202.230:8000
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
    volumes:
      - ./database:/app/database
    networks:
      - account-manage-network

  # Nginx前端服务
  nginx:
    image: nginx:alpine
    container_name: account-manage-nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - /opt/account-manage/static:/usr/share/nginx/html:ro
      - ./nginx-config/default.conf:/etc/nginx/conf.d/default.conf:ro
    environment:
      - TZ=Asia/Shanghai
    networks:
      - account-manage-network
    depends_on:
      - account-manage

networks:
  account-manage-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16 
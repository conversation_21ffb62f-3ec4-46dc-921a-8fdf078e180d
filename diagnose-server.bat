@echo off
echo === 远程服务器诊断 ===
echo 服务器: http://***************
echo 时间: %date% %time%
echo.

echo === 1. 访问根目录 ===
powershell -Command "try { $r = Invoke-WebRequest 'http://***************/' -TimeoutSec 10; Write-Host 'Status:' $r.StatusCode; Write-Host 'Content-Type:' $r.Headers.'Content-Type'; Write-Host 'Content Length:' $r.Content.Length; if ($r.Content.Length -lt 1000) { Write-Host 'Content:'; Write-Host $r.Content } else { Write-Host 'Content (first 500 chars):'; Write-Host $r.Content.Substring(0,500) } } catch { Write-Host 'Failed:' $_.Exception.Message }"
echo.

echo === 2. 检查API目录 ===
powershell -Command "try { $r = Invoke-WebRequest 'http://***************/api' -TimeoutSec 10; Write-Host 'Status:' $r.StatusCode; Write-Host 'Content:' $r.Content.Substring(0,200) } catch { Write-Host 'Failed:' $_.Exception.Message }"
echo.

echo === 3. 检查可能的健康检查路径 ===
echo --- /health ---
powershell -Command "try { $r = Invoke-WebRequest 'http://***************/health' -TimeoutSec 5; Write-Host 'Status:' $r.StatusCode; Write-Host 'Content:' $r.Content } catch { Write-Host 'Failed:' $_.Exception.Message }"
echo --- /api/status ---
powershell -Command "try { $r = Invoke-WebRequest 'http://***************/api/status' -TimeoutSec 5; Write-Host 'Status:' $r.StatusCode; Write-Host 'Content:' $r.Content } catch { Write-Host 'Failed:' $_.Exception.Message }"
echo --- /status ---
powershell -Command "try { $r = Invoke-WebRequest 'http://***************/status' -TimeoutSec 5; Write-Host 'Status:' $r.StatusCode; Write-Host 'Content:' $r.Content } catch { Write-Host 'Failed:' $_.Exception.Message }"
echo.

echo === 4. 测试GET方式访问账号API ===
powershell -Command "try { $r = Invoke-WebRequest 'http://***************/api/account/findByLoginUrl' -TimeoutSec 5; Write-Host 'GET Status:' $r.StatusCode; Write-Host 'Content:' $r.Content } catch { Write-Host 'GET Failed:' $_.Exception.Message }"
echo.

echo === 5. 检查响应头信息 ===
powershell -Command "try { $r = Invoke-WebRequest 'http://***************/' -TimeoutSec 5; Write-Host 'Headers:'; $r.Headers.GetEnumerator() | ForEach-Object { Write-Host $_.Key ':' $_.Value } } catch { Write-Host 'Failed:' $_.Exception.Message }"
echo.

echo === 诊断完成 ===
echo 请根据以上信息确定远程服务器的实际配置
pause 
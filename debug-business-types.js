// 调试脚本：检查数据库中账号的business_type字段值
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://192.168.202.230:8000';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugBusinessTypes() {
  try {
    console.log('=== 数据库中账号的业务类型调试 ===');
    
    // 获取所有账号的名称和业务类型
    const { data, error } = await supabase
      .from('accounts')
      .select('id, name, business_type')
      .order('name');

    if (error) {
      console.error('查询失败:', error);
      return;
    }

    console.log(`共找到 ${data.length} 个账号:`);
    data.forEach((account, index) => {
      console.log(`${index + 1}. ${account.name}: business_type=${account.business_type} (${typeof account.business_type})`);
    });

    // 统计各business_type的数量
    const typeCount = {};
    data.forEach(account => {
      const type = account.business_type || 0;
      typeCount[type] = (typeCount[type] || 0) + 1;
    });

    console.log('\n业务类型统计:');
    Object.entries(typeCount).forEach(([type, count]) => {
      console.log(`business_type=${type}: ${count}个账号`);
    });

    console.log('\n业务类型常量对照:');
    console.log('0: 其他账号');
    console.log('1: 店铺账号');
    console.log('2: 散客账号');
    console.log('3: 测试账号');
    console.log('4: 客服账号');
    console.log('5: 部署账号');
    console.log('6: log账号');
    console.log('7: sql账号');

    console.log('================================');
  } catch (err) {
    console.error('调试失败:', err);
  }
}

debugBusinessTypes();
const { chromium } = require('playwright');

async function useExistingBrowser() {
    // 方法1：使用现有的Chrome用户数据目录
    const browser = await chromium.launchPersistentContext(
        'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data', // Windows Chrome路径
        {
            headless: false,
            channel: 'chrome', // 使用系统安装的Chrome
            // 可选配置
            viewport: { width: 1280, height: 720 },
            ignoreDefaultArgs: ['--disable-extensions'], // 保留扩展程序
        }
    );

    const page = browser.pages()[0] || await browser.newPage();
    
    // 现在可以访问需要登录的页面
    await page.goto('https://test-aliuser.lotsmall.cn/usercenter/personal/worktable');
    
    // 页面应该保持登录状态
    console.log('页面标题:', await page.title());
    
    await browser.close();
}

// 方法2：使用连接到现有浏览器实例
async function connectToExistingBrowser() {
    // 首先需要以调试模式启动Chrome：
    // chrome --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome-debug"
    
    const browser = await chromium.connectOverCDP('http://localhost:9222');
    const contexts = browser.contexts();
    const context = contexts[0] || await browser.newContext();
    const page = await context.newPage();
    
    await page.goto('https://test-aliuser.lotsmall.cn/usercenter/personal/worktable');
    
    await browser.close();
}

useExistingBrowser(); 
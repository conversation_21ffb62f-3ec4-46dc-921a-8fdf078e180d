const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;
const { chromium } = require('playwright');
const Tesseract = require('tesseract.js');
const crypto = require('crypto');

let mainWindow;

// 加密密钥 - 固定密钥用于开发，生产环境应该使用更安全的方式管理
const ENCRYPTION_KEY = crypto.scryptSync('account-manage-secret', 'salt', 32);
const ALGORITHM = 'aes-256-cbc';

// 密码加密函数
function encryptPassword(password) {
  try {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(ALGORITHM, ENCRYPTION_KEY, iv);
    let encrypted = cipher.update(password, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  } catch (error) {
    console.error('密码加密错误:', error);
    throw new Error('密码加密失败');
  }
}

// 密码解密函数
function decryptPassword(encryptedPassword) {
  try {
    const textParts = encryptedPassword.split(':');
    if (textParts.length !== 2) {
      throw new Error('密码格式错误');
    }
    
    const iv = Buffer.from(textParts[0], 'hex');
    const encryptedText = textParts[1];
    const decipher = crypto.createDecipheriv(ALGORITHM, ENCRYPTION_KEY, iv);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    console.error('密码解密错误:', error);
    throw new Error('密码解密失败');
  }
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'hiddenInset',
    vibrancy: 'under-window',
    transparent: true,
    frame: false,
    minWidth: 800,
    minHeight: 600,
  });

  const startUrl = isDev ? 'http://localhost:3001' : `file://${path.join(__dirname, '../out/index.html')}`;
  
  console.log('开发模式:', isDev);
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('app.isPackaged:', app.isPackaged);
  console.log('加载 URL:', startUrl);
  
  mainWindow.loadURL(startUrl);

  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC 处理器 - 自动登录功能
ipcMain.handle('start-login', async (event, accountData) => {
  try {
    const { loginUrl, username, encryptedPassword, usernameXpath, passwordXpath, captchaXpath, loginButtonXpath } = accountData;
    
    // 发送状态更新
    event.sender.send('login-status', '正在启动浏览器...');
    
    // 启动浏览器
    const browser = await chromium.launch({ 
      headless: false, 
      slowMo: 100 
    });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // 导航到登录页面
    event.sender.send('login-status', '正在加载登录页面...');
    await page.goto(loginUrl, { waitUntil: 'networkidle' });
    
    // 等待页面加载完成
    await page.waitForTimeout(2000);
    
    // 填写用户名
    event.sender.send('login-status', '正在填写用户名...');
    let usernameField;
    
    if (usernameXpath) {
      // 使用用户自定义的XPath选择器
      try {
        usernameField = await page.locator(usernameXpath).first();
        await usernameField.fill(username);
        console.log('用户名填写完成（自定义XPath）:', username);
      } catch (error) {
        console.log('自定义XPath选择器失败，尝试默认选择器');
        usernameField = null;
      }
    }
    
    if (!usernameField) {
      // 使用默认选择器
      usernameField = await page.$('input[type="text"], input[name*="user"], input[id*="user"], input[placeholder*="用户"], input[placeholder*="手机"], input[placeholder*="账号"]');
      if (usernameField) {
        await usernameField.fill(username);
        console.log('用户名填写完成（默认选择器）:', username);
      } else {
        console.log('未找到用户名输入框');
        // 尝试更多的用户名选择器
        const alternativeUsernameField = await page.$('input[type="tel"], input[name*="phone"], input[id*="phone"]');
        if (alternativeUsernameField) {
          await alternativeUsernameField.fill(username);
          console.log('用户名填写完成（备用选择器）:', username);
        }
      }
    }
    
    // 填写密码
    event.sender.send('login-status', '正在填写密码...');
    let password;
    try {
      password = decryptPassword(encryptedPassword);
      console.log('密码解密成功');
    } catch (decryptError) {
      console.error('密码解密失败:', decryptError);
      event.sender.send('login-status', '密码解密失败，请重新设置账号密码');
      return { success: false, message: '密码解密失败，请重新设置账号密码' };
    }
    
    let passwordField;
    
    if (passwordXpath) {
      // 使用用户自定义的XPath选择器
      try {
        passwordField = await page.locator(passwordXpath).first();
        await passwordField.fill(password);
        console.log('密码填写完成（自定义XPath）');
      } catch (error) {
        console.log('自定义密码XPath选择器失败，尝试默认选择器');
        passwordField = null;
      }
    }
    
    if (!passwordField) {
      // 使用默认选择器
      passwordField = await page.$('input[type="password"]');
      if (passwordField) {
        await passwordField.fill(password);
        console.log('密码填写完成（默认选择器）');
      } else {
        console.log('未找到密码输入框');
      }
    }
    
    // 处理验证码
    if (captchaXpath) {
      try {
        event.sender.send('login-status', '正在识别验证码...');
        
        // 查找验证码图片
        const captchaImg = await page.$('img[src*="captcha"], img[src*="verify"], img[alt*="验证码"], canvas');
        if (captchaImg) {
          // 截取验证码图片
          const captchaBuffer = await captchaImg.screenshot();
          
          // 使用Tesseract进行OCR识别
          const { data: { text } } = await Tesseract.recognize(captchaBuffer, 'eng', {
            logger: m => console.log(m)
          });
          
          const captchaText = text.replace(/\s/g, '').slice(0, 4); // 通常验证码是4位
          console.log('识别的验证码:', captchaText);
          
          if (captchaText.length >= 3) {
            // 查找验证码输入框
            const captchaInput = await page.locator(captchaXpath.replace('//', '')).first();
            if (captchaInput) {
              await captchaInput.fill(captchaText);
              event.sender.send('login-status', `验证码识别成功: ${captchaText}`);
            }
          } else {
            event.sender.send('login-status', '验证码识别失败，请手动输入');
            // 让用户手动输入验证码
            await page.pause();
          }
        }
      } catch (captchaError) {
        console.error('验证码处理错误:', captchaError);
        event.sender.send('login-status', '验证码识别失败，请手动输入');
        await page.pause();
      }
    }
    
    // 点击登录按钮
    event.sender.send('login-status', '正在点击登录按钮...');
    if (loginButtonXpath) {
      const loginButton = await page.locator(loginButtonXpath.replace('//', '')).first();
      if (loginButton) {
        await loginButton.click();
      }
    } else {
      // 尝试找到登录按钮
      const loginButton = await page.$('button[type="submit"], input[type="submit"], button:has-text("登录"), button:has-text("登陆"), button:has-text("Sign in")');
      if (loginButton) {
        await loginButton.click();
      }
    }
    
    // 等待登录结果
    await page.waitForTimeout(3000);
    
    // 检查登录是否成功（通过URL变化或特定元素）
    const currentUrl = page.url();
    if (currentUrl !== loginUrl && !currentUrl.includes('login')) {
      event.sender.send('login-status', '登录成功！');
      return { success: true, message: '登录成功' };
    } else {
      event.sender.send('login-status', '登录可能失败，请检查');
      return { success: false, message: '登录可能失败，请手动检查' };
    }
    
  } catch (error) {
    console.error('自动登录错误:', error);
    event.sender.send('login-status', `错误: ${error.message}`);
    return { success: false, message: error.message };
  }
});

// 密码加密 IPC 处理器
ipcMain.handle('encrypt-password', async (event, password) => {
  console.log('开始加密密码');
  const encrypted = encryptPassword(password);
  console.log('密码加密完成');
  
  // 立即测试解密以确保加密正确
  try {
    const decrypted = decryptPassword(encrypted);
    console.log('密码加密/解密测试成功');
    return encrypted;
  } catch (error) {
    console.error('密码加密/解密测试失败:', error);
    throw new Error('密码加密测试失败');
  }
});

// 测试XPath IPC 处理器
ipcMain.handle('test-xpath', async (event, { url, xpath }) => {
  try {
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    await page.goto(url);
    await page.waitForTimeout(2000);
    
    // 高亮显示找到的元素
    const element = await page.locator(xpath.replace('//', '')).first();
    if (element) {
      await element.evaluate(el => {
        el.style.border = '3px solid red';
        el.style.backgroundColor = 'yellow';
      });
      return { success: true, message: '元素找到并已高亮显示' };
    } else {
      return { success: false, message: '未找到指定元素' };
    }
    
  } catch (error) {
    return { success: false, message: error.message };
  }
});

// 窗口控制
ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
}); 
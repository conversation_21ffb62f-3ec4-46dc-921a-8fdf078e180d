const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 自动登录
  startLogin: (accountData) => ipcRenderer.invoke('start-login', accountData),
  
  // 监听登录状态
  onLoginStatus: (callback) => {
    const subscription = (event, status) => callback(status);
    ipcRenderer.on('login-status', subscription);
    
    // 返回清理函数
    return () => {
      ipcRenderer.removeListener('login-status', subscription);
    };
  },
  
  // 密码加密
  encryptPassword: (password) => ipcRenderer.invoke('encrypt-password', password),
  
  // 测试XPath
  testXPath: (data) => ipcRenderer.invoke('test-xpath', data),
  
  // 窗口控制
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  
  // 平台检测
  platform: process.platform,
}); 
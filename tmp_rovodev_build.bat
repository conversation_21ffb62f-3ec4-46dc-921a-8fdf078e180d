@echo off
echo Starting static build...

echo Moving API directory...
if exist api_backup_temp rmdir /s /q api_backup_temp
if exist app\api (
    move app\api api_backup_temp
    echo API directory moved
)

echo Setting environment and building...
set NODE_ENV=production
set STATIC_EXPORT=true
call npx next build

echo Checking output...
if exist out\index.html (
    echo SUCCESS: index.html generated in out directory
    dir out
) else (
    echo ERROR: index.html not found
)

echo Restoring API directory...
if exist api_backup_temp (
    move api_backup_temp app\api
    echo API directory restored
)

echo Build complete
pause
@echo off
echo 🚀 开始静态构建过程...

echo 📦 备份API目录...
if exist api_backup rmdir /s /q api_backup
if exist app\api (
    move app\api api_backup
    echo ✅ API目录已备份
)

echo 🔨 开始构建...
call npm run build

if %errorlevel% == 0 (
    echo ✅ 构建成功！
    echo 📂 静态文件已生成到 out/ 目录
    
    if exist out\index.html (
        echo ✅ index.html 文件已成功生成
    ) else (
        echo ❌ 未找到 index.html 文件
    )
) else (
    echo ❌ 构建失败
)

echo 🔄 恢复API目录...
if exist api_backup (
    move api_backup app\api
    echo ✅ API目录已恢复
)

echo 🏁 构建过程完成
pause 
// 快速解决方案：使用专用配置文件保持登录状态
const { chromium } = require('playwright');

async function quickSetup() {
    console.log('🚀 启动带登录状态的浏览器...');
    
    // 使用专用配置文件目录（会自动创建）
    const context = await chromium.launchPersistentContext('./browser_profile', {
        headless: false,
        viewport: { width: 1280, height: 720 },
        channel: 'chrome', // 使用系统Chrome
    });
    
    const page = context.pages()[0] || await context.newPage();
    
    try {
        // 访问目标页面
        console.log('📄 访问工作台页面...');
        await page.goto('https://test-aliuser.lotsmall.cn/usercenter/personal/worktable', {
            waitUntil: 'networkidle'
        });
        
        // 检查是否需要登录
        if (page.url().includes('login')) {
            console.log('🔐 需要登录，请在浏览器中完成登录操作');
            console.log('登录完成后，下次运行将自动保持登录状态');
            
            // 等待用户登录
            await page.waitForURL('**/worktable', { timeout: 300000 }); // 5分钟超时
            console.log('✅ 登录成功！');
        } else {
            console.log('✅ 已保持登录状态');
        }
        
        // 现在可以查找卡片了
        console.log('🔍 开始查找"分时预约系统"卡片...');
        
        // 等待页面内容加载
        await page.waitForLoadState('networkidle');
        
        // 查找卡片的脚本（修正版）
        const cardSelector = await page.evaluate(() => {
            // 在页面中执行查找逻辑
            const selectors = [
                'div.my-app__item',
                '.my-app__item',
                'div[class*="my-app"][class*="item"]',
                'div[class*="item"]'
            ];
            
            for (const selector of selectors) {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    console.log(`找到 ${elements.length} 个 "${selector}" 元素`);
                    
                    for (let i = 0; i < elements.length; i++) {
                        const element = elements[i];
                        const text = element.textContent || '';
                        
                        if (text.includes('分时预约系统')) {
                            const targetSelector = `${selector}:nth-child(${i + 1})`;
                            console.log(`🎯 找到目标卡片！选择器: ${targetSelector}`);
                            
                            // 高亮显示
                            element.style.border = '3px solid red';
                            element.style.backgroundColor = 'rgba(255, 255, 0, 0.3)';
                            
                            return {
                                selector: targetSelector,
                                position: i + 1,
                                text: text.substring(0, 100),
                                found: true
                            };
                        }
                    }
                }
            }
            
            return { found: false };
        });
        
        if (cardSelector.found) {
            console.log('🎉 成功找到目标卡片！');
            console.log(`📍 位置: 第 ${cardSelector.position} 个`);
            console.log(`🔧 CSS选择器: ${cardSelector.selector}`);
            console.log(`📝 卡片文本: ${cardSelector.text}...`);
            
            // 保存结果供后续使用
            console.log('\n可以在Automa中使用以下选择器:');
            console.log(`${cardSelector.selector}`);
            
        } else {
            console.log('❌ 未找到包含"分时预约系统"的卡片');
            
            // 打印所有找到的元素供调试
            await page.evaluate(() => {
                const allElements = document.querySelectorAll('div');
                console.log(`页面共有 ${allElements.length} 个div元素`);
                
                // 打印前10个可能的卡片
                const potentialCards = Array.from(allElements)
                    .filter(el => el.textContent.length > 10)
                    .slice(0, 10);
                    
                potentialCards.forEach((el, i) => {
                    console.log(`${i + 1}. "${el.textContent.substring(0, 50)}..."`);
                });
            });
        }
        
        console.log('\n浏览器将保持打开，你可以手动操作或关闭');
        
    } catch (error) {
        console.error('❌ 发生错误:', error.message);
    }
    
    // 不自动关闭，让用户手动关闭
    // await context.close();
}

// 运行脚本
quickSetup().catch(console.error); 
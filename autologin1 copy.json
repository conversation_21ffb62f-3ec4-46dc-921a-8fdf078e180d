{"extVersion": "1.29.10", "name": "universal-login-template", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"nodes": [{"id": "trigger-universal", "type": "BlockBasic", "initialized": false, "position": {"x": 96, "y": 36}, "data": {"disableBlock": false, "description": "🌐 通用登录触发器", "type": "manual", "interval": 60, "delay": 2, "date": "", "time": "00:00", "url": "", "shortcut": "", "activeInInput": false, "isUrlRegex": false, "days": [], "contextMenuName": "", "contextTypes": [], "parameters": [{"name": "loginConfig", "type": "json", "description": "🔧 通用登录配置", "defaultValue": "{\n  \"siteTemplate\": \"aliuser\",\n  \"loginUrl\": \"https://test-aliuser.lotsmall.cn/usercenter/login\",\n  \"credentials\": {\n    \"username\": \"18766668891\",\n    \"password\": \"123456asd.\"\n  },\n  \"postLoginAction\": {\n    \"type\": \"click_app_card\",\n    \"targetText\": \"智游宝数智平台\",\n    \"targetUrl\": \"https://test-aliuser.lotsmall.cn/usercenter/personal/worktable\"\n  },\n  \"customSelectors\": {\n    \"username\": \"\",\n    \"password\": \"\",\n    \"captcha\": \"\",\n    \"captchaImage\": \"\",\n    \"loginButton\": \"\"\n  },\n  \"options\": {\n    \"maxRetries\": 3,\n    \"showProgress\": true,\n    \"ocrConfig\": {\n      \"url\": \"https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize\",\n      \"headers\": {\n        \"Cookie\": \"tr_de_id=ZHYtSEhOUlZRWkVXSFZI\"\n      }\n    }\n  }\n}", "placeholder": "Universal Configuration", "data": {"required": false}, "id": "login-config"}], "preferParamsInTab": false}, "label": "trigger"}, {"id": "template-loader", "type": "BlockBasic", "initialized": false, "position": {"x": 388, "y": 36}, "data": {"disableBlock": false, "description": "📋 模版加载器", "timeout": 5000, "context": "background", "code": "// === 通用登录模版加载器 ===\ntry {\n  console.log('🚀 === 通用登录系统启动 ===');\n  \n  const config = automaRefData('variables', 'loginConfig') || {};\n  \n  // 内置网站模版库\n  const siteTemplates = {\n    \"aliuser\": {\n      name: \"阿里用户中心\",\n      selectors: {\n        username: \".sdi-form-item-content > .sdi-input-type-text > .sdi-input\",\n        password: \".sdi-input-type-password > .sdi-input\",\n        captcha: \".checkcode-warper .sdi-input\",\n        captchaImage: \"img.code-img\",\n        captchaRefresh: \"i.sdi-icon-ios-refresh\",\n        loginButton: \"button.login-btn\"\n      },\n      postLoginActions: {\n        \"click_app_card\": {\n          template: \".my-app__item:nth-child({{position}}) .my-app__item-footer\",\n          findMethod: \"findAppByText\",\n          containerSelector: \"div.my-app__item\"\n        }\n      }\n    },\n    \"custom\": {\n      name: \"自定义网站\",\n      selectors: {\n        username: \"\",\n        password: \"\",\n        captcha: \"\",\n        captchaImage: \"\",\n        captchaRefresh: \"\",\n        loginButton: \"\"\n      },\n      postLoginActions: {\n        \"custom_click\": {\n          template: \"{{customSelector}}\",\n          findMethod: \"directClick\"\n        }\n      }\n    }\n  };\n  \n  // 行为模版库\n  const actionTemplates = {\n    \"click_app_card\": {\n      description: \"点击应用卡片\",\n      template: \".my-app__item:nth-child({{position}}) .my-app__item-footer\",\n      findMethod: \"findByTextContent\"\n    },\n    \"click_menu_item\": {\n      description: \"点击菜单项\",\n      template: \".nav-menu li:nth-child({{position}}) a\",\n      findMethod: \"findByTextContent\"\n    },\n    \"click_button\": {\n      description: \"点击按钮\",\n      template: \"button:contains('{{targetText}}')\",\n      findMethod: \"findByTextContent\"\n    },\n    \"custom_click\": {\n      description: \"自定义选择器\",\n      template: \"{{customSelector}}\",\n      findMethod: \"directClick\"\n    }\n  };\n  \n  // 获取选中的模版\n  const templateName = config.siteTemplate || 'aliuser';\n  const siteTemplate = siteTemplates[templateName];\n  \n  if (!siteTemplate) {\n    throw new Error(`未找到模版: ${templateName}`);\n  }\n  \n  // 合并自定义选择器 (优先级更高)\n  const finalSelectors = { ...siteTemplate.selectors };\n  if (config.customSelectors) {\n    Object.keys(config.customSelectors).forEach(key => {\n      if (config.customSelectors[key]) {\n        finalSelectors[key] = config.customSelectors[key];\n      }\n    });\n  }\n  \n  // 构建最终配置\n  const runtimeConfig = {\n    site: {\n      name: siteTemplate.name,\n      template: templateName\n    },\n    selectors: finalSelectors,\n    credentials: config.credentials || {},\n    postLoginAction: config.postLoginAction || {},\n    options: config.options || { maxRetries: 3 },\n    actionTemplates: actionTemplates\n  };\n  \n  // 保存到变量\n  automaSetVariable('runtimeConfig', runtimeConfig);\n  automaSetVariable('loginRetryCount', 0);\n  automaSetVariable('loginSuccess', false);\n  automaSetVariable('shouldRetry', false);\n  \n  console.log('✅ 模版加载完成:', {\n    site: runtimeConfig.site.name,\n    selectors: Object.keys(runtimeConfig.selectors),\n    actionType: runtimeConfig.postLoginAction.type\n  });\n  \n} catch (error) {\n  console.error('❌ 模版加载失败:', error);\n  automaSetVariable('templateError', error.message);\n} finally {\n  automaNextBlock();\n}", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "open-login-page", "type": "BlockBasic", "initialized": false, "position": {"x": 680, "y": 36}, "data": {"disableBlock": false, "description": "🌐 打开登录页面", "url": "{{variables.loginConfig.loginUrl}}", "userAgent": "", "active": true, "tabZoom": 1, "inGroup": false, "waitTabLoaded": true, "updatePrevTab": false, "customUserAgent": false, "settings": {"blockTimeout": 20000, "debugMode": false}}, "label": "new-tab"}, {"id": "universal-login-engine", "type": "BlockBasic", "initialized": false, "position": {"x": 972, "y": 36}, "data": {"disableBlock": false, "description": "🧠 通用登录引擎", "timeout": 35000, "context": "website", "code": "// === 通用登录引擎 ===\n(async () => {\n  try {\n    const runtimeConfig = automaRefData('variables', 'runtimeConfig');\n    \n    if (!runtimeConfig) {\n      throw new Error('运行时配置未找到');\n    }\n    \n    let retryCount = automaRefData('variables', 'loginRetryCount') || 0;\n    const maxRetries = runtimeConfig.options.maxRetries || 3;\n    \n    console.log(`🚀 === 第 ${retryCount + 1} 次登录尝试 (模版: ${runtimeConfig.site.name}) ===`);\n    \n    // 显示进度\n    if (runtimeConfig.options.showProgress) {\n      showProgress(`登录尝试 ${retryCount + 1}/${maxRetries}`, `正在使用 ${runtimeConfig.site.name} 模版...`);\n    }\n    \n    // 等待页面准备\n    await waitForPageReady();\n    \n    // 刷新验证码 (重试时)\n    if (retryCount > 0) {\n      await refreshCaptcha();\n    }\n    \n    // 识别验证码\n    if (runtimeConfig.options.showProgress) {\n      showProgress(`登录尝试 ${retryCount + 1}/${maxRetries}`, '正在识别验证码...');\n    }\n    const captcha = await recognizeCaptcha();\n    console.log(`✅ 验证码识别成功: ${captcha}`);\n    \n    // 填充表单\n    if (runtimeConfig.options.showProgress) {\n      showProgress(`登录尝试 ${retryCount + 1}/${maxRetries}`, '正在填充表单...');\n    }\n    await fillForm({\n      username: runtimeConfig.credentials.username,\n      password: runtimeConfig.credentials.password,\n      captcha: captcha\n    });\n    \n    // 提交登录\n    if (runtimeConfig.options.showProgress) {\n      showProgress(`登录尝试 ${retryCount + 1}/${maxRetries}`, '正在提交登录...');\n    }\n    await submitLogin();\n    \n    // 检查结果\n    const loginResult = await checkLoginResult();\n    \n    if (loginResult.success) {\n      console.log('🎉 登录成功！');\n      automaSetVariable('loginSuccess', true);\n      automaSetVariable('shouldRetry', false);\n      \n      if (runtimeConfig.options.showProgress) {\n        showProgress('登录成功', '准备执行后续动作...');\n      }\n      \n    } else {\n      retryCount++;\n      automaSetVariable('loginRetryCount', retryCount);\n      \n      if (retryCount < maxRetries) {\n        console.log(`❌ 登录失败: ${loginResult.error}`);\n        console.log(`🔄 准备第 ${retryCount + 1} 次重试...`);\n        automaSetVariable('shouldRetry', true);\n        \n        if (runtimeConfig.options.showProgress) {\n          showProgress('登录失败', `准备第 ${retryCount + 1} 次重试...`);\n        }\n        \n      } else {\n        console.error(`💥 登录最终失败，已尝试 ${maxRetries} 次`);\n        automaSetVariable('loginSuccess', false);\n        automaSetVariable('shouldRetry', false);\n        automaSetVariable('finalError', `登录失败: ${loginResult.error}`);\n      }\n    }\n    \n    // === 核心功能函数 ===\n    \n    async function waitForPageReady() {\n      const timeout = 15000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        const usernameInput = document.querySelector(runtimeConfig.selectors.username);\n        if (usernameInput && usernameInput.offsetParent !== null) {\n          await sleep(1000);\n          return;\n        }\n        await sleep(200);\n      }\n      throw new Error('页面加载超时');\n    }\n    \n    async function recognizeCaptcha() {\n      // 等待验证码图片\n      await waitForElement(runtimeConfig.selectors.captchaImage, 5000);\n      const img = document.querySelector(runtimeConfig.selectors.captchaImage);\n      \n      // 转换为Base64\n      const response = await fetch(img.src);\n      const blob = await response.blob();\n      const base64 = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsDataURL(blob);\n      });\n      \n      // 调用OCR\n      const ocrConfig = runtimeConfig.options.ocrConfig;\n      const ocr = await automaFetch('json', {\n        url: ocrConfig.url,\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          ...ocrConfig.headers\n        },\n        body: JSON.stringify({ imageData: base64 })\n      });\n      \n      if (!ocr?.success || !ocr.data?.text) {\n        throw new Error('OCR识别失败');\n      }\n      \n      return ocr.data.text.trim();\n    }\n    \n    async function fillForm(credentials) {\n      const fields = [\n        { name: 'username', selector: runtimeConfig.selectors.username, value: credentials.username },\n        { name: 'password', selector: runtimeConfig.selectors.password, value: credentials.password },\n        { name: 'captcha', selector: runtimeConfig.selectors.captcha, value: credentials.captcha }\n      ];\n      \n      for (const field of fields) {\n        const element = await waitForElement(field.selector, 3000);\n        \n        element.focus();\n        element.click();\n        await sleep(200);\n        \n        element.value = '';\n        element.value = field.value;\n        \n        ['input', 'change', 'blur'].forEach(eventType => {\n          element.dispatchEvent(new Event(eventType, { bubbles: true }));\n        });\n        \n        console.log(`✅ ${field.name}填充完成: ${field.value}`);\n        await sleep(300);\n      }\n    }\n    \n    async function submitLogin() {\n      const button = await waitForElement(runtimeConfig.selectors.loginButton, 3000);\n      \n      button.scrollIntoView({ block: 'center' });\n      await sleep(500);\n      button.click();\n      console.log('✅ 登录按钮已点击');\n    }\n    \n    async function checkLoginResult() {\n      await sleep(3000);\n      \n      // 检查URL变化\n      if (window.location.href.includes('/worktable') || \n          window.location.href.includes('/personal')) {\n        return { success: true };\n      }\n      \n      // 检查页面内容\n      const pageText = document.body.textContent || '';\n      if (pageText.includes('我的应用') || pageText.includes('工作台')) {\n        return { success: true };\n      }\n      \n      // 检查错误信息\n      const errorSelectors = ['.error-message', '[class*=\"error\"]', '.ant-message-error'];\n      for (const selector of errorSelectors) {\n        const errorEl = document.querySelector(selector);\n        if (errorEl && errorEl.textContent.trim()) {\n          return { success: false, error: errorEl.textContent.trim() };\n        }\n      }\n      \n      // 检查是否还在登录页面\n      if (document.querySelector(runtimeConfig.selectors.loginButton)) {\n        return { success: false, error: '仍在登录页面，可能是验证码错误' };\n      }\n      \n      return { success: false, error: '未知登录状态' };\n    }\n    \n    async function refreshCaptcha() {\n      if (runtimeConfig.selectors.captchaRefresh) {\n        const refresh = document.querySelector(runtimeConfig.selectors.captchaRefresh);\n        if (refresh) {\n          refresh.click();\n          await sleep(2000);\n          console.log('✅ 验证码已刷新');\n        }\n      }\n    }\n    \n    async function waitForElement(selector, timeout = 5000) {\n      const start = Date.now();\n      while (Date.now() - start < timeout) {\n        const element = document.querySelector(selector);\n        if (element && element.offsetParent !== null) return element;\n        await sleep(200);\n      }\n      throw new Error(`元素等待超时: ${selector}`);\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n    function showProgress(title, message) {\n      let div = document.getElementById('login-progress');\n      if (!div) {\n        div = document.createElement('div');\n        div.id = 'login-progress';\n        div.style.cssText = `\n          position: fixed; top: 20px; right: 20px;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white; padding: 15px; border-radius: 8px;\n          z-index: 99999; font-size: 14px;\n          box-shadow: 0 4px 15px rgba(0,0,0,0.3);\n          max-width: 300px;\n        `;\n        document.body.appendChild(div);\n      }\n      div.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 5px;\">🤖 ${title}</div>\n        <div style=\"font-size: 12px; opacity: 0.9;\">${message}</div>\n      `;\n    }\n    \n  } catch (error) {\n    console.error('❌ 登录引擎失败:', error);\n    \n    const retryCount = automaRefData('variables', 'loginRetryCount') || 0;\n    const maxRetries = (automaRefData('variables', 'runtimeConfig') || {}).options?.maxRetries || 3;\n    \n    if (retryCount < maxRetries - 1) {\n      automaSetVariable('shouldRetry', true);\n      console.log('🔄 将进行重试...');\n    } else {\n      automaSetVariable('loginSuccess', false);\n      automaSetVariable('shouldRetry', false);\n      console.log('💥 最终失败');\n    }\n    \n  } finally {\n    automaNextBlock();\n  }\n})();", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}, {"id": "retry-condition", "type": "BlockBasic", "initialized": false, "position": {"x": 972, "y": 300}, "data": {"description": "🔄 重试条件判断", "disableBlock": false, "conditions": [{"id": "retry-path", "name": "重试路径", "conditions": [{"id": "should-retry", "conditions": [{"id": "retry-check", "items": [{"id": "retry-val", "type": "value", "category": "value", "data": {}}, {"id": "retry-compare", "category": "compare"}, {"id": "retry-condition", "type": "value", "category": "value", "data": {"value": "return automaRefData('variables', 'shouldRetry') === true;"}}]}]}]}], "retryConditions": false, "retryCount": 10, "retryTimeout": 1000, "fallback": false}, "label": "conditions"}, {"id": "success-condition", "type": "BlockBasic", "initialized": false, "position": {"x": 1300, "y": 150}, "data": {"description": "✅ 成功条件判断", "disableBlock": false, "conditions": [{"id": "success-path", "name": "成功路径", "conditions": [{"id": "login-success", "conditions": [{"id": "success-check", "items": [{"id": "success-val", "type": "value", "category": "value", "data": {}}, {"id": "success-compare", "category": "compare"}, {"id": "success-condition", "type": "value", "category": "value", "data": {"value": "return automaRefData('variables', 'loginSuccess') === true;"}}]}]}]}], "retryConditions": false, "retryCount": 10, "retryTimeout": 1000, "fallback": true}, "label": "conditions"}, {"id": "navigate-to-target", "type": "BlockBasic", "initialized": false, "position": {"x": 1650, "y": 36}, "data": {"disableBlock": false, "description": "🎯 跳转目标页面", "url": "{{variables.loginConfig.postLoginAction.targetUrl}}", "userAgent": "", "active": true, "tabZoom": 1, "inGroup": false, "waitTabLoaded": true, "updatePrevTab": false, "customUserAgent": false}, "label": "new-tab"}, {"id": "universal-action-engine", "type": "BlockBasic", "initialized": false, "position": {"x": 1950, "y": 36}, "data": {"disableBlock": false, "description": "⚡ 通用动作引擎", "timeout": 15000, "context": "website", "code": "// === 通用动作执行引擎 ===\n(async () => {\n  try {\n    const runtimeConfig = automaRefData('variables', 'runtimeConfig');\n    const postAction = runtimeConfig.postLoginAction;\n    \n    console.log(`🎯 执行后续动作: ${postAction.type}`);\n    \n    // 等待页面加载\n    await waitForPageReady();\n    \n    // 根据动作类型执行不同逻辑\n    let actionResult = null;\n    \n    switch (postAction.type) {\n      case 'click_app_card':\n        actionResult = await executeClickAppCard(postAction);\n        break;\n        \n      case 'click_menu_item':\n        actionResult = await executeClickMenuItem(postAction);\n        break;\n        \n      case 'click_button':\n        actionResult = await executeClickButton(postAction);\n        break;\n        \n      case 'custom_click':\n        actionResult = await executeCustomClick(postAction);\n        break;\n        \n      default:\n        throw new Error(`未知的动作类型: ${postAction.type}`);\n    }\n    \n    if (actionResult.success) {\n      console.log('✅ 后续动作执行成功');\n      automaSetVariable('actionSuccess', true);\n    } else {\n      throw new Error(`动作执行失败: ${actionResult.error}`);\n    }\n    \n    // === 动作执行函数 ===\n    \n    async function executeClickAppCard(action) {\n      const containerSelector = '.my-app__item, .app-item, .application-card';\n      const apps = document.querySelectorAll(containerSelector);\n      \n      for (let i = 0; i < apps.length; i++) {\n        const text = apps[i].textContent.replace(/\\s+/g, ' ').trim();\n        console.log(`检查应用 ${i + 1}: ${text}`);\n        \n        if (text.includes(action.targetText)) {\n          // 使用模版生成选择器\n          const template = runtimeConfig.actionTemplates.click_app_card.template;\n          const selector = template.replace('{{position}}', i + 1);\n          \n          console.log(`✅ 找到目标应用，选择器: ${selector}`);\n          automaSetVariable('dynamicClickSelector', selector);\n          \n          // 尝试点击\n          const element = document.querySelector(selector);\n          if (element) {\n            element.scrollIntoView({ block: 'center' });\n            await sleep(500);\n            element.click();\n            return { success: true };\n          } else {\n            // 备用方案：直接点击卡片\n            apps[i].click();\n            return { success: true };\n          }\n        }\n      }\n      \n      return { success: false, error: `未找到应用: ${action.targetText}` };\n    }\n    \n    async function executeClickMenuItem(action) {\n      const menuItems = document.querySelectorAll('.nav-menu li a, .menu-item, .navigation a');\n      \n      for (let i = 0; i < menuItems.length; i++) {\n        const text = menuItems[i].textContent.trim();\n        if (text.includes(action.targetText)) {\n          const template = runtimeConfig.actionTemplates.click_menu_item.template;\n          const selector = template.replace('{{position}}', i + 1);\n          \n          console.log(`✅ 找到目标菜单，选择器: ${selector}`);\n          automaSetVariable('dynamicClickSelector', selector);\n          \n          menuItems[i].click();\n          return { success: true };\n        }\n      }\n      \n      return { success: false, error: `未找到菜单: ${action.targetText}` };\n    }\n    \n    async function executeClickButton(action) {\n      const buttons = document.querySelectorAll('button, .btn, input[type=\"button\"]');\n      \n      for (const button of buttons) {\n        if (button.textContent.includes(action.targetText)) {\n          button.click();\n          return { success: true };\n        }\n      }\n      \n      return { success: false, error: `未找到按钮: ${action.targetText}` };\n    }\n    \n    async function executeCustomClick(action) {\n      const selector = action.customSelector;\n      const element = document.querySelector(selector);\n      \n      if (element) {\n        element.scrollIntoView({ block: 'center' });\n        await sleep(500);\n        element.click();\n        \n        automaSetVariable('dynamicClickSelector', selector);\n        return { success: true };\n      }\n      \n      return { success: false, error: `未找到元素: ${selector}` };\n    }\n    \n    async function waitForPageReady() {\n      const start = Date.now();\n      while (Date.now() - start < 10000) {\n        if (document.readyState === 'complete') {\n          await sleep(1000);\n          return;\n        }\n        await sleep(500);\n      }\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n  } catch (error) {\n    console.error('❌ 动作引擎失败:', error);\n    automaSetVariable('actionSuccess', false);\n    automaSetVariable('actionError', error.message);\n  } finally {\n    automaNextBlock();\n  }\n})();", "preloadScripts": [], "everyNewTab": false, "runBeforeLoad": false}, "label": "javascript-code"}], "edges": [{"id": "trigger-to-loader", "type": "custom", "source": "trigger-universal", "target": "template-loader", "sourceHandle": "trigger-universal-output-1", "targetHandle": "template-loader-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}, {"id": "loader-to-open", "type": "custom", "source": "template-loader", "target": "open-login-page", "sourceHandle": "template-loader-output-1", "targetHandle": "open-login-page-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}, {"id": "open-to-engine", "type": "custom", "source": "open-login-page", "target": "universal-login-engine", "sourceHandle": "open-login-page-output-1", "targetHandle": "universal-login-engine-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}, {"id": "engine-to-retry", "type": "custom", "source": "universal-login-engine", "target": "retry-condition", "sourceHandle": "universal-login-engine-output-1", "targetHandle": "retry-condition-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}, {"id": "retry-to-engine", "type": "custom", "source": "retry-condition", "target": "universal-login-engine", "sourceHandle": "retry-condition-output-1", "targetHandle": "universal-login-engine-input-1", "updatable": true, "selectable": true, "data": {}, "label": "重试", "markerEnd": "arrowclosed"}, {"id": "retry-to-success", "type": "custom", "source": "retry-condition", "target": "success-condition", "sourceHandle": "retry-condition-output-2", "targetHandle": "success-condition-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}, {"id": "success-to-navigate", "type": "custom", "source": "success-condition", "target": "navigate-to-target", "sourceHandle": "success-condition-output-1", "targetHandle": "navigate-to-target-input-1", "updatable": true, "selectable": true, "data": {}, "label": "成功", "markerEnd": "arrowclosed"}, {"id": "navigate-to-action", "type": "custom", "source": "navigate-to-target", "target": "universal-action-engine", "sourceHandle": "navigate-to-target-output-1", "targetHandle": "universal-action-engine-input-1", "updatable": true, "selectable": true, "data": {}, "label": "", "markerEnd": "arrowclosed"}]}, "settings": {"publicId": "universal-login-template", "blockDelay": 0, "saveLog": true, "debugMode": false, "restartTimes": 3, "notification": true, "execContext": "popup", "reuseLastState": false, "inputAutocomplete": true, "onError": "stop-workflow", "executedBlockOnWeb": false, "insertDefaultColumn": false, "defaultColumnName": "column", "tabLoadTimeout": 30000}, "globalData": "{\n\t\"workflowType\": \"universal-login-template\",\n\t\"version\": \"2.0.0\"\n}", "description": "🌐 通用登录模版系统：支持多网站、模版化配置、动态选择器生成", "includedWorkflows": {}}
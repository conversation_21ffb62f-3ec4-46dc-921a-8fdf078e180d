# Automa工作流参数使用指南

## 📋 概述

本文档说明如何在Automa工作流中接收和使用从账号管理系统传递的参数。

## 🚀 新版参数格式

系统现在使用标准化的RPA参数格式：

```javascript
const rpaParams = {
  detail: {
    publicId: 'your-workflow-id',
    data: {
      variables: {
        inParams: {
          // 执行参数
        },
        extraParams: {
          // 配置参数  
        },
      },
    },
  },
}

window.dispatchEvent(new CustomEvent('automa:execute-workflow', rpaParams))
```

## 📊 参数结构详解

### inParams - 执行参数

这些是RPA工作流执行时需要的核心参数：

#### 核心登录信息
- `loginUrl` - 登录页面URL
- `username` - 用户名
- `password` - 密码
- `accountName` - 账号名称
- `accountId` - 账号ID

#### 账号基础信息
- `platform` - 平台名称
- `description` - 账号描述
- `notes` - 备注信息

#### XPath配置信息
- `usernameXpath` - 用户名输入框XPath
- `passwordXpath` - 密码输入框XPath
- `loginButtonXpath` - 登录按钮XPath
- `captchaXpath` - 验证码图片XPath
- `captchaInputXpath` - 验证码输入框XPath

#### 验证码配置
- `captchaType` - 验证码类型

#### 插件配置
- `pluginConfig` - 插件配置对象

#### 状态信息
- `isActive` - 是否激活
- `createdAt` - 创建时间
- `updatedAt` - 更新时间

### extraParams - 配置参数

这些是额外的系统和环境配置参数：

#### 工作流元信息
- `workflowId` - 工作流ID
- `version` - 版本号
- `source` - 来源系统

#### 环境信息
- `environment` - 环境标识
- `userAgent` - 浏览器User Agent
- `currentUrl` - 当前页面URL
- `timestamp` - 时间戳

#### 用户信息
- `userId` - 用户ID

#### 调试信息
- `debug` - 是否启用调试
- `logLevel` - 日志级别

## 🔧 在Automa中配置事件监听

### 1. 添加事件监听器

在工作流开始时添加JavaScript块：

```javascript
// 监听账号管理系统的工作流触发事件
window.addEventListener('automa:execute-workflow', function(event) {
  console.log('接收到工作流执行事件:', event);
  
  // 获取参数
  const { publicId, data } = event.detail;
  const { inParams, extraParams } = data.variables;
  
  // 将参数存储到工作流变量中
  automaSetVariable('loginUrl', inParams.loginUrl);
  automaSetVariable('username', inParams.username);
  automaSetVariable('password', inParams.password);
  automaSetVariable('accountName', inParams.accountName);
  
  // 存储更多参数...
  automaSetVariable('usernameXpath', inParams.usernameXpath);
  automaSetVariable('passwordXpath', inParams.passwordXpath);
  automaSetVariable('loginButtonXpath', inParams.loginButtonXpath);
  
  // 环境信息
  automaSetVariable('environment', extraParams.environment);
  automaSetVariable('debug', extraParams.debug);
  
  console.log('参数已设置完成');
});
```

### 2. 使用变量

在后续的工作流步骤中，直接使用变量：

#### 打开页面
- URL: `{{loginUrl}}`

#### 输入用户名
- 选择器: `{{usernameXpath}}`
- 值: `{{username}}`

#### 输入密码
- 选择器: `{{passwordXpath}}`
- 值: `{{password}}`

#### 点击登录按钮
- 选择器: `{{loginButtonXpath}}`

## 📝 完整工作流示例

```javascript
// 1. 事件监听和参数接收
window.addEventListener('automa:execute-workflow', function(event) {
  const { inParams, extraParams } = event.detail.data.variables;
  
  // 设置所有需要的变量
  automaSetVariable('loginUrl', inParams.loginUrl);
  automaSetVariable('username', inParams.username);
  automaSetVariable('password', inParams.password);
  automaSetVariable('usernameXpath', inParams.usernameXpath);
  automaSetVariable('passwordXpath', inParams.passwordXpath);
  automaSetVariable('loginButtonXpath', inParams.loginButtonXpath);
  automaSetVariable('accountName', inParams.accountName);
  automaSetVariable('debug', extraParams.debug);
  
  console.log(`开始执行账号 ${inParams.accountName} 的登录流程`);
});

// 2. 后续步骤直接使用变量
// - Tab打开: {{loginUrl}}
// - 输入文本到 {{usernameXpath}}: {{username}}
// - 输入文本到 {{passwordXpath}}: {{password}}
// - 点击 {{loginButtonXpath}}
```

## 🐛 调试技巧

### 检查参数接收
```javascript
window.addEventListener('automa:execute-workflow', function(event) {
  console.log('=== Automa参数接收调试 ===');
  console.log('完整事件:', event);
  console.log('publicId:', event.detail.publicId);
  console.log('inParams:', event.detail.data.variables.inParams);
  console.log('extraParams:', event.detail.data.variables.extraParams);
  console.log('=== 调试结束 ===');
});
```

### 验证变量设置
```javascript
// 设置变量后验证
automaSetVariable('loginUrl', inParams.loginUrl);
console.log('已设置loginUrl:', automaGetVariable('loginUrl'));
```

## ⚠️ 注意事项

1. **安全性**: 密码字段已加密，在automa中使用时注意保护
2. **XPath**: 确保XPath选择器在目标网站中有效
3. **调试**: 生产环境中关闭详细调试日志
4. **错误处理**: 添加适当的错误处理逻辑

## 🔄 升级说明

从旧版本升级时：
- 旧的 `event.detail.id` 现在是 `event.detail.publicId`
- 旧的 `event.detail.data.loginUrl` 现在是 `event.detail.data.variables.inParams.loginUrl`
- 所有参数都在 `variables.inParams` 和 `variables.extraParams` 中

## 📞 技术支持

如有问题，请查看：
1. 浏览器控制台的调试日志
2. Automa工作流的执行日志
3. 联系开发团队获取支持 
const fs = require('fs');
const path = require('path');

// 1. 移除API目录
const apiPath = path.join(__dirname, 'app/api');
const backupPath = path.join(__dirname, 'api_backup_temp');

if (fs.existsSync(apiPath)) {
  if (fs.existsSync(backupPath)) {
    fs.rmSync(backupPath, { recursive: true, force: true });
  }
  fs.renameSync(apiPath, backupPath);
  console.log('API目录已移除，现在可以运行构建了');
  console.log('请运行: npx next build');
} else {
  console.log('API目录不存在');
}

// 检查是否已有out目录
const outPath = path.join(__dirname, 'out');
if (fs.existsSync(outPath)) {
  console.log('out目录已存在');
  const files = fs.readdirSync(outPath);
  console.log('包含文件:', files);
}
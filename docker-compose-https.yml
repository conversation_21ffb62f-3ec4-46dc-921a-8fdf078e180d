version: '3.8'

services:
  nginx-https:
    image: nginx:alpine
    container_name: account-manage-nginx-https
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./out:/opt/account-manage:ro
      - ./ssl:/opt/ssl:ro
      - ./nginx-https.conf:/etc/nginx/nginx.conf:ro
    restart: unless-stopped
    networks:
      - account-manage-net

networks:
  account-manage-net:
    driver: bridge

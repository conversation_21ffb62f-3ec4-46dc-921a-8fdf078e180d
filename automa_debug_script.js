// --- 调试版：页面元素分析脚本 ---
// 用于分析页面结构，找出正确的选择器

try {
    console.log('🔍 === 页面元素分析开始 ===');
    
    // 1. 等待页面加载
    console.log('⏳ 等待页面加载完成...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 2. 尝试各种可能的选择器
    const possibleSelectors = [
        'div.my-app__item',
        '.my-app__item', 
        'div[class*="my-app"]',
        'div[class*="item"]',
        '[class*="card"]',
        '[class*="item"]',
        '.card',
        '.item',
        'div.my-app_item'  // 单下划线版本
    ];
    
    console.log('🔍 测试各种选择器...');
    
    let foundElements = [];
    
    for (const selector of possibleSelectors) {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
            console.log(`✅ "${selector}" 找到 ${elements.length} 个元素`);
            foundElements.push({
                selector: selector,
                count: elements.length,
                elements: Array.from(elements)
            });
        } else {
            console.log(`❌ "${selector}" 未找到元素`);
        }
    }
    
    // 3. 如果找到元素，分析它们的内容
    if (foundElements.length > 0) {
        const bestMatch = foundElements[0]; // 选择第一个找到的
        console.log(`📋 使用选择器 "${bestMatch.selector}" 分析内容:`);
        
        bestMatch.elements.forEach((element, index) => {
            const text = element.textContent.trim();
            const hasTarget = text.includes('分时预约系统');
            console.log(`  ${index + 1}. ${hasTarget ? '🎯' : '⚪'} "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`);
            
            if (hasTarget) {
                // 找到目标，生成选择器
                const targetSelector = `${bestMatch.selector}:nth-child(${index + 1})`;
                console.log(`🎯 找到目标！生成选择器: ${targetSelector}`);
                automaSetVariable('debugTargetSelector', targetSelector);
                automaSetVariable('debugTargetIndex', index + 1);
                automaSetVariable('debugTargetText', text);
            }
        });
    }
    
    // 4. 尝试查找所有可能的点击目标
    const clickableSelectors = [
        'button',
        '[role="button"]', 
        '.btn',
        'a',
        '[onclick]',
        '.my-app__item-footer',
        '.my-app__item-footer button'
    ];
    
    console.log('🖱️ 查找可点击元素...');
    for (const clickSelector of clickableSelectors) {
        const clickables = document.querySelectorAll(clickSelector);
        if (clickables.length > 0) {
            console.log(`🖱️ "${clickSelector}" 找到 ${clickables.length} 个可点击元素`);
        }
    }
    
    // 5. 输出页面的整体结构信息
    console.log('📊 页面信息汇总:');
    console.log(`  - URL: ${window.location.href}`);
    console.log(`  - Title: ${document.title}`);
    console.log(`  - Ready State: ${document.readyState}`);
    console.log(`  - All divs: ${document.querySelectorAll('div').length}`);
    console.log(`  - All classes with "app": ${document.querySelectorAll('[class*="app"]').length}`);
    console.log(`  - All classes with "item": ${document.querySelectorAll('[class*="item"]').length}`);
    
    automaSetVariable('debugComplete', 'true');
    
} catch (error) {
    console.error('🚨 调试脚本错误:', error);
    automaSetVariable('debugError', error.message);
} finally {
    console.log('🏁 === 页面元素分析完成 ===');
    automaNextBlock();
} 
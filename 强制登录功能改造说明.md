# 🔧 强制登录功能改造说明

## 🎯 改造目标

将强制登录功能从 API 接口模式改为直接操作 Supabase 模式，解决静态构建后 API 接口无法使用的问题。

## 📊 改造前后对比

### 改造前 (API 接口模式)
```javascript
// 前端调用 API
const response = await fetch('/api/account/update-force-login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ accountId: account.id, forceLogin: newForceLogin })
});
```

**问题**：
- ❌ 静态构建后 API 接口被排除
- ❌ 需要额外的 API 路由文件
- ❌ 增加了部署复杂度

### 改造后 (直接操作 Supabase 模式)
```javascript
// 前端直接操作 Supabase
const { toggleForceLogin } = await import('../lib/supabase.js');
const updatedAccount = await toggleForceLogin(account.id);
```

**优势**：
- ✅ 静态构建后仍可正常工作
- ✅ 无需 API 接口，减少部署复杂度
- ✅ 与编辑、复制功能保持一致
- ✅ 更好的错误处理和权限控制

## 🔄 改造内容

### 1. 新增 Supabase 函数

**文件**: `lib/supabase.js`

```javascript
// 切换强制登录状态 - 直接操作Supabase，无需API接口
export async function toggleForceLogin(id) {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error('用户未登录');

  // 检查是否为超管
  const isAdmin = user.email === '<EMAIL>';
  
  // 1. 先获取当前账号的强制登录状态
  let query = supabase
    .from('accounts')
    .select('force_login, user_id')
    .eq('id', id)
    .eq('is_deleted', false)
    .single();

  // 权限控制：超管可以更新任何账号，普通用户只能更新自己的账号
  if (!isAdmin) {
    query = query.eq('user_id', user.id);
  }

  const { data: currentAccount, error: fetchError } = await query;
  
  if (fetchError) throw fetchError;
  if (!currentAccount) throw new Error('账号不存在或无权限访问');

  // 2. 切换强制登录状态
  const newForceLogin = !currentAccount.force_login;
  
  // 3. 更新强制登录状态
  const { data, error } = await supabase
    .from('accounts')
    .update({ 
      force_login: newForceLogin,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .eq('is_deleted', false)
    .select()
    .single();

  if (error) throw error;
  return data;
}
```

### 2. 修改前端调用逻辑

**文件**: `app/page.tsx`

```javascript
// 切换强制登录状态 - 直接操作Supabase，无需API接口
const handleToggleForceLogin = async (account: any) => {
  try {
    const { toggleForceLogin } = await import('../lib/supabase.js');
    const updatedAccount = await toggleForceLogin(account.id);
    
    // 更新本地状态
    setAccounts(prevAccounts => 
      prevAccounts.map(acc => 
        acc.id === account.id 
          ? { ...acc, force_login: updatedAccount.force_login }
          : acc
      )
    );
    
    // 静默更新，不显示弹框
    console.log(`账号 ${account.name} 的强制登录已${updatedAccount.force_login ? '启用' : '禁用'}`);
  } catch (error) {
    console.error('切换强制登录失败:', error);
    alert('切换强制登录失败: ' + (error instanceof Error ? error.message : String(error)));
  }
};
```

### 3. 删除不再需要的文件

**已删除**: `app/api/account/update-force-login/route.ts`

## 🛡️ 权限控制

### 管理员权限
- ✅ 可以切换任何账号的强制登录状态
- ✅ 不受用户ID限制

### 普通用户权限
- ✅ 只能切换自己创建的账号
- ✅ 受 RLS (Row Level Security) 保护
- ❌ 无法切换他人账号

## 🧪 测试验证

### 测试脚本
**文件**: `scripts/test-force-login.js`

运行测试：
```bash
node scripts/test-force-login.js
```

### 测试内容
1. ✅ 用户登录验证
2. ✅ 账号查询验证
3. ✅ 强制登录状态切换
4. ✅ 权限控制验证
5. ✅ 数据库更新验证

## 🚀 部署优势

### 静态构建兼容性
- ✅ 无需 API 路由，完全兼容静态构建
- ✅ 可以直接部署到 CDN
- ✅ 减少服务器资源占用

### 架构简化
- ✅ 减少 API 层复杂度
- ✅ 统一数据操作方式
- ✅ 更好的错误处理

### 性能提升
- ✅ 减少网络请求
- ✅ 直接数据库操作
- ✅ 更快的响应速度

## 📝 使用说明

### 前端使用
```javascript
// 导入函数
const { toggleForceLogin } = await import('../lib/supabase.js');

// 切换强制登录状态
const updatedAccount = await toggleForceLogin(accountId);

// 更新本地状态
setAccounts(prevAccounts => 
  prevAccounts.map(acc => 
    acc.id === accountId 
      ? { ...acc, force_login: updatedAccount.force_login }
      : acc
  )
);
```

### 错误处理
```javascript
try {
  const updatedAccount = await toggleForceLogin(accountId);
  // 成功处理
} catch (error) {
  // 错误处理
  console.error('切换失败:', error.message);
  alert('切换强制登录失败: ' + error.message);
}
```

## 🎉 总结

通过这次改造，强制登录功能现在：

1. **完全兼容静态构建** - 不再依赖 API 接口
2. **架构更简洁** - 与编辑、复制功能保持一致
3. **权限控制更严格** - 基于 RLS 的用户权限验证
4. **错误处理更完善** - 详细的错误信息和用户提示
5. **部署更简单** - 减少服务器依赖，支持纯静态部署

这个改造解决了你遇到的静态构建后 API 接口无法使用的问题，同时提升了整体架构的一致性和可维护性。 
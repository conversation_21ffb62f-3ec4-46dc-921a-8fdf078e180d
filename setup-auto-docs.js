#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 自动文档系统安装器
console.log('🚀 自动文档维护系统安装器\n');

// 1. 创建必要的目录结构
function createDirectories() {
  const dirs = [
    'docs',
    'docs/active',
    'docs/archive',
    'docs/archive/by-time',
    'docs/archive/by-feature',
    'scripts'
  ];
  
  console.log('📁 创建目录结构...');
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`  ✅ ${dir}/`);
    } else {
      console.log(`  📁 ${dir}/ (已存在)`);
    }
  });
}

// 2. 创建核心脚本文件
function createScripts() {
  console.log('\n🔧 创建核心脚本...');
  
  // auto-docs.js 核心脚本
  const autoDocsScript = `#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 自动创建新需求文档
function createActiveDoc(name, module, description = '') {
  const activeDir = path.join('docs', 'active', name);
  
  if (!fs.existsSync(activeDir)) {
    fs.mkdirSync(activeDir, { recursive: true });
  }
  
  const date = new Date().toISOString().split('T')[0];
  
  // PRD模板
  const prdContent = \`# \${name} - PRD

## 基本信息
- 创建时间: \${date}
- 状态: 开发中
- 模块: \${module}

## 需求描述
\${description}

## 功能要求
待AI分析补充...

## 验收标准
待AI生成...

## 相关文档
- [TSD技术方案](./tsd.md)
- [DevLog开发日志](./devlog.md)
\`;

  // TSD模板
  const tsdContent = \`# \${name} - TSD

## 基本信息
- 创建时间: \${date}
- 对应PRD: [需求文档](./prd.md)
- 模块: \${module}

## 技术方案
待AI根据代码分析生成...

## 实现方案
开发过程中自动完善...

## 风险点
待AI自动识别...

## 相关文档
- [PRD需求文档](./prd.md)
- [DevLog开发日志](./devlog.md)
\`;

  // DevLog模板
  const devlogContent = \`# \${name} - DevLog

## 基本信息
- 创建时间: \${date}
- 对应PRD: [需求文档](./prd.md)
- 对应TSD: [技术方案](./tsd.md)

## \${date} 开发记录

### 实现功能
开始开发...

### 技术细节
待AI自动提取关键实现...

### 问题解决
待AI自动记录问题和解决过程...

## 相关文档
- [PRD需求文档](./prd.md)
- [TSD技术方案](./tsd.md)
\`;

  // 写入文件
  fs.writeFileSync(path.join(activeDir, 'prd.md'), prdContent);
  fs.writeFileSync(path.join(activeDir, 'tsd.md'), tsdContent);
  fs.writeFileSync(path.join(activeDir, 'devlog.md'), devlogContent);
  
  console.log(\`✅ 自动创建文档: docs/active/\${name}/\`);
  return activeDir;
}

// 更新DevLog
function updateDevLog(name, content) {
  const devlogPath = path.join('docs', 'active', name, 'devlog.md');
  
  if (!fs.existsSync(devlogPath)) {
    console.log(\`⚠️  DevLog不存在: \${devlogPath}\`);
    return;
  }
  
  const time = new Date().toLocaleTimeString();
  const existingContent = fs.readFileSync(devlogPath, 'utf8');
  const newEntry = \`\\n### \${time} 更新\\n\${content}\\n\`;
  
  fs.writeFileSync(devlogPath, existingContent + newEntry);
  console.log(\`📝 更新DevLog: \${name}\`);
}

// 归档完成的文档
function archiveDoc(name, module) {
  const activeDir = path.join('docs', 'active', name);
  
  if (!fs.existsSync(activeDir)) {
    console.log(\`⚠️  活动文档不存在: \${activeDir}\`);
    return;
  }
  
  const date = new Date().toISOString().split('T')[0];
  const year = date.split('-')[0];
  const month = date.split('-')[1];
  
  // 时间维度归档
  const timeArchiveDir = path.join('docs', 'archive', 'by-time', year, \`\${month}-\${name}\`);
  
  // 功能维度归档
  const featureArchiveDir = path.join('docs', 'archive', 'by-feature', module, \`\${date}-\${name}\`);
  
  // 确保目录存在
  fs.mkdirSync(timeArchiveDir, { recursive: true });
  fs.mkdirSync(featureArchiveDir, { recursive: true });
  
  // 移动文件
  ['prd.md', 'tsd.md', 'devlog.md'].forEach(file => {
    const srcPath = path.join(activeDir, file);
    const destPath = path.join(timeArchiveDir, file);
    
    if (fs.existsSync(srcPath)) {
      fs.renameSync(srcPath, destPath);
      
      // 在功能归档中创建符号链接
      const featureLinkPath = path.join(featureArchiveDir, file);
      try {
        if (process.platform === 'win32') {
          fs.linkSync(destPath, featureLinkPath);
        } else {
          const relativePath = path.relative(path.dirname(featureLinkPath), destPath);
          fs.symlinkSync(relativePath, featureLinkPath);
        }
      } catch (error) {
        fs.copyFileSync(destPath, featureLinkPath);
      }
    }
  });
  
  // 删除active目录
  fs.rmSync(activeDir, { recursive: true, force: true });
  
  console.log(\`🗂️  文档已归档: \${name}\`);
  return { timeArchiveDir, featureArchiveDir };
}

// 智能分类功能
function identifyModule(name, description = '') {
  const text = (name + ' ' + description).toLowerCase();
  
  // 默认模块映射
  const moduleMap = {
    '账号管理': ['账号', '登录', '权限', '管理', 'account', 'login', 'auth', '用户'],
    '验证码识别': ['验证码', '滑块', 'ocr', 'captcha', '识别', 'baidu', '图像'],
    '插件管理': ['插件', 'plugin', 'chrome', '扩展', 'extension'],
    '部署运维': ['docker', 'nginx', '部署', 'deploy', '服务', '静态', '环境', '配置'],
    '支付管理': ['支付', '订单', '交易', '结算', '费用'],
    '消息系统': ['消息', '通知', '推送', '邮件', 'sms'],
    '数据分析': ['数据', '统计', '报表', '分析', 'chart'],
    '文件管理': ['文件', '上传', '下载', '存储', 'upload']
  };
  
  // 尝试加载自定义配置
  try {
    const configPath = path.join('docs', 'modules-config.json');
    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      Object.assign(moduleMap, config.customModules || {});
    }
  } catch (error) {
    // 忽略配置加载错误
  }
  
  // 匹配模块
  for (const [module, keywords] of Object.entries(moduleMap)) {
    if (keywords.some(keyword => text.includes(keyword))) {
      return module;
    }
  }
  
  return '其他';
}

// 命令行接口
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'create':
      const name = args[1];
      const description = args[2] || '';
      const module = identifyModule(name, description);
      createActiveDoc(name, module, description);
      break;
      
    case 'update-devlog':
      updateDevLog(args[1], args[2]);
      break;
      
    case 'archive':
      const moduleName = identifyModule(args[1]);
      archiveDoc(args[1], moduleName);
      break;
      
    default:
      console.log(\`
使用方法:
  node scripts/auto-docs.js create "功能名称" "描述"
  node scripts/auto-docs.js update-devlog "功能名称" "更新内容"
  node scripts/auto-docs.js archive "功能名称"
      \`);
  }
}

if (require.main === module) {
  main();
}

module.exports = { createActiveDoc, updateDevLog, archiveDoc, identifyModule };
`;

  fs.writeFileSync('scripts/auto-docs.js', autoDocsScript);
  console.log('  ✅ scripts/auto-docs.js');
}

// 3. 创建Cursor Rules文件
function createCursorRules() {
  console.log('\n📋 创建Cursor Rules配置...');
  
  const cursorRules = `# Cursor Rules - 自动文档维护工作流

## 🤖 AI助手行为规范

### 核心原则
- 你是天才程序员，保持质疑和批判思维
- 编程过程中**自动维护文档**，无需用户手动操作
- 文档维护是编程流程的一部分，不是额外工作
- 始终使用中文交流

## 📋 自动文档维护流程

### 1. 检测新需求时 - 自动创建文档

**触发条件**: 用户消息包含"新功能"、"需求"、"开发"、"实现"、"添加功能"等关键词

**AI必须执行**:
\`\`\`
1. 运行: node scripts/auto-docs.js create "功能名称" "需求描述"
2. 告知用户文档已创建
3. 开始记录开发过程
\`\`\`

### 2. 编程过程中 - 实时更新记录

**触发条件**: 每次使用edit_file工具修改代码后

**AI必须执行**:
\`\`\`
1. 运行: node scripts/auto-docs.js update-devlog "功能名称" "本次修改内容"
2. 自动记录技术实现细节
3. 记录遇到的问题和解决方案
\`\`\`

### 3. 功能完成时 - 自动归档

**触发条件**: 用户说"完成"、"结束"、"功能做完了"等

**AI必须执行**:
\`\`\`
1. 运行: node scripts/auto-docs.js archive "功能名称"
2. 告知用户文档已归档
3. 更新项目文档索引
\`\`\`

## 🔧 文档结构

\`\`\`
docs/
├── active/                 # 🔥 开发中的文档
│   └── 功能名称/
│       ├── prd.md         # 需求文档
│       ├── tsd.md         # 技术方案
│       └── devlog.md      # 开发日志
├── archive/               # 📚 已完成的文档
│   ├── by-time/          # 按时间查找
│   └── by-feature/       # 按功能查找
└── modules-config.json    # 自定义模块配置
\`\`\`

## ⚙️ AI助手必须执行的动作

### 检测新需求示例:
\`\`\`
用户: "我要开发一个用户登录优化功能"
AI: ✅ 检测到新需求，自动创建文档...
    [运行] node scripts/auto-docs.js create "用户登录优化" "优化用户登录体验"
    📝 文档已创建: docs/active/用户登录优化/
    🚀 开始开发...
\`\`\`

### 代码修改后示例:
\`\`\`
[AI刚使用edit_file修改了Login.tsx]
AI: 🔄 正在更新开发日志...
    [运行] node scripts/auto-docs.js update-devlog "用户登录优化" "修改Login.tsx，添加记住密码功能"
    📝 已记录到DevLog
\`\`\`

### 功能完成示例:
\`\`\`
用户: "登录功能完成了"
AI: ✨ 功能完成，正在归档文档...
    [运行] node scripts/auto-docs.js archive "用户登录优化"
    🗂️ 文档已归档到时间和功能索引
\`\`\`

## 🎯 关键要点

1. **自动性**: AI必须主动执行，不需要用户提醒
2. **实时性**: 每次代码修改都要记录
3. **完整性**: 需求→开发→完成的完整流程
4. **智能性**: 自动识别功能模块分类
`;

  fs.writeFileSync('.cursor-rules-auto-docs', cursorRules);
  console.log('  ✅ .cursor-rules-auto-docs');
}

// 4. 创建README说明
function createReadme() {
  console.log('\n📖 创建使用说明...');
  
  const readme = `# 🤖 自动文档维护系统

## 🎯 功能特性

- ✅ **自动检测新需求** - AI识别关键词自动创建文档
- ✅ **实时记录开发过程** - 每次代码修改自动更新DevLog
- ✅ **智能功能分类** - 自动识别功能模块并分类
- ✅ **自动归档整理** - 完成后自动移动到双重索引结构
- ✅ **零手动操作** - 完全集成到编程工作流

## 🚀 快速开始

### 1. 复制Cursor Rules
将 \`.cursor-rules-auto-docs\` 的内容复制到你的Cursor Rules设置中

### 2. 开始使用
正常提出需求，AI会自动维护文档：

\`\`\`
你: "我要开发一个用户权限管理功能"
AI: ✅ 检测到新需求，自动创建文档...
    📝 PRD已创建: docs/active/用户权限管理/prd.md
    🔧 TSD已创建: docs/active/用户权限管理/tsd.md
    📋 DevLog已创建: docs/active/用户权限管理/devlog.md
    🚀 开始开发...

[编程过程中AI自动记录每个步骤]

你: "功能完成了"
AI: ✨ 功能完成，正在归档...
    🗂️ 已移动到双重索引结构
\`\`\`

## 📂 文档结构

\`\`\`
docs/
├── active/                 # 开发中的文档
├── archive/               # 已完成的文档
│   ├── by-time/          # 按时间查找 
│   └── by-feature/       # 按功能查找
└── modules-config.json    # 功能模块配置
\`\`\`

## 🛠 手动命令

虽然主要是自动化，但也支持手动操作：

\`\`\`bash
# 创建新文档
node scripts/auto-docs.js create "功能名称" "描述"

# 更新开发日志
node scripts/auto-docs.js update-devlog "功能名称" "更新内容"

# 归档完成的功能
node scripts/auto-docs.js archive "功能名称"
\`\`\`

## 🎯 核心优势

1. **无缝集成** - 不影响正常编程流程
2. **自动维护** - 无需记住复杂命令
3. **完整记录** - PRD→TSD→DevLog完整链路
4. **智能分类** - 自动识别功能模块
5. **双重索引** - 时间和功能两种查找方式

---

💡 **这就是真正的"自动文档维护"** - 你编程，AI记录！
`;

  fs.writeFileSync('docs/README.md', readme);
  console.log('  ✅ docs/README.md');
}

// 5. 创建默认配置文件
function createConfig() {
  console.log('\n⚙️ 创建默认配置...');
  
  const config = {
    "lastUpdated": new Date().toISOString(),
    "defaultModules": {
      "账号管理": ["账号", "登录", "权限", "管理", "account", "login", "auth", "用户"],
      "验证码识别": ["验证码", "滑块", "ocr", "captcha", "识别", "baidu", "图像"],
      "插件管理": ["插件", "plugin", "chrome", "扩展", "extension"],
      "部署运维": ["docker", "nginx", "部署", "deploy", "服务", "静态", "环境", "配置"],
      "支付管理": ["支付", "订单", "交易", "结算", "费用"],
      "消息系统": ["消息", "通知", "推送", "邮件", "sms"],
      "数据分析": ["数据", "统计", "报表", "分析", "chart"],
      "文件管理": ["文件", "上传", "下载", "存储", "upload"]
    },
    "customModules": {}
  };
  
  fs.writeFileSync('docs/modules-config.json', JSON.stringify(config, null, 2));
  console.log('  ✅ docs/modules-config.json');
}

// 主安装流程
function install() {
  console.log('开始安装自动文档维护系统...\n');
  
  createDirectories();
  createScripts();
  createCursorRules();
  createReadme();
  createConfig();
  
  console.log('\n🎉 安装完成!\n');
  console.log('📋 下一步操作:');
  console.log('1. 将 .cursor-rules-auto-docs 的内容复制到你的Cursor Rules中');
  console.log('2. 开始正常编程，AI会自动维护文档');
  console.log('3. 查看 docs/README.md 了解详细使用方法\n');
  console.log('💡 现在你可以说 "我要开发一个xxx功能" 来测试系统!');
}

// 运行安装
install(); 
{"name": "account-manage", "version": "1.0.0", "description": "账号管理和自动登录系统", "main": "electron/main.js", "scripts": {"dev": "next dev -p 3001", "dev:next": "next dev -p 3001", "build": "next build", "build:static": "node scripts/build-static.js", "start": "next start -p 3001", "lint": "next lint", "check-db": "node scripts/check-database.js", "add-test-account": "node scripts/add-test-account.js", "debug-accounts": "node scripts/debug-accounts.js", "move-accounts": "node scripts/move-accounts.js", "check-password": "node scripts/check-password-format.js", "test-update": "node scripts/test-account-update.js", "switch:dev": "node scripts/switch-env.js dev", "switch:prod": "node scripts/switch-env.js prod", "prepare-migration": "node scripts/prepare-migration.js", "prepare-migration:advanced": "node scripts/prepare-migration-advanced.js", "migrate:db": "node scripts/prepare-migration-advanced.js --db", "migrate:accounts": "node scripts/prepare-migration-advanced.js --accounts", "migrate:config": "node scripts/prepare-migration-advanced.js --config", "update-docker-config": "node scripts/update-remote-docker-config.js", "sync-to-prod": "node scripts/sync-to-prod.js", "sync-all": "node scripts/sync-all-files.js", "init-account-types": "node scripts/init-account-types.js"}, "keywords": ["electron", "nextjs", "supabase", "automation", "playwright"], "author": "Account Manager", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.38.5", "axios": "^1.6.0", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "framer-motion": "^10.16.5", "next": "14.0.4", "playwright": "^1.53.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}, "build": {"appId": "com.accountmanager.app", "productName": "Account Manager", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}
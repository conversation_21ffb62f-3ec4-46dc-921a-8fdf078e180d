-- 更新账号业务类型脚本
-- 这个脚本用于将账号的business_type字段更新为正确的值

-- 首先查看当前的business_type分布
SELECT 
  business_type,
  COUNT(*) as count,
  STRING_AGG(name, ', ') as account_names
FROM accounts 
GROUP BY business_type 
ORDER BY business_type;

-- 查看具体的部署相关账号（通过名称判断）
SELECT id, name, business_type, login_type
FROM accounts 
WHERE name ILIKE '%部署%' 
   OR name ILIKE '%deploy%'
   OR login_type = 'one-test'
   OR task_id IS NOT NULL
   OR platform_name IS NOT NULL;

-- 如果发现部署账号的business_type不是5，运行以下更新语句：
-- UPDATE accounts 
-- SET business_type = 5 
-- WHERE name ILIKE '%部署%' 
--    OR name ILIKE '%deploy%'
--    OR login_type = 'one-test'
--    OR task_id IS NOT NULL
--    OR platform_name IS NOT NULL;

-- 业务类型参考：
-- 0: 其他账号
-- 1: 店铺账号  
-- 2: 散客账号
-- 3: 测试账号
-- 4: 客服账号
-- 5: 部署账号
-- 6: log账号
-- 7: sql账号
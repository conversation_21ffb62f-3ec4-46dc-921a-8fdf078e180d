const https = require('http');

// 测试数据库连接API
function testDatabaseConnection() {
  console.log('测试数据库连接API...');
  
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/account/test',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  const req = https.request(options, (res) => {
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应头: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('数据库测试响应:');
      console.log(data);
      console.log('\n' + '='.repeat(50) + '\n');
      
      // 测试登录查询API
      testLoginUrlQuery();
    });
  });

  req.on('error', (e) => {
    console.error(`数据库测试请求失败: ${e.message}`);
    console.log('可能服务器未启动，请先运行: npm run dev');
  });

  req.end();
}

// 测试登录链接查询API
function testLoginUrlQuery() {
  console.log('测试登录链接查询API...');
  
  const testUrls = [
    'https://user.lotsmall.cn/login',
    'https://wap.lotsmall.cn/login',
    'https://test-aliuser.lotsmall.cn/usercenter/login'
  ];
  
  testUrls.forEach((testUrl, index) => {
    setTimeout(() => {
      console.log(`\n测试URL ${index + 1}: ${testUrl}`);
      
      const postData = JSON.stringify({
        loginUrl: testUrl
      });
      
      const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/account/findByLoginUrl',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      const req = https.request(options, (res) => {
        console.log(`状态码: ${res.statusCode}`);
        
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          console.log('查询响应:');
          try {
            const response = JSON.parse(data);
            console.log(JSON.stringify(response, null, 2));
          } catch (e) {
            console.log(data);
          }
          console.log('-'.repeat(30));
        });
      });

      req.on('error', (e) => {
        console.error(`查询请求失败: ${e.message}`);
      });

      req.write(postData);
      req.end();
    }, index * 1000);
  });
}

// 开始测试
console.log('开始API测试...\n');
testDatabaseConnection(); 
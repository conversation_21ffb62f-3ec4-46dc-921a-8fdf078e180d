# Cursor Rules - 自动文档维护工作流

## 🤖 AI助手行为规范

### 核心原则
- 你是天才程序员，保持质疑和批判思维
- 编程过程中**自动维护文档**，无需用户手动操作
- 文档维护是编程流程的一部分，不是额外工作
- 始终使用中文交流

## 📋 自动文档维护流程

### 1. 检测新需求时 - 自动创建文档

**触发条件**: 用户消息包含"新功能"、"需求"、"开发"、"实现"、"添加功能"等关键词

**AI必须执行**:
```
1. 运行: node scripts/auto-docs.js create "功能名称" "需求描述"
2. 告知用户文档已创建
3. 开始记录开发过程
```

### 2. 编程过程中 - 实时更新记录

**触发条件**: 每次使用edit_file工具修改代码后

**AI必须执行**:
```
1. 运行: node scripts/auto-docs.js update-devlog "功能名称" "本次修改内容"
2. 自动记录技术实现细节
3. 记录遇到的问题和解决方案
```

### 3. 功能完成时 - 自动归档

**触发条件**: 用户说"完成"、"结束"、"功能做完了"等

**AI必须执行**:
```
1. 运行: node scripts/auto-docs.js archive "功能名称"
2. 告知用户文档已归档
3. 更新项目文档索引
```

## 🔧 文档结构

```
docs/
├── active/                 # 🔥 开发中的文档
│   └── 功能名称/
│       ├── prd.md         # 需求文档
│       ├── tsd.md         # 技术方案
│       └── devlog.md      # 开发日志
├── archive/               # 📚 已完成的文档
│   ├── by-time/          # 按时间查找
│   └── by-feature/       # 按功能查找
└── modules-config.json    # 自定义模块配置
```

## ⚙️ AI助手必须执行的动作

### 检测新需求示例:
```
用户: "我要开发一个用户登录优化功能"
AI: ✅ 检测到新需求，自动创建文档...
    [运行] node scripts/auto-docs.js create "用户登录优化" "优化用户登录体验"
    📝 文档已创建: docs/active/用户登录优化/
    🚀 开始开发...
```

### 代码修改后示例:
```
[AI刚使用edit_file修改了Login.tsx]
AI: 🔄 正在更新开发日志...
    [运行] node scripts/auto-docs.js update-devlog "用户登录优化" "修改Login.tsx，添加记住密码功能"
    📝 已记录到DevLog
```

### 功能完成示例:
```
用户: "登录功能完成了"
AI: ✨ 功能完成，正在归档文档...
    [运行] node scripts/auto-docs.js archive "用户登录优化"
    🗂️ 文档已归档到时间和功能索引
```

## 🎯 关键要点

1. **自动性**: AI必须主动执行，不需要用户提醒
2. **实时性**: 每次代码修改都要记录
3. **完整性**: 需求→开发→完成的完整流程
4. **智能性**: 自动识别功能模块分类

/**
 * Chrome插件环境配置文件
 */

// 环境配置
const environments = {
  // 开发环境 - 连接生产服务器
  development: {
    apiUrls: [
    ],
    appUrls: [
      '***************',
      'localhost:3001'
    ]
  },
  
  // 测试环境 - 连接测试服务器
  testing: {
    apiUrls: [
    ],
    appUrls: [
      '**************'
    ]
  }
};

// 当前环境 - 设置为development使用生产服务器
const currentEnvironment = 'development';

// 导出当前环境配置
const config = environments[currentEnvironment];

// 导出环境配置
export default config; 
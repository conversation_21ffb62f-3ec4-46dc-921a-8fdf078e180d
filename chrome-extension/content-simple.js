// chrome-extension/content-simple.js - 简化版本用于测试
console.log('简化版自动登录内容脚本已加载（v2 - 优化验证码识别流程）。');

// Helper function to find elements
const getElementByXPath = (xpath) => {
  if (!xpath) return null;
  try {
    return document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
  } catch (e) {
    console.error(`无效的 XPath 表达式: "${xpath}"`, e);
    return null;
  }
};

// Main message listener
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.type === 'EXECUTE_LOGIN') {
    console.log('接收到执行登录命令:', request.data);
    
    const performLogin = async () => {
      try {
        const {
          username, password,
          usernameXpath, passwordXpath, captchaXpath, loginButtonXpath,
          captchaInputXpath
        } = request.data;

        // Step 1: Fill Username and Password
        console.log('开始填充账号密码...');
        const usernameField = getElementByXPath(usernameXpath);
        if (usernameField) {
          usernameField.value = username;
          usernameField.dispatchEvent(new Event('input', { bubbles: true }));
          usernameField.dispatchEvent(new Event('change', { bubbles: true }));
          console.log('✅ 用户名已填充');
        } else {
          console.error('❌ 未找到用户名输入框');
        }

        const passwordField = getElementByXPath(passwordXpath);
        if (passwordField) {
          passwordField.value = password;
          passwordField.dispatchEvent(new Event('input', { bubbles: true }));
          passwordField.dispatchEvent(new Event('change', { bubbles: true }));
          console.log('✅ 密码已填充');
        } else {
          console.error('❌ 未找到密码输入框');
        }
        
        // 添加延迟，等待验证码图片加载
        console.log('⏱️ 等待验证码图片加载...');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Step 2: Handle Captcha - 尝试多次查找验证码
        let captchaImg = null;
        let retryCount = 0;
        const MAX_FIND_RETRY = 5;
        
        while (!captchaImg && retryCount < MAX_FIND_RETRY) {
          captchaImg = getElementByXPath(captchaXpath);
          if (!captchaImg || !(captchaImg instanceof HTMLImageElement)) {
            console.log(`🔍 第 ${retryCount + 1} 次尝试查找验证码图片...未找到`);
            retryCount++;
            // 等待一小段时间后再次尝试
            await new Promise(resolve => setTimeout(resolve, 1000));
          } else {
            console.log(`✅ 第 ${retryCount + 1} 次尝试成功找到验证码图片`);
            break;
          }
        }
        
        if (captchaInputXpath) {
          const captchaInput = getElementByXPath(captchaInputXpath);
          if (captchaInput) {
            captchaInput.focus();
            captchaInput.style.border = "3px solid #FF9800";
            captchaInput.style.backgroundColor = "#FFF3E0";
            captchaInput.placeholder = "请手动输入验证码";
            console.log('✅ 已聚焦到验证码输入框，请手动输入');
            
            // 添加提示
            const hint = document.createElement('div');
            hint.style.cssText = `
              position: absolute;
              background: #FF9800;
              color: white;
              padding: 8px 12px;
              border-radius: 4px;
              font-size: 14px;
              z-index: 10000;
              margin-top: 5px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            `;
            
            if (captchaImg) {
              hint.textContent = '已找到验证码图片，请输入验证码';
              hint.style.background = '#4CAF50';
              
              // 显示验证码图片信息
              console.log(`📊 验证码图片信息:`);
              console.log(`  - 地址: ${captchaImg.src}`);
              console.log(`  - 尺寸: ${captchaImg.width}x${captchaImg.height}`);
              console.log(`  - 加载状态: ${captchaImg.complete ? '已加载' : '加载中'}`);
            } else {
              hint.textContent = '未找到验证码图片，请检查后手动输入';
            }
            
            if (captchaInput.parentNode) {
              captchaInput.parentNode.style.position = 'relative';
              captchaInput.parentNode.appendChild(hint);
              
              // 10秒后移除提示
              setTimeout(() => {
                if (hint.parentNode) {
                  hint.parentNode.removeChild(hint);
                }
              }, 10000);
            }
          } else {
            console.error('❌ 未找到验证码输入框');
          }
        }

        // Step 3: 延迟点击登录按钮，给用户时间输入验证码
        if (loginButtonXpath) {
          console.log('⏰ 等待用户输入验证码...');
          setTimeout(() => {
            const loginButton = getElementByXPath(loginButtonXpath);
            if (loginButton) {
              console.log('🔘 准备点击登录按钮');
              // 高亮登录按钮
              loginButton.style.border = "3px solid #4CAF50";
              loginButton.style.boxShadow = "0 0 10px #4CAF50";
              
              // 询问用户是否准备好
              if (confirm('验证码已输入完成？点击确定自动点击登录按钮')) {
                loginButton.click();
                console.log('✅ 已点击登录按钮');
                sendResponse({ success: true, message: '登录流程已完成' });
              } else {
                console.log('⏸️ 用户取消自动点击登录');
                sendResponse({ success: true, message: '已填充账号密码，请手动完成登录' });
              }
            } else {
              console.error('❌ 未找到登录按钮');
              sendResponse({ success: false, message: '未找到登录按钮' });
            }
          }, 2000); // 给用户2秒时间看到验证码输入框
        } else {
          sendResponse({ success: true, message: '账号密码已填充，请手动点击登录' });
        }

      } catch (error) {
        console.error('❌ 自动登录过程发生错误:', error);
        sendResponse({ success: false, message: `执行失败: ${error.message}` });
      }
    };

    performLogin();
    return true; // Keep the message channel open for async response
  }
  return false;
}); 
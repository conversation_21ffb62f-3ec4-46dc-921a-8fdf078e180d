{"extVersion": "1.29.10", "name": "智能数据采集工作流", "icon": "riDatabase2Line", "table": [{"id": "scraped-data", "name": "采集数据", "columns": [{"id": "title", "name": "标题", "type": "text"}, {"id": "url", "name": "链接", "type": "text"}, {"id": "content", "name": "内容", "type": "text"}, {"id": "timestamp", "name": "采集时间", "type": "text"}, {"id": "source", "name": "来源", "type": "text"}]}], "version": "1.0.0", "drawflow": {"nodes": [{"id": "scraper-trigger", "type": "BlockBasic", "initialized": false, "position": {"x": -200, "y": -200}, "data": {"disableBlock": false, "description": "🕷️ 数据采集触发器", "type": "manual", "parameters": [{"data": {"required": true}, "defaultValue": "{\n  \"targetSites\": [\n    {\n      \"name\": \"新闻网站\",\n      \"baseUrl\": \"https://example-news.com\",\n      \"listPageUrl\": \"https://example-news.com/news\",\n      \"selectors\": {\n        \"itemList\": \".news-item\",\n        \"title\": \".news-title\",\n        \"link\": \".news-title a\",\n        \"summary\": \".news-summary\",\n        \"date\": \".news-date\"\n      },\n      \"pagination\": {\n        \"nextButton\": \".pagination .next\",\n        \"maxPages\": 5\n      }\n    }\n  ],\n  \"extractionRules\": {\n    \"contentSelectors\": {\n      \"title\": \"h1, .title, .headline\",\n      \"content\": \".content, .article-body, .post-content\",\n      \"author\": \".author, .byline\",\n      \"publishDate\": \".date, .publish-date, time\"\n    },\n    \"cleanupRules\": {\n      \"removeElements\": [\".ad\", \".sidebar\", \".footer\"],\n      \"trimWhitespace\": true,\n      \"removeEmptyLines\": true\n    }\n  },\n  \"options\": {\n    \"maxItems\": 50,\n    \"delayBetweenRequests\": 2000,\n    \"respectRobotsTxt\": true,\n    \"userAgent\": \"Mozilla/5.0 (compatible; DataCollector/1.0)\",\n    \"timeout\": 30000,\n    \"retryCount\": 3\n  }\n}", "description": "🔧 数据采集配置", "id": "scraperConfig", "name": "scraperConfig", "type": "json"}]}, "label": "trigger"}, {"id": "config-validator", "type": "BlockBasic", "initialized": false, "position": {"x": -200, "y": -50}, "data": {"code": "// === 配置验证器 ===\ntry {\n  const config = automaRefData('variables', 'scraperConfig') || {};\n  \n  console.log('🔍 验证采集配置...');\n  \n  // 验证必需字段\n  if (!config.targetSites || !Array.isArray(config.targetSites) || config.targetSites.length === 0) {\n    throw new Error('缺少目标网站配置');\n  }\n  \n  // 验证每个网站配置\n  config.targetSites.forEach((site, index) => {\n    if (!site.name || !site.baseUrl || !site.listPageUrl) {\n      throw new Error(`网站配置 ${index + 1} 缺少必需字段`);\n    }\n    \n    if (!site.selectors || !site.selectors.itemList) {\n      throw new Error(`网站配置 ${index + 1} 缺少选择器配置`);\n    }\n  });\n  \n  // 设置默认值\n  const defaultOptions = {\n    maxItems: 50,\n    delayBetweenRequests: 2000,\n    respectRobotsTxt: true,\n    timeout: 30000,\n    retryCount: 3\n  };\n  \n  config.options = Object.assign(defaultOptions, config.options || {});\n  \n  // 初始化运行时变量\n  automaSetVariable('validatedConfig', config);\n  automaSetVariable('currentSiteIndex', 0);\n  automaSetVariable('currentPage', 1);\n  automaSetVariable('collectedItems', []);\n  automaSetVariable('totalCollected', 0);\n  automaSetVariable('errors', []);\n  \n  console.log('✅ 配置验证完成');\n  console.log('目标网站数量:', config.targetSites.length);\n  console.log('最大采集数量:', config.options.maxItems);\n  \n} catch (error) {\n  console.error('❌ 配置验证失败:', error);\n  automaSetVariable('configError', error.message);\n} finally {\n  automaNextBlock();\n}", "context": "background", "description": "✅ 配置验证器", "timeout": 5000}, "label": "javascript-code"}, {"id": "site-navigator", "type": "BlockBasic", "initialized": false, "position": {"x": 100, "y": -50}, "data": {"active": true, "description": "🌐 打开目标网站", "url": "{{variables.validatedConfig.targetSites[variables.currentSiteIndex].listPageUrl}}", "waitTabLoaded": true, "timeout": 30000}, "label": "new-tab"}, {"id": "data-extractor", "type": "BlockBasic", "initialized": false, "position": {"x": 400, "y": -50}, "data": {"code": "// === 数据提取器 ===\n(async () => {\n  try {\n    const config = automaRefData('variables', 'validatedConfig') || {};\n    const currentSiteIndex = automaRefData('variables', 'currentSiteIndex') || 0;\n    const currentPage = automaRefData('variables', 'currentPage') || 1;\n    let collectedItems = automaRefData('variables', 'collectedItems') || [];\n    let totalCollected = automaRefData('variables', 'totalCollected') || 0;\n    const errors = automaRefData('variables', 'errors') || [];\n    \n    const currentSite = config.targetSites[currentSiteIndex];\n    console.log(`🕷️ 开始采集: ${currentSite.name} - 第 ${currentPage} 页`);\n    \n    // 等待页面加载\n    await waitForPageReady();\n    \n    // 提取列表数据\n    const items = await extractListItems(currentSite);\n    console.log(`📋 本页发现 ${items.length} 个项目`);\n    \n    // 处理每个项目\n    for (const [index, item] of items.entries()) {\n      try {\n        if (totalCollected >= config.options.maxItems) {\n          console.log('📊 已达到最大采集数量限制');\n          break;\n        }\n        \n        console.log(`📄 处理第 ${index + 1} 个项目: ${item.title}`);\n        \n        // 如果有详情链接，获取详细内容\n        let detailContent = {};\n        if (item.link) {\n          try {\n            detailContent = await extractDetailContent(item.link, config.extractionRules);\n          } catch (detailError) {\n            console.warn(`⚠️ 获取详情失败: ${detailError.message}`);\n          }\n        }\n        \n        // 合并数据\n        const finalItem = {\n          ...item,\n          ...detailContent,\n          source: currentSite.name,\n          sourceUrl: currentSite.baseUrl,\n          timestamp: new Date().toISOString(),\n          page: currentPage\n        };\n        \n        collectedItems.push(finalItem);\n        totalCollected++;\n        \n        console.log(`✅ 项目 ${index + 1} 采集完成`);\n        \n        // 延迟以避免过于频繁的请求\n        await sleep(config.options.delayBetweenRequests || 2000);\n        \n      } catch (itemError) {\n        console.error(`❌ 项目 ${index + 1} 处理失败:`, itemError);\n        errors.push({\n          type: 'item_processing',\n          item: item,\n          error: itemError.message,\n          timestamp: new Date().toISOString()\n        });\n      }\n    }\n    \n    // 更新变量\n    automaSetVariable('collectedItems', collectedItems);\n    automaSetVariable('totalCollected', totalCollected);\n    automaSetVariable('errors', errors);\n    \n    console.log(`📊 当前采集统计: ${totalCollected} 个项目`);\n    \n    // 检查是否需要翻页\n    const hasNextPage = await checkNextPage(currentSite);\n    const shouldContinue = hasNextPage && \n                          totalCollected < config.options.maxItems && \n                          currentPage < (currentSite.pagination?.maxPages || 10);\n    \n    if (shouldContinue) {\n      automaSetVariable('currentPage', currentPage + 1);\n      automaSetVariable('hasNextPage', true);\n      console.log(`📄 准备采集第 ${currentPage + 1} 页`);\n    } else {\n      automaSetVariable('hasNextPage', false);\n      console.log('📄 当前网站采集完成');\n    }\n    \n    // === 核心函数 ===\n    \n    async function extractListItems(site) {\n      const items = [];\n      const selectors = site.selectors;\n      \n      const itemElements = document.querySelectorAll(selectors.itemList);\n      \n      for (const element of itemElements) {\n        try {\n          const item = {};\n          \n          // 提取标题\n          if (selectors.title) {\n            const titleEl = element.querySelector(selectors.title);\n            item.title = titleEl ? titleEl.textContent.trim() : '';\n          }\n          \n          // 提取链接\n          if (selectors.link) {\n            const linkEl = element.querySelector(selectors.link);\n            if (linkEl) {\n              item.link = linkEl.href || linkEl.getAttribute('href');\n              // 处理相对链接\n              if (item.link && !item.link.startsWith('http')) {\n                item.link = new URL(item.link, site.baseUrl).href;\n              }\n            }\n          }\n          \n          // 提取摘要\n          if (selectors.summary) {\n            const summaryEl = element.querySelector(selectors.summary);\n            item.summary = summaryEl ? summaryEl.textContent.trim() : '';\n          }\n          \n          // 提取日期\n          if (selectors.date) {\n            const dateEl = element.querySelector(selectors.date);\n            item.date = dateEl ? dateEl.textContent.trim() : '';\n          }\n          \n          if (item.title) {\n            items.push(item);\n          }\n          \n        } catch (itemError) {\n          console.warn('⚠️ 提取项目数据失败:', itemError);\n        }\n      }\n      \n      return items;\n    }\n    \n    async function extractDetailContent(url, rules) {\n      // 在新标签页中打开详情页\n      const detailTab = window.open(url, '_blank');\n      \n      return new Promise((resolve, reject) => {\n        const timeout = setTimeout(() => {\n          detailTab.close();\n          reject(new Error('详情页加载超时'));\n        }, config.options.timeout || 30000);\n        \n        // 监听详情页加载完成\n        detailTab.addEventListener('load', () => {\n          try {\n            const detailDoc = detailTab.document;\n            const content = {};\n            \n            // 提取内容\n            if (rules.contentSelectors) {\n              for (const [key, selector] of Object.entries(rules.contentSelectors)) {\n                const element = detailDoc.querySelector(selector);\n                if (element) {\n                  content[key] = element.textContent.trim();\n                }\n              }\n            }\n            \n            // 清理内容\n            if (rules.cleanupRules) {\n              content = cleanupContent(content, rules.cleanupRules);\n            }\n            \n            clearTimeout(timeout);\n            detailTab.close();\n            resolve(content);\n            \n          } catch (error) {\n            clearTimeout(timeout);\n            detailTab.close();\n            reject(error);\n          }\n        });\n      });\n    }\n    \n    async function checkNextPage(site) {\n      if (!site.pagination?.nextButton) return false;\n      \n      const nextBtn = document.querySelector(site.pagination.nextButton);\n      if (!nextBtn) return false;\n      \n      // 检查按钮是否可点击\n      const isDisabled = nextBtn.disabled || \n                        nextBtn.classList.contains('disabled') ||\n                        nextBtn.getAttribute('aria-disabled') === 'true';\n      \n      return !isDisabled;\n    }\n    \n    function cleanupContent(content, rules) {\n      const cleaned = { ...content };\n      \n      for (const [key, value] of Object.entries(cleaned)) {\n        if (typeof value === 'string') {\n          let cleanValue = value;\n          \n          if (rules.trimWhitespace) {\n            cleanValue = cleanValue.trim();\n          }\n          \n          if (rules.removeEmptyLines) {\n            cleanValue = cleanValue.replace(/\\n\\s*\\n/g, '\\n');\n          }\n          \n          cleaned[key] = cleanValue;\n        }\n      }\n      \n      return cleaned;\n    }\n    \n    async function waitForPageReady() {\n      const timeout = 15000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        if (document.readyState === 'complete') {\n          await sleep(1000);\n          return;\n        }\n        await sleep(200);\n      }\n      throw new Error('页面加载超时');\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n  } catch (error) {\n    console.error('❌ 数据提取失败:', error);\n    automaSetVariable('extractionError', error.message);\n  } finally {\n    automaNextBlock();\n  }\n})();", "context": "website", "description": "🔍 智能数据提取器", "timeout": 120000}, "label": "javascript-code"}, {"id": "pagination-handler", "type": "BlockConditions", "initialized": false, "position": {"x": 700, "y": -50}, "data": {"description": "判断是否需要翻页", "conditions": [{"id": "has-next-page", "name": "翻页继续", "conditions": [{"id": "check-next-page", "conditions": [{"id": "next-page-check", "items": [{"type": "code", "category": "value", "data": {"code": "return automaRefData('variables', 'hasNextPage') === true;", "context": "background"}}]}]}]}]}, "label": "conditions"}, {"id": "next-page-clicker", "type": "BlockBasic", "initialized": false, "position": {"x": 1000, "y": 50}, "data": {"code": "// === 翻页处理器 ===\ntry {\n  const config = automaRefData('variables', 'validatedConfig') || {};\n  const currentSiteIndex = automaRefData('variables', 'currentSiteIndex') || 0;\n  const currentSite = config.targetSites[currentSiteIndex];\n  \n  console.log('📄 执行翻页操作...');\n  \n  if (currentSite.pagination?.nextButton) {\n    const nextBtn = document.querySelector(currentSite.pagination.nextButton);\n    if (nextBtn && !nextBtn.disabled) {\n      nextBtn.click();\n      console.log('✅ 翻页成功');\n    } else {\n      console.log('⚠️ 翻页按钮不可用');\n    }\n  }\n  \n} catch (error) {\n  console.error('❌ 翻页失败:', error);\n} finally {\n  automaNextBlock();\n}", "context": "website", "description": "📄 翻页处理器", "timeout": 5000}, "label": "javascript-code"}, {"id": "data-saver", "type": "BlockBasic", "initialized": false, "position": {"x": 700, "y": 200}, "data": {"code": "// === 数据保存器 ===\ntry {\n  const collectedItems = automaRefData('variables', 'collectedItems') || [];\n  const totalCollected = automaRefData('variables', 'totalCollected') || 0;\n  const errors = automaRefData('variables', 'errors') || [];\n  \n  console.log('💾 开始保存采集数据...');\n  console.log(`总计采集: ${totalCollected} 个项目`);\n  \n  if (collectedItems.length > 0) {\n    // 保存到表格\n    const tableData = collectedItems.map(item => ({\n      title: item.title || '',\n      url: item.link || '',\n      content: item.content || item.summary || '',\n      timestamp: item.timestamp || '',\n      source: item.source || ''\n    }));\n    \n    // 清空现有数据并添加新数据\n    automaSetTable('scraped-data', tableData);\n    \n    console.log('✅ 数据已保存到表格');\n    \n    // 生成采集报告\n    const report = {\n      timestamp: new Date().toISOString(),\n      totalItems: totalCollected,\n      successItems: collectedItems.length,\n      errorCount: errors.length,\n      sources: [...new Set(collectedItems.map(item => item.source))],\n      errors: errors\n    };\n    \n    automaSetVariable('scrapingReport', report);\n    \n    console.log('📊 采集报告:');\n    console.log(`  - 成功: ${report.successItems} 个`);\n    console.log(`  - 错误: ${report.errorCount} 个`);\n    console.log(`  - 来源: ${report.sources.join(', ')}`);\n    \n    // 显示通知\n    if (typeof automaShowNotification === 'function') {\n      automaShowNotification({\n        title: '数据采集完成',\n        message: `成功采集 ${totalCollected} 个项目`,\n        type: 'success'\n      });\n    }\n  } else {\n    console.log('⚠️ 没有采集到任何数据');\n    \n    if (typeof automaShowNotification === 'function') {\n      automaShowNotification({\n        title: '数据采集完成',\n        message: '没有采集到任何数据',\n        type: 'warning'\n      });\n    }\n  }\n  \n  console.log('✅ 数据采集流程完成');\n  \n} catch (error) {\n  console.error('❌ 数据保存失败:', error);\n} finally {\n  automaNextBlock();\n}", "context": "background", "description": "💾 数据保存器", "timeout": 10000}, "label": "javascript-code"}], "edges": [{"id": "trigger-to-validator", "source": "scraper-trigger", "target": "config-validator", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "validator-to-navigator", "source": "config-validator", "target": "site-navigator", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "navigator-to-extractor", "source": "site-navigator", "target": "data-extractor", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "extractor-to-pagination", "source": "data-extractor", "target": "pagination-handler", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "pagination-to-clicker", "source": "pagination-handler", "target": "next-page-clicker", "sourceHandle": "has-next-page-output", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "clicker-to-extractor", "source": "next-page-clicker", "target": "data-extractor", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "pagination-to-saver", "source": "pagination-handler", "target": "data-saver", "sourceHandle": "fallback-output", "type": "custom", "markerEnd": "arrowclosed"}]}, "settings": {"publicId": "intelligent-data-scraper", "restartTimes": 1, "notification": true, "tabLoadTimeout": 30000, "saveLog": true, "debugMode": false, "execContext": "popup", "onError": "continue-workflow"}, "globalData": "{\n  \"workflowType\": \"data-scraping\",\n  \"version\": \"1.0.0\",\n  \"features\": [\"intelligent-extraction\", \"pagination-support\", \"content-cleanup\", \"error-handling\"]\n}", "description": "🕷️ 智能数据采集工作流：支持多网站数据抓取、自动翻页、内容清理和结构化存储"}
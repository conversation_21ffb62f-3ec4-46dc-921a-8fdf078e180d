{"extVersion": "1.29.10", "name": "账号管理通用工作流", "icon": "riUserSettingsLine", "table": [], "version": "1.0.0", "drawflow": {"nodes": [{"id": "trigger-universal", "type": "BlockBasic", "initialized": false, "position": {"x": -200, "y": -300}, "data": {"disableBlock": false, "description": "🌟 通用账号管理触发器", "type": "manual", "parameters": [{"data": {"required": true}, "defaultValue": "{\n  \"mode\": \"multi_platform\",\n  \"platforms\": [\n    {\n      \"name\": \"测试环境\",\n      \"loginUrl\": \"https://test-aliuser.lotsmall.cn/usercenter/login\",\n      \"targetUrl\": \"https://test-aliwww.lotsmall.cn/manage/customForm\",\n      \"credentials\": {\n        \"username\": \"18766668891\",\n        \"password\": \"123456asd.\"\n      },\n      \"selectors\": {\n        \"username\": \".sdi-input-type-text .sdi-input\",\n        \"password\": \".sdi-input-type-password .sdi-input\",\n        \"captcha\": \".checkcode-warper .sdi-input\",\n        \"captchaImage\": \"img.code-img\",\n        \"loginButton\": \"button.login-btn\"\n      }\n    },\n    {\n      \"name\": \"生产环境\",\n      \"loginUrl\": \"https://user.lotsmall.cn/usercenter/login\",\n      \"targetUrl\": \"https://www.lotsmall.cn/manage/dashboard\",\n      \"credentials\": {\n        \"username\": \"<EMAIL>\",\n        \"password\": \"prod_password\"\n      },\n      \"selectors\": {\n        \"username\": \"#username\",\n        \"password\": \"#password\",\n        \"loginButton\": \".login-submit\"\n      }\n    }\n  ],\n  \"options\": {\n    \"autoSelectPlatform\": true,\n    \"maxRetries\": 3,\n    \"enableOCR\": true,\n    \"ocrConfig\": {\n      \"url\": \"https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize\",\n      \"headers\": {\"Cookie\": \"tr_de_id=ZHYtSEhOUlZRWkVXSFZI\"}\n    }\n  }\n}", "description": "🔧 多平台账号配置", "id": "platformConfig", "name": "platformConfig", "type": "json"}]}, "label": "trigger"}, {"id": "platform-detector", "type": "BlockBasic", "initialized": false, "position": {"x": -200, "y": -150}, "data": {"code": "// === 平台检测器 ===\ntry {\n  const config = automaRefData('variables', 'platformConfig') || {};\n  const currentUrl = window.location.href;\n  \n  console.log('🔍 检测当前平台...');\n  console.log('当前URL:', currentUrl);\n  \n  let selectedPlatform = null;\n  \n  // 自动检测平台\n  if (config.options?.autoSelectPlatform) {\n    for (const platform of config.platforms || []) {\n      const platformDomain = new URL(platform.loginUrl).hostname;\n      if (currentUrl.includes(platformDomain)) {\n        selectedPlatform = platform;\n        console.log(`✅ 自动检测到平台: ${platform.name}`);\n        break;\n      }\n    }\n  }\n  \n  // 如果没有自动检测到，使用第一个平台\n  if (!selectedPlatform && config.platforms?.length > 0) {\n    selectedPlatform = config.platforms[0];\n    console.log(`📋 使用默认平台: ${selectedPlatform.name}`);\n  }\n  \n  if (!selectedPlatform) {\n    throw new Error('未找到可用的平台配置');\n  }\n  \n  // 设置运行时配置\n  automaSetVariable('currentPlatform', selectedPlatform);\n  automaSetVariable('globalOptions', config.options || {});\n  \n  console.log('🎯 平台检测完成:', selectedPlatform.name);\n  \n} catch (error) {\n  console.error('❌ 平台检测失败:', error);\n  automaSetVariable('platformError', error.message);\n} finally {\n  automaNextBlock();\n}", "context": "background", "description": "🎯 智能平台检测", "timeout": 5000}, "label": "javascript-code"}, {"id": "session-checker", "type": "BlockBasic", "initialized": false, "position": {"x": 100, "y": -150}, "data": {"active": true, "description": "🌐 打开目标页面检查登录状态", "url": "{{variables.currentPlatform.targetUrl}}", "waitTabLoaded": true, "timeout": 15000}, "label": "new-tab"}, {"id": "login-status-check", "type": "BlockBasic", "initialized": false, "position": {"x": 400, "y": -150}, "data": {"code": "// === 登录状态检查 ===\ntry {\n  const platform = automaRefData('variables', 'currentPlatform') || {};\n  const currentUrl = window.location.href;\n  \n  console.log('🔍 检查登录状态...');\n  console.log('当前URL:', currentUrl);\n  console.log('目标URL:', platform.targetUrl);\n  \n  let isLoggedIn = false;\n  \n  // 检查URL是否包含登录页面特征\n  const loginIndicators = ['/login', '/signin', '/auth', '/usercenter/login'];\n  const hasLoginIndicator = loginIndicators.some(indicator => currentUrl.includes(indicator));\n  \n  if (hasLoginIndicator) {\n    console.log('🔓 检测到登录页面，需要登录');\n    isLoggedIn = false;\n  } else {\n    // 检查页面内容\n    const pageText = document.body.textContent || '';\n    const loggedInIndicators = ['我的应用', '工作台', '控制台', '管理后台', '欢迎', 'dashboard'];\n    const hasLoggedInIndicator = loggedInIndicators.some(indicator => \n      pageText.toLowerCase().includes(indicator.toLowerCase())\n    );\n    \n    if (hasLoggedInIndicator) {\n      console.log('✅ 检测到已登录状态');\n      isLoggedIn = true;\n    } else {\n      console.log('❓ 登录状态不明确，默认执行登录');\n      isLoggedIn = false;\n    }\n  }\n  \n  automaSetVariable('isLoggedIn', isLoggedIn);\n  automaSetVariable('needLogin', !isLoggedIn);\n  \n  console.log('🎯 登录状态检查完成:', isLoggedIn ? '已登录' : '需要登录');\n  \n} catch (error) {\n  console.error('❌ 登录状态检查失败:', error);\n  automaSetVariable('needLogin', true); // 出错时默认需要登录\n} finally {\n  automaNextBlock();\n}", "context": "website", "description": "🔍 智能登录状态检查", "timeout": 10000}, "label": "javascript-code"}, {"id": "login-condition", "type": "BlockConditions", "initialized": false, "position": {"x": 700, "y": -150}, "data": {"description": "判断是否需要登录", "conditions": [{"id": "need-login-path", "name": "需要登录", "conditions": [{"id": "check-need-login", "conditions": [{"id": "need-login-check", "items": [{"type": "code", "category": "value", "data": {"code": "return automaRefData('variables', 'needLogin') === true;", "context": "background"}}]}]}]}]}, "label": "conditions"}, {"id": "login-page-opener", "type": "BlockBasic", "initialized": false, "position": {"x": 1000, "y": -50}, "data": {"active": true, "description": "🌐 打开登录页面", "url": "{{variables.currentPlatform.loginUrl}}", "waitTabLoaded": true, "timeout": 15000}, "label": "new-tab"}, {"id": "universal-login-engine", "type": "BlockBasic", "initialized": false, "position": {"x": 1000, "y": 100}, "data": {"code": "// === 通用登录引擎 ===\n(async () => {\n  try {\n    const platform = automaRefData('variables', 'currentPlatform') || {};\n    const globalOptions = automaRefData('variables', 'globalOptions') || {};\n    \n    console.log(`🚀 开始登录: ${platform.name}`);\n    \n    // 等待页面加载\n    await waitForPageReady();\n    \n    // 填充登录表单\n    await fillLoginForm(platform);\n    \n    // 处理验证码（如果需要）\n    if (platform.selectors?.captchaImage && globalOptions.enableOCR) {\n      await handleCaptcha(platform, globalOptions);\n    }\n    \n    // 点击登录按钮\n    await clickLoginButton(platform);\n    \n    // 等待登录结果\n    const loginResult = await waitForLoginResult(platform);\n    \n    if (loginResult.success) {\n      console.log('🎉 登录成功！');\n      automaSetVariable('loginSuccess', true);\n      \n      // 如果当前不在目标页面，跳转到目标页面\n      if (!window.location.href.includes(new URL(platform.targetUrl).pathname)) {\n        console.log('🔗 跳转到目标页面...');\n        window.location.href = platform.targetUrl;\n      }\n    } else {\n      throw new Error(loginResult.error || '登录失败');\n    }\n    \n    // === 核心函数 ===\n    \n    async function waitForPageReady() {\n      const timeout = 10000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        if (document.readyState === 'complete') {\n          await sleep(1000);\n          return;\n        }\n        await sleep(200);\n      }\n      throw new Error('页面加载超时');\n    }\n    \n    async function fillLoginForm(platform) {\n      const { credentials, selectors } = platform;\n      \n      // 填充用户名\n      if (selectors.username && credentials.username) {\n        const usernameEl = await findElement(selectors.username);\n        if (usernameEl) {\n          await fillInput(usernameEl, credentials.username);\n          console.log('✅ 用户名已填充');\n        }\n      }\n      \n      // 填充密码\n      if (selectors.password && credentials.password) {\n        const passwordEl = await findElement(selectors.password);\n        if (passwordEl) {\n          await fillInput(passwordEl, credentials.password);\n          console.log('✅ 密码已填充');\n        }\n      }\n    }\n    \n    async function handleCaptcha(platform, options) {\n      const captchaImg = await findElement(platform.selectors.captchaImage);\n      if (!captchaImg) return;\n      \n      console.log('🔍 开始识别验证码...');\n      \n      // 获取验证码图片\n      const response = await fetch(captchaImg.src);\n      const blob = await response.blob();\n      const base64 = await blobToBase64(blob);\n      \n      // 调用OCR识别\n      const ocrResult = await automaFetch('json', {\n        url: options.ocrConfig.url,\n        method: 'POST',\n        headers: Object.assign({\n          'Content-Type': 'application/json'\n        }, options.ocrConfig.headers || {}),\n        body: JSON.stringify({ imageData: base64 })\n      });\n      \n      if (ocrResult?.success && ocrResult.data?.text) {\n        const captchaText = ocrResult.data.text.trim();\n        console.log('✅ 验证码识别成功:', captchaText);\n        \n        // 填充验证码\n        const captchaInput = await findElement(platform.selectors.captcha);\n        if (captchaInput) {\n          await fillInput(captchaInput, captchaText);\n          console.log('✅ 验证码已填充');\n        }\n      } else {\n        console.warn('⚠️ 验证码识别失败');\n      }\n    }\n    \n    async function clickLoginButton(platform) {\n      const loginBtn = await findElement(platform.selectors.loginButton);\n      if (!loginBtn) throw new Error('登录按钮未找到');\n      \n      loginBtn.scrollIntoView({ block: 'center' });\n      await sleep(500);\n      loginBtn.click();\n      console.log('✅ 登录按钮已点击');\n    }\n    \n    async function waitForLoginResult(platform) {\n      const timeout = 15000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        const currentUrl = window.location.href;\n        \n        // 检查是否跳转到目标页面\n        if (currentUrl.includes(new URL(platform.targetUrl).pathname) ||\n            currentUrl.includes('/dashboard') ||\n            currentUrl.includes('/worktable') ||\n            currentUrl.includes('/manage')) {\n          return { success: true };\n        }\n        \n        // 检查错误信息\n        const errorEl = document.querySelector('.error-message, .ant-message-error, [class*=\"error\"]');\n        if (errorEl && errorEl.textContent.trim()) {\n          return { success: false, error: errorEl.textContent.trim() };\n        }\n        \n        await sleep(1000);\n      }\n      \n      return { success: false, error: '登录超时' };\n    }\n    \n    // === 工具函数 ===\n    \n    async function findElement(selector) {\n      const maxAttempts = 10;\n      for (let i = 0; i < maxAttempts; i++) {\n        const element = document.querySelector(selector);\n        if (element) return element;\n        await sleep(500);\n      }\n      return null;\n    }\n    \n    async function fillInput(element, value) {\n      element.focus();\n      element.click();\n      await sleep(100);\n      element.value = '';\n      element.value = value;\n      \n      // 触发事件\n      ['input', 'change', 'blur'].forEach(eventType => {\n        element.dispatchEvent(new Event(eventType, { bubbles: true }));\n      });\n      \n      await sleep(200);\n    }\n    \n    function blobToBase64(blob) {\n      return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsDataURL(blob);\n      });\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n  } catch (error) {\n    console.error('❌ 登录失败:', error);\n    automaSetVariable('loginSuccess', false);\n    automaSetVariable('loginError', error.message);\n  } finally {\n    automaNextBlock();\n  }\n})();", "context": "website", "description": "🧠 通用登录引擎", "timeout": 30000}, "label": "javascript-code"}, {"id": "success-handler", "type": "BlockBasic", "initialized": false, "position": {"x": 1000, "y": 300}, "data": {"code": "// === 成功处理器 ===\ntry {\n  const platform = automaRefData('variables', 'currentPlatform') || {};\n  const loginSuccess = automaRefData('variables', 'loginSuccess');\n  \n  if (loginSuccess) {\n    console.log(`🎉 ${platform.name} 登录成功！`);\n    \n    // 显示成功通知\n    if (typeof automaShowNotification === 'function') {\n      automaShowNotification({\n        title: '登录成功',\n        message: `已成功登录到 ${platform.name}`,\n        type: 'success'\n      });\n    }\n    \n    // 记录登录历史\n    const loginHistory = automaRefData('variables', 'loginHistory') || [];\n    loginHistory.push({\n      platform: platform.name,\n      timestamp: new Date().toISOString(),\n      url: window.location.href\n    });\n    automaSetVariable('loginHistory', loginHistory);\n    \n    console.log('✅ 登录流程完成');\n  } else {\n    const error = automaRefData('variables', 'loginError') || '未知错误';\n    console.error(`❌ ${platform.name} 登录失败: ${error}`);\n    \n    // 显示错误通知\n    if (typeof automaShowNotification === 'function') {\n      automaShowNotification({\n        title: '登录失败',\n        message: `${platform.name}: ${error}`,\n        type: 'error'\n      });\n    }\n  }\n  \n} catch (error) {\n  console.error('❌ 处理登录结果时出错:', error);\n} finally {\n  automaNextBlock();\n}", "context": "website", "description": "🎯 成功处理器", "timeout": 5000}, "label": "javascript-code"}, {"id": "already-logged-in", "type": "BlockBasic", "initialized": false, "position": {"x": 700, "y": 300}, "data": {"code": "// === 已登录处理器 ===\ntry {\n  const platform = automaRefData('variables', 'currentPlatform') || {};\n  \n  console.log(`✅ ${platform.name} 已处于登录状态`);\n  \n  // 显示通知\n  if (typeof automaShowNotification === 'function') {\n    automaShowNotification({\n      title: '已登录',\n      message: `${platform.name} 已处于登录状态`,\n      type: 'info'\n    });\n  }\n  \n  console.log('🎯 无需重复登录，流程结束');\n  \n} catch (error) {\n  console.error('❌ 处理已登录状态时出错:', error);\n} finally {\n  automaNextBlock();\n}", "context": "website", "description": "✅ 已登录处理器", "timeout": 3000}, "label": "javascript-code"}], "edges": [{"id": "trigger-to-detector", "source": "trigger-universal", "target": "platform-detector", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "detector-to-checker", "source": "platform-detector", "target": "session-checker", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "checker-to-status", "source": "session-checker", "target": "login-status-check", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "status-to-condition", "source": "login-status-check", "target": "login-condition", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "condition-to-login", "source": "login-condition", "target": "login-page-opener", "sourceHandle": "need-login-path-output", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "condition-to-already", "source": "login-condition", "target": "already-logged-in", "sourceHandle": "fallback-output", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "opener-to-engine", "source": "login-page-opener", "target": "universal-login-engine", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "engine-to-success", "source": "universal-login-engine", "target": "success-handler", "type": "custom", "markerEnd": "arrowclosed"}]}, "settings": {"publicId": "universal-account-manager", "restartTimes": 2, "notification": true, "tabLoadTimeout": 30000, "saveLog": true, "debugMode": false, "execContext": "popup", "onError": "stop-workflow"}, "globalData": "{\n  \"workflowType\": \"universal-account-manager\",\n  \"version\": \"1.0.0\",\n  \"features\": [\"multi-platform\", \"auto-detection\", \"ocr-captcha\", \"session-check\"]\n}", "description": "🌟 通用账号管理工作流：支持多平台自动登录、智能会话检测、OCR验证码识别"}
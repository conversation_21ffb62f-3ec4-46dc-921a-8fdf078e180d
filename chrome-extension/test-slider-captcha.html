<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滑块验证码测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 4px;
            width: 100%;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        .slider-container {
            position: relative;
            margin: 20px 0;
            background-color: #f1f1f1;
            height: 40px;
            line-height: 40px;
            border-radius: 4px;
            text-align: center;
            color: #888;
        }
        
        .slider-bg {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            border-radius: 4px;
            background-color: #e8f5e9;
            width: 0;
            transition: width 0.2s;
        }
        
        .slider-button {
            position: absolute;
            width: 40px;
            height: 40px;
            background-color: #4CAF50;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            z-index: 2;
            transition: background-color 0.3s;
        }
        
        .slider-button:hover {
            background-color: #45a049;
        }
        
        .slider-text {
            position: relative;
            z-index: 1;
        }
        
        .captcha-image {
            position: relative;
            height: 150px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .captcha-bg {
            width: 100%;
            height: 100%;
            background-image: url('https://picsum.photos/400/150');
            background-size: cover;
        }
        
        .captcha-puzzle {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.8);
            border: 2px solid #4CAF50;
            border-radius: 4px;
        }
        
        .captcha-target {
            position: absolute;
            top: 20px;
            left: 200px;
            width: 44px;
            height: 44px;
            border: 2px dashed #4CAF50;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .success-tip, .fail-tip {
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            text-align: center;
            display: none;
        }
        
        .success-tip {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        
        .fail-tip {
            background-color: #ffebee;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试登录页面</h1>
        
        <div class="form-group">
            <label for="username">用户名</label>
            <input type="text" id="username" name="username" placeholder="请输入用户名">
        </div>
        
        <div class="form-group">
            <label for="password">密码</label>
            <input type="password" id="password" name="password" placeholder="请输入密码">
        </div>
        
        <div class="form-group">
            <label>请完成滑块验证</label>
            
            <div class="captcha-image">
                <div class="captcha-bg"></div>
                <div class="captcha-target"></div>
                <div class="captcha-puzzle" id="puzzle"></div>
            </div>
            
            <div class="slider-container">
                <div class="slider-bg" id="slider-bg"></div>
                <div class="slider-button" id="slider">→</div>
                <div class="slider-text">向右滑动填充拼图</div>
            </div>
            
            <div class="success-tip" id="success-tip">验证成功！</div>
            <div class="fail-tip" id="fail-tip">验证失败，请重试！</div>
        </div>
        
        <button id="login-btn" disabled>登录</button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const slider = document.getElementById('slider');
            const sliderBg = document.getElementById('slider-bg');
            const puzzle = document.getElementById('puzzle');
            const successTip = document.getElementById('success-tip');
            const failTip = document.getElementById('fail-tip');
            const loginBtn = document.getElementById('login-btn');
            
            let isDragging = false;
            let startX = 0;
            let puzzleStartX = 20;
            const targetX = 200;
            const tolerance = 10; // 允许的误差范围
            const containerWidth = document.querySelector('.slider-container').offsetWidth - slider.offsetWidth;
            
            // 初始化拼图位置
            puzzle.style.left = puzzleStartX + 'px';
            
            // 鼠标按下事件
            slider.addEventListener('mousedown', function(e) {
                isDragging = true;
                startX = e.clientX - slider.offsetLeft;
                slider.style.transition = 'none';
                sliderBg.style.transition = 'none';
                puzzle.style.transition = 'none';
            });
            
            // 鼠标移动事件
            document.addEventListener('mousemove', function(e) {
                if (!isDragging) return;
                
                let newX = e.clientX - startX;
                
                // 限制滑块在容器内
                if (newX < 0) newX = 0;
                if (newX > containerWidth) newX = containerWidth;
                
                // 移动滑块和背景
                slider.style.left = newX + 'px';
                sliderBg.style.width = newX + slider.offsetWidth / 2 + 'px';
                
                // 移动拼图
                const puzzleX = puzzleStartX + (newX / containerWidth) * (targetX - puzzleStartX);
                puzzle.style.left = puzzleX + 'px';
            });
            
            // 鼠标释放事件
            document.addEventListener('mouseup', function() {
                if (!isDragging) return;
                isDragging = false;
                
                // 获取当前拼图位置
                const puzzleX = parseInt(puzzle.style.left);
                
                // 检查是否匹配目标位置
                if (Math.abs(puzzleX - targetX) <= tolerance) {
                    // 验证成功
                    successTip.style.display = 'block';
                    failTip.style.display = 'none';
                    slider.style.backgroundColor = '#4CAF50';
                    slider.innerHTML = '✓';
                    loginBtn.disabled = false;
                    
                    // 固定拼图到正确位置
                    puzzle.style.transition = 'left 0.2s';
                    puzzle.style.left = targetX + 'px';
                } else {
                    // 验证失败
                    failTip.style.display = 'block';
                    successTip.style.display = 'none';
                    
                    // 重置滑块和拼图
                    slider.style.transition = 'left 0.2s';
                    sliderBg.style.transition = 'width 0.2s';
                    puzzle.style.transition = 'left 0.2s';
                    
                    slider.style.left = '0';
                    sliderBg.style.width = '0';
                    puzzle.style.left = puzzleStartX + 'px';
                    
                    // 2秒后隐藏失败提示
                    setTimeout(function() {
                        failTip.style.display = 'none';
                    }, 2000);
                }
            });
            
            // 登录按钮点击事件
            loginBtn.addEventListener('click', function() {
                alert('登录成功！');
            });
        });
    </script>
</body>
</html> 
// 验证码调试工具
function debugCaptcha() {
  console.log('🔍 开始验证码调试...');
  
  // 查找页面上的所有图片
  const images = document.querySelectorAll('img');
  console.log(`找到 ${images.length} 个图片元素`);
  
  images.forEach((img, index) => {
    console.log(`图片 ${index + 1}:`);
    console.log(`  - src: ${img.src}`);
    console.log(`  - alt: ${img.alt}`);
    console.log(`  - 尺寸: ${img.width}x${img.height}`);
    console.log(`  - 自然尺寸: ${img.naturalWidth}x${img.naturalHeight}`);
    
    // 检查是否可能是验证码图片
    const isPossibleCaptcha = 
      img.src.includes('captcha') || 
      img.src.includes('verify') || 
      img.alt.includes('验证码') ||
      (img.width > 50 && img.width < 200 && img.height > 20 && img.height < 100);
    
    if (isPossibleCaptcha) {
      console.log(`  ⭐ 可能是验证码图片`);
      
      // 高亮显示
      img.style.border = '3px solid red';
      img.style.boxShadow = '0 0 10px red';
      
      // 尝试分析图片
      analyzeCaptchaImage(img, index);
    }
  });
  
  // 查找验证码输入框
  const inputs = document.querySelectorAll('input[type="text"], input[type="password"], input');
  console.log(`\n找到 ${inputs.length} 个输入框`);
  
  inputs.forEach((input, index) => {
    const isPossibleCaptchaInput = 
      input.placeholder.includes('验证码') ||
      input.placeholder.includes('captcha') ||
      input.name.includes('captcha') ||
      input.id.includes('captcha');
    
    if (isPossibleCaptchaInput) {
      console.log(`输入框 ${index + 1} (可能是验证码输入框):`);
      console.log(`  - placeholder: ${input.placeholder}`);
      console.log(`  - name: ${input.name}`);
      console.log(`  - id: ${input.id}`);
      
      // 高亮显示
      input.style.border = '3px solid blue';
      input.style.backgroundColor = '#E3F2FD';
    }
  });
}

// 新增：在填充用户名后检测验证码图片
function detectCaptchaAfterUsername(usernameXpath, captchaXpath) {
  console.log('🔍 开始监测用户名输入后的验证码图片...');
  
  // 找到用户名输入框
  const usernameField = document.evaluate(
    usernameXpath, 
    document, 
    null, 
    XPathResult.FIRST_ORDERED_NODE_TYPE, 
    null
  ).singleNodeValue;
  
  if (!usernameField) {
    console.error('❌ 未找到用户名输入框，请检查XPath');
    return;
  }
  
  console.log('✅ 找到用户名输入框，准备监听输入事件');
  
  // 创建一个状态面板
  const statusPanel = document.createElement('div');
  statusPanel.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    width: 300px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    z-index: 10000;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  `;
  statusPanel.innerHTML = `
    <h3 style="margin: 0 0 10px 0; color: #4CAF50;">验证码监测面板</h3>
    <div id="status-log" style="max-height: 200px; overflow-y: auto; font-size: 12px;"></div>
    <div style="margin-top: 10px;">
      <button id="check-captcha-btn" style="background: #4CAF50; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">检查验证码</button>
      <button id="close-panel-btn" style="background: #F44336; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin-left: 5px; cursor: pointer;">关闭</button>
    </div>
  `;
  document.body.appendChild(statusPanel);
  
  const logStatus = (message, isError = false) => {
    const statusLog = document.getElementById('status-log');
    if (statusLog) {
      const logEntry = document.createElement('div');
      logEntry.style.color = isError ? '#FF5252' : '#FFFFFF';
      logEntry.style.marginBottom = '5px';
      logEntry.style.borderLeft = isError ? '3px solid #FF5252' : '3px solid #4CAF50';
      logEntry.style.paddingLeft = '5px';
      logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      statusLog.appendChild(logEntry);
      statusLog.scrollTop = statusLog.scrollHeight;
    }
  };
  
  // 设置关闭按钮事件
  document.getElementById('close-panel-btn').addEventListener('click', () => {
    statusPanel.remove();
  });
  
  // 设置检查验证码按钮事件
  document.getElementById('check-captcha-btn').addEventListener('click', () => {
    checkForCaptcha();
  });
  
  // 监听用户名输入事件
  usernameField.addEventListener('input', () => {
    logStatus(`用户名输入框值变化: "${usernameField.value}"`);
    
    // 延迟一会儿检查验证码，给页面时间响应
    setTimeout(checkForCaptcha, 500);
  });
  
  // 检查验证码图片的函数
  function checkForCaptcha() {
    logStatus('开始检查验证码图片...');
    
    // 尝试查找验证码图片
    const captchaImg = document.evaluate(
      captchaXpath, 
      document, 
      null, 
      XPathResult.FIRST_ORDERED_NODE_TYPE, 
      null
    ).singleNodeValue;
    
    if (captchaImg && captchaImg instanceof HTMLImageElement) {
      logStatus(`✅ 找到验证码图片! src: ${captchaImg.src.substring(0, 50)}...`);
      
      // 高亮显示验证码图片
      captchaImg.style.border = '3px solid #4CAF50';
      captchaImg.style.boxShadow = '0 0 10px #4CAF50';
      
      // 显示验证码图片信息
      logStatus(`尺寸: ${captchaImg.width}x${captchaImg.height}`);
      logStatus(`加载状态: ${captchaImg.complete ? '已加载' : '加载中'}`);
      
      // 将验证码图片复制到状态面板中
      const captchaPreview = document.createElement('div');
      captchaPreview.style.marginTop = '10px';
      captchaPreview.style.padding = '5px';
      captchaPreview.style.background = 'white';
      captchaPreview.style.borderRadius = '3px';
      captchaPreview.innerHTML = `
        <div style="color: black; margin-bottom: 5px;">验证码图片预览:</div>
        <img src="${captchaImg.src}" style="max-width: 100%; border: 1px solid #ccc;">
        <div style="color: black; font-size: 10px; margin-top: 5px;">XPath: ${captchaXpath}</div>
      `;
      
      const statusLog = document.getElementById('status-log');
      statusLog.appendChild(captchaPreview);
      statusLog.scrollTop = statusLog.scrollHeight;
      
      return true;
    } else {
      logStatus('❌ 未找到验证码图片，请检查XPath或确认用户名输入是否触发了验证码显示', true);
      return false;
    }
  }
  
  // 初始检查
  setTimeout(checkForCaptcha, 1000);
  
  logStatus('监测已启动，请在输入框中输入用户名');
}

function analyzeCaptchaImage(img, index) {
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = img.naturalWidth || img.width;
    canvas.height = img.naturalHeight || img.height;
    
    // 等待图片加载
    if (img.complete) {
      drawAndAnalyze();
    } else {
      img.onload = drawAndAnalyze;
    }
    
    function drawAndAnalyze() {
      ctx.drawImage(img, 0, 0);
      
      // 获取图像数据
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;
      
      // 分析颜色分布
      let totalPixels = data.length / 4;
      let darkPixels = 0;
      let colorCounts = {};
      
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const brightness = (r + g + b) / 3;
        
        if (brightness < 128) darkPixels++;
        
        const color = `rgb(${r},${g},${b})`;
        colorCounts[color] = (colorCounts[color] || 0) + 1;
      }
      
      console.log(`  📊 图片 ${index + 1} 分析结果:`);
      console.log(`    - 总像素: ${totalPixels}`);
      console.log(`    - 暗像素: ${darkPixels} (${(darkPixels/totalPixels*100).toFixed(1)}%)`);
      console.log(`    - 主要颜色数量: ${Object.keys(colorCounts).length}`);
      
      // 显示处理后的图片
      const dataURL = canvas.toDataURL();
      console.log(`    - 数据URL长度: ${dataURL.length}`);
      
      // 创建一个小窗口显示处理后的图片
      const debugDiv = document.createElement('div');
      debugDiv.style.cssText = `
        position: fixed;
        top: ${20 + index * 120}px;
        right: 20px;
        width: 200px;
        background: white;
        border: 2px solid #333;
        padding: 10px;
        z-index: 10000;
        font-size: 12px;
      `;
      
      debugDiv.innerHTML = `
        <div>验证码图片 ${index + 1}</div>
        <img src="${dataURL}" style="max-width: 100%; border: 1px solid #ccc;">
        <div>尺寸: ${canvas.width}x${canvas.height}</div>
        <div>暗像素: ${(darkPixels/totalPixels*100).toFixed(1)}%</div>
        <button onclick="this.parentNode.remove()">关闭</button>
      `;
      
      document.body.appendChild(debugDiv);
      
      // 5秒后自动移除
      setTimeout(() => {
        if (debugDiv.parentNode) {
          debugDiv.parentNode.removeChild(debugDiv);
        }
      }, 10000);
    }
  } catch (e) {
    console.error(`分析图片 ${index + 1} 时出错:`, e);
  }
}

// 在控制台中运行调试
console.log('💡 验证码调试工具已加载');
console.log('💡 在控制台中输入 debugCaptcha() 来开始调试');
console.log('💡 或者输入 detectCaptchaAfterUsername("用户名XPath", "验证码XPath") 来监测用户名输入后的验证码');

// 自动运行一次
setTimeout(debugCaptcha, 1000); 
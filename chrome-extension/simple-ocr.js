// 简单的验证码识别函数
function simpleOCR(canvas) {
  const ctx = canvas.getContext('2d');
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;
  
  // 转换为灰度
  const grayData = [];
  for (let i = 0; i < data.length; i += 4) {
    const gray = Math.round(data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114);
    grayData.push(gray);
  }
  
  // 二值化处理
  const threshold = 128;
  const binaryData = grayData.map(gray => gray > threshold ? 255 : 0);
  
  // 简单的字符分割和识别
  const width = canvas.width;
  const height = canvas.height;
  
  // 查找字符边界
  const chars = [];
  let inChar = false;
  let charStart = 0;
  
  for (let x = 0; x < width; x++) {
    let hasPixel = false;
    for (let y = 0; y < height; y++) {
      if (binaryData[y * width + x] === 0) { // 黑色像素
        hasPixel = true;
        break;
      }
    }
    
    if (hasPixel && !inChar) {
      inChar = true;
      charStart = x;
    } else if (!hasPixel && inChar) {
      inChar = false;
      chars.push({ start: charStart, end: x });
    }
  }
  
  // 如果最后一个字符到边界
  if (inChar) {
    chars.push({ start: charStart, end: width });
  }
  
  console.log('检测到字符数量:', chars.length);
  
  // 如果检测到的字符数量不合理（太多或太少），可能是识别错误
  if (chars.length < 3 || chars.length > 8) {
    console.log('检测到的字符数量不合理，可能识别有误');
    return '';
  }
  
  // 简单的数字识别模式
  const patterns = {
    '0': [1,1,1,1,0,1,1,0,1,1,0,1,1,1,1],
    '1': [0,1,0,1,1,0,0,1,0,0,1,0,1,1,1],
    '2': [1,1,1,0,0,1,1,1,1,1,0,0,1,1,1],
    '3': [1,1,1,0,0,1,1,1,1,0,0,1,1,1,1],
    '4': [1,0,1,1,0,1,1,1,1,0,0,1,0,0,1],
    '5': [1,1,1,1,0,0,1,1,1,0,0,1,1,1,1],
    '6': [1,1,1,1,0,0,1,1,1,1,0,1,1,1,1],
    '7': [1,1,1,0,0,1,0,0,1,0,0,1,0,0,1],
    '8': [1,1,1,1,0,1,1,1,1,1,0,1,1,1,1],
    '9': [1,1,1,1,0,1,1,1,1,0,0,1,1,1,1]
  };
  
  let result = '';
  const recognizedChars = [];
  
  // 对每个字符进行识别
  chars.forEach((char, index) => {
    if (char.end - char.start > 3) { // 忽略太窄的区域
      // 提取字符区域
      const charWidth = char.end - char.start;
      const charCanvas = document.createElement('canvas');
      const charCtx = charCanvas.getContext('2d');
      charCanvas.width = charWidth;
      charCanvas.height = height;
      
      // 将字符区域绘制到新画布
      charCtx.drawImage(
        canvas, 
        char.start, 0, charWidth, height, 
        0, 0, charWidth, height
      );
      
      // 获取字符区域的像素数据
      const charImageData = charCtx.getImageData(0, 0, charWidth, height);
      const charData = charImageData.data;
      
      // 尝试识别字符
      const recognizedChar = recognizeDigit(charImageData, charWidth, height);
      if (recognizedChar) {
        recognizedChars.push({
          char: recognizedChar,
          width: charWidth,
          height: height,
          aspectRatio: charWidth / height
        });
        result += recognizedChar;
      }
    }
  });
  
  // 过滤非数字字符
  result = result.replace(/[^0-9]/g, '');
  
  // 检查是否为重复字符（如"11111"）
  const isRepeatingChars = /^(.)\1{3,}$/.test(result);
  if (isRepeatingChars) {
    console.log('警告：识别结果为重复字符，可能不准确:', result);
    
    // 检查字符宽度是否有明显差异，如果有，说明可能不是相同字符
    if (recognizedChars.length >= 4) {
      const widthVariance = calculateVariance(recognizedChars.map(c => c.aspectRatio));
      console.log('字符宽高比方差:', widthVariance);
      
      // 如果字符宽度变化大，说明不太可能都是相同字符
      if (widthVariance > 0.05) {
        console.log('字符宽度变化较大，尝试重新识别');
        
        // 尝试使用更严格的识别策略重新识别
        result = '';
        recognizedChars.forEach(charInfo => {
          // 根据宽高比重新判断，1通常很窄，0和8通常较宽
          const aspectRatio = charInfo.width / charInfo.height;
          if (aspectRatio < 0.4) {
            result += '1';
          } else if (aspectRatio > 0.7) {
            result += '0';
          } else {
            // 使用原来的识别结果，但避免全是相同字符
            if (result.length > 0 && result[result.length - 1] === charInfo.char) {
              // 如果和前一个字符相同，随机选择一个不同的数字
              const options = '0123456789'.replace(charInfo.char, '');
              result += options.charAt(Math.floor(Math.random() * options.length));
            } else {
              result += charInfo.char;
            }
          }
        });
        
        console.log('重新识别结果:', result);
      }
    }
  }
  
  // 如果没有识别出任何数字，返回空字符串
  if (result.length === 0) {
    console.log('未能识别出任何数字');
    return '';
  }
  
  console.log('识别结果(本地识别):', result);
  return result;
}

/**
 * 计算数组方差
 */
function calculateVariance(array) {
  const mean = array.reduce((sum, val) => sum + val, 0) / array.length;
  return array.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / array.length;
}

/**
 * 识别单个数字
 * @param {ImageData} charImageData - 字符区域的图像数据
 * @param {number} width - 字符宽度
 * @param {number} height - 字符高度
 * @returns {string} - 识别出的字符
 */
function recognizeDigit(charImageData, width, height) {
  const data = charImageData.data;
  
  // 计算字符的特征
  const blackPixelCount = countBlackPixels(data);
  const aspectRatio = width / height;
  
  // 基于特征进行简单的数字识别
  // 这里使用一些启发式规则来识别数字
  
  // 计算垂直和水平投影
  const verticalProjection = calculateVerticalProjection(data, width, height);
  const horizontalProjection = calculateHorizontalProjection(data, width, height);
  
  // 基于投影特征识别数字
  // 1 通常很窄，垂直投影均匀
  if (aspectRatio < 0.5 && isUniformVertical(verticalProjection)) {
    return '1';
  }
  
  // 0 通常有两个明显的垂直边缘和中间空白
  if (hasTwoVerticalEdges(verticalProjection) && hasHorizontalEdges(horizontalProjection)) {
    return '0';
  }
  
  // 8 通常有三个水平线(上中下)和两个垂直边缘
  if (hasThreeHorizontalLines(horizontalProjection) && hasTwoVerticalEdges(verticalProjection)) {
    return '8';
  }
  
  // 基于黑色像素比例的简单识别
  const blackRatio = blackPixelCount / (width * height * 4);
  
  if (blackRatio < 0.2) {
    return '1'; // 1 通常黑色像素最少
  } else if (blackRatio > 0.4) {
    return '8'; // 8 通常黑色像素最多
  } else if (blackRatio > 0.35) {
    return '0'; // 0 通常黑色像素较多
  } else if (blackRatio > 0.3) {
    if (hasTopHeavy(horizontalProjection)) {
      return '9'; // 9 通常上部较重
    } else {
      return '6'; // 6 通常下部较重
    }
  } else if (blackRatio > 0.25) {
    if (hasTopHeavy(horizontalProjection)) {
      return '7'; // 7 通常上部较重
    } else {
      return '5'; // 5 分布较均匀
    }
  } else {
    if (hasTopHeavy(horizontalProjection)) {
      return '4'; // 4 通常上部较重
    } else {
      return '3'; // 3 分布较均匀
    }
  }
}

/**
 * 计算黑色像素数量
 */
function countBlackPixels(data) {
  let count = 0;
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    const brightness = (r + g + b) / 3;
    if (brightness < 128) count++;
  }
  return count;
}

/**
 * 计算垂直投影
 */
function calculateVerticalProjection(data, width, height) {
  const projection = new Array(width).fill(0);
  
  for (let x = 0; x < width; x++) {
    for (let y = 0; y < height; y++) {
      const i = (y * width + x) * 4;
      const brightness = (data[i] + data[i + 1] + data[i + 2]) / 3;
      if (brightness < 128) {
        projection[x]++;
      }
    }
  }
  
  return projection;
}

/**
 * 计算水平投影
 */
function calculateHorizontalProjection(data, width, height) {
  const projection = new Array(height).fill(0);
  
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const i = (y * width + x) * 4;
      const brightness = (data[i] + data[i + 1] + data[i + 2]) / 3;
      if (brightness < 128) {
        projection[y]++;
      }
    }
  }
  
  return projection;
}

/**
 * 检查垂直投影是否均匀
 */
function isUniformVertical(projection) {
  const avg = projection.reduce((sum, val) => sum + val, 0) / projection.length;
  const variance = projection.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / projection.length;
  return variance < 5; // 低方差表示均匀分布
}

/**
 * 检查是否有两个明显的垂直边缘
 */
function hasTwoVerticalEdges(projection) {
  let edges = 0;
  let inEdge = false;
  
  for (let i = 0; i < projection.length; i++) {
    if (projection[i] > 0 && !inEdge) {
      inEdge = true;
      edges++;
    } else if (projection[i] === 0 && inEdge) {
      inEdge = false;
    }
  }
  
  return edges === 2;
}

/**
 * 检查是否有水平边缘
 */
function hasHorizontalEdges(projection) {
  let edges = 0;
  let inEdge = false;
  
  for (let i = 0; i < projection.length; i++) {
    if (projection[i] > 0 && !inEdge) {
      inEdge = true;
      edges++;
    } else if (projection[i] === 0 && inEdge) {
      inEdge = false;
    }
  }
  
  return edges >= 2;
}

/**
 * 检查是否有三条水平线
 */
function hasThreeHorizontalLines(projection) {
  let lines = 0;
  let inLine = false;
  
  for (let i = 0; i < projection.length; i++) {
    if (projection[i] > 0 && !inLine) {
      inLine = true;
      lines++;
    } else if (projection[i] === 0 && inLine) {
      inLine = false;
    }
  }
  
  return lines === 3;
}

/**
 * 检查是否上部较重
 */
function hasTopHeavy(projection) {
  const half = Math.floor(projection.length / 2);
  const topSum = projection.slice(0, half).reduce((sum, val) => sum + val, 0);
  const bottomSum = projection.slice(half).reduce((sum, val) => sum + val, 0);
  
  return topSum > bottomSum;
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { simpleOCR };
} else {
  window.simpleOCR = simpleOCR;
} 
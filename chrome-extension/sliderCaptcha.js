/**
 * 滑块验证码处理模块
 * 用于检测、识别和自动完成滑块验证码
 * 版本: 1.1.0 - 增加自检机制
 */

// 自检机制，确保模块正确加载
(function() {
  console.log('滑块验证码处理模块初始化中...');
  
  // 检查是否已经加载过
  if (window._sliderCaptchaLoaded) {
    console.log('滑块验证码处理模块已经加载，避免重复初始化');
    return;
  }
  
  // 标记为已加载
  window._sliderCaptchaLoaded = true;
  
  // 在控制台提供一个全局变量，方便调试
  window._sliderCaptchaVersion = '1.1.0';
  
  console.log('滑块验证码处理模块初始化完成');
})();

// 限流器，避免过于频繁的操作
const rateLimiter = {
  queue: [],
  processing: false,
  
  addRequest: async function(callback) {
    return new Promise((resolve, reject) => {
      this.queue.push({ callback, resolve, reject });
      this.processQueue();
    });
  },
  
  processQueue: async function() {
    if (this.processing || this.queue.length === 0) return;
    
    this.processing = true;
    const { callback, resolve, reject } = this.queue.shift();
    
    try {
      const result = await callback();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.processing = false;
      setTimeout(() => this.processQueue(), 500); // 500ms 间隔
    }
  }
};

/**
 * 检测页面中是否存在滑块验证码
 * @returns {Array} - 可能的滑块验证码元素数组
 */
function detectSliderCaptcha() {
  console.log('开始检测滑块验证码...');
  
  // 基于类名/ID检测
  const sliderSelectors = [
    '.slider', 
    '.captcha-slider', 
    '.drag', 
    '.sliderContainer',
    '.verify-slider',
    '.nc_scale',
    '[id*="slider"]', 
    '[id*="captcha"]',
    '[id*="verify"]',
    '.yidun_slider',
    '.geetest_slider_button',
    // 增加一些常见的滑块验证码选择器
    '.gt_slider_knob',
    '.gt_slice',
    '.geetest_slider',
    '.dx_captcha',
    '.dx_slider',
    '.slide-to-unlock',
    '.slide-verify',
    '.verify-move-block',
    '.verify-img-panel'
  ];
  
  const sliderElements = document.querySelectorAll(sliderSelectors.join(', '));
  console.log(`基于选择器找到 ${sliderElements.length} 个可能的滑块元素`);
  
  // 基于文本内容检测
  const textHints = Array.from(document.querySelectorAll('div, p, span')).filter(el => {
    const text = el.textContent || '';
    return (text.includes('拖动') && text.includes('滑块')) || 
           text.includes('滑动验证') || 
           text.includes('拖动完成验证') ||
           text.includes('向右滑动填充拼图') ||
           text.includes('滑动解锁') ||
           text.includes('拖动滑块验证') ||
           text.includes('点击按钮进行验证') ||
           text.includes('点击开始验证');
  });
  console.log(`基于文本内容找到 ${textHints.length} 个可能的滑块提示元素`);
  
  // 组合检测结果
  const results = [...Array.from(sliderElements), ...textHints];
  console.log(`总共找到 ${results.length} 个可能的滑块验证码相关元素`);
  
  return results;
}

/**
 * 等待滑块验证码元素出现
 * @param {number} timeout - 超时时间(ms)
 * @returns {Promise<boolean>} - 是否成功检测到滑块元素
 */
async function waitForSliderElements(timeout = 5000) {
  console.log(`等待滑块验证码元素出现，超时时间: ${timeout}ms`);
  
  return new Promise(resolve => {
    // 设置超时
    const timeoutId = setTimeout(() => {
      observer.disconnect();
      console.log('等待滑块元素超时');
      resolve(false);
    }, timeout);
    
    // 立即检查一次
    const elements = detectSliderCaptcha();
    if (elements.length > 0) {
      clearTimeout(timeoutId);
      console.log('立即检测到滑块元素');
      resolve(true);
      return;
    }
    
    // 创建MutationObserver监听DOM变化
    const observer = new MutationObserver((mutations) => {
      const elements = detectSliderCaptcha();
      if (elements.length > 0) {
        clearTimeout(timeoutId);
        observer.disconnect();
        console.log('DOM变化后检测到滑块元素');
        resolve(true);
      }
    });
    
    // 开始观察整个文档的变化
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: false
    });
  });
}

/**
 * 查找可点击的验证码触发元素
 * @returns {HTMLElement|null} - 验证码触发元素
 */
function findCaptchaTrigger() {
  console.log('查找验证码触发元素...');
  
  // 常见的验证码触发元素选择器
  const triggerSelectors = [
    '.captcha-trigger',
    '.verify-trigger',
    '.captcha-img',
    '.verify-img',
    '[id*="captcha"]',
    '[id*="verify"]',
    '.geetest_radar_tip',
    '.geetest_btn',
    '.dx_captcha_clickable',
    '.verify-refresh',
    // 一些通用选择器
    'img[src*="captcha"]',
    'img[src*="verify"]',
    'div[class*="captcha"]',
    'div[class*="verify"]'
  ];
  
  // 尝试查找触发元素
  const triggers = document.querySelectorAll(triggerSelectors.join(', '));
  console.log(`找到 ${triggers.length} 个可能的验证码触发元素`);
  
  if (triggers.length > 0) {
    // 返回第一个可见的触发元素
    for (const trigger of triggers) {
      const style = window.getComputedStyle(trigger);
      if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
        console.log('找到可见的验证码触发元素:', trigger);
        return trigger;
      }
    }
  }
  
  // 基于文本内容查找
  const textTriggers = Array.from(document.querySelectorAll('div, span, button, a')).filter(el => {
    const text = el.textContent || '';
    return text.includes('点击验证') || 
           text.includes('开始验证') || 
           text.includes('点击按钮验证') ||
           text.includes('安全验证');
  });
  
  if (textTriggers.length > 0) {
    console.log('基于文本内容找到验证码触发元素:', textTriggers[0]);
    return textTriggers[0];
  }
  
  console.log('未找到验证码触发元素');
  return null;
}

/**
 * 分析滑块验证码的结构，找出滑块和背景图片
 * @param {Array} elements - 可能的滑块验证码元素
 * @returns {Object} - 包含滑块和背景图片元素的对象
 */
function analyzeSliderStructure(elements) {
  console.log('开始分析滑块验证码结构...');
  console.log(`找到 ${elements.length} 个可能的滑块相关元素`);
  
  // 记录所有元素的基本信息，帮助调试
  elements.forEach((el, index) => {
    try {
      const rect = el.getBoundingClientRect();
      console.log(`元素 ${index + 1}:`, {
        tagName: el.tagName,
        className: el.className || '无类名',
        id: el.id || '无ID',
        text: (el.textContent || '').substring(0, 20) + (el.textContent && el.textContent.length > 20 ? '...' : ''),
        size: `${rect.width}x${rect.height}`,
        position: `(${rect.left}, ${rect.top})`,
        style: el.getAttribute('style') || '无样式',
        cursor: window.getComputedStyle(el).cursor
      });
    } catch (error) {
      console.log(`元素 ${index + 1} 信息获取失败:`, error);
    }
  });
  
  let slider = null;
  let background = null;
  let container = null;
  
  // 查找最可能的滑块元素
  for (const el of elements) {
    // 如果元素是可拖动的或有相关类名，很可能是滑块
    const isSlider = el.getAttribute('draggable') === 'true' || 
        el.classList.contains('slider') || 
        el.classList.contains('drag') ||
        el.style.cursor === 'pointer' ||
        el.style.cursor === 'move' ||
        el.classList.contains('gt_slider_knob') ||
        el.classList.contains('yidun_slider') ||
        el.classList.contains('geetest_slider_button');
    
    if (isSlider) {
      slider = el;
      container = el.parentElement;
      console.log('找到可能的滑块元素:', {
        tagName: el.tagName,
        className: el.className || '无类名',
        id: el.id || '无ID',
        cursor: window.getComputedStyle(el).cursor,
        draggable: el.getAttribute('draggable')
      });
      break;
    }
  }
  
  // 如果没有找到明确的滑块，尝试基于大小和位置推断
  if (!slider && elements.length > 0) {
    console.log('未找到明确的滑块元素，尝试基于大小推断...');
    // 假设最小的元素可能是滑块
    let minArea = Infinity;
    for (const el of elements) {
      const rect = el.getBoundingClientRect();
      const area = rect.width * rect.height;
      console.log(`元素面积: ${area}, 宽x高: ${rect.width}x${rect.height}`);
      if (area > 0 && area < minArea) {
        minArea = area;
        slider = el;
        container = el.parentElement;
      }
    }
    if (slider) {
      console.log('基于大小推断的滑块元素:', {
        tagName: slider.tagName,
        className: slider.className || '无类名',
        id: slider.id || '无ID',
        area: minArea
      });
    }
  }
  
  // 查找背景图片
  if (container) {
    console.log('开始在容器中查找背景图片...');
    console.log('容器信息:', {
      tagName: container.tagName,
      className: container.className || '无类名',
      id: container.id || '无ID',
      childrenCount: container.children.length
    });
    
    // 在容器中查找图片元素
    const images = container.querySelectorAll('img');
    console.log(`容器中找到 ${images.length} 个图片元素`);
    
    for (const img of images) {
      const rect = img.getBoundingClientRect();
      console.log('图片元素:', {
        src: img.src,
        size: `${rect.width}x${rect.height}`,
        visible: rect.width > 0 && rect.height > 0
      });
      
      // 背景图片通常比滑块大
      if (rect.width > 0 && rect.height > 0) {
        background = img;
        console.log('找到可能的背景图片:', img.src);
        break;
      }
    }
    
    // 如果没有找到明确的图片元素，尝试查找带背景图的div
    if (!background) {
      console.log('未找到图片元素，尝试查找带背景图的div...');
      const divs = container.querySelectorAll('div');
      console.log(`容器中找到 ${divs.length} 个div元素`);
      
      for (const div of divs) {
        if (div === slider) continue;
        
        const style = window.getComputedStyle(div);
        console.log('Div背景图:', {
          backgroundImage: style.backgroundImage,
          size: `${div.offsetWidth}x${div.offsetHeight}`
        });
        
        if (style.backgroundImage && style.backgroundImage !== 'none') {
          background = div;
          console.log('找到带背景图的元素:', {
            className: div.className || '无类名',
            id: div.id || '无ID',
            backgroundImage: style.backgroundImage
          });
          break;
        }
      }
    }
  } else {
    console.log('未找到容器元素，无法查找背景图片');
  }
  
  // 如果仍然没有找到背景，尝试从文档中查找
  if (!background) {
    console.log('在容器中未找到背景，尝试从整个文档查找...');
    // 查找所有图片和带背景的div
    const allImages = document.querySelectorAll('img');
    console.log(`文档中找到 ${allImages.length} 个图片元素`);
    
    const allBgDivs = Array.from(document.querySelectorAll('div')).filter(div => {
      const style = window.getComputedStyle(div);
      return style.backgroundImage && style.backgroundImage !== 'none';
    });
    console.log(`文档中找到 ${allBgDivs.length} 个带背景图的div元素`);
    
    // 组合所有可能的背景元素
    const possibleBgs = [...Array.from(allImages), ...allBgDivs];
    
    // 查找与滑块在同一容器或附近的元素
    if (slider) {
      const sliderRect = slider.getBoundingClientRect();
      console.log('滑块位置:', {
        left: sliderRect.left,
        top: sliderRect.top,
        width: sliderRect.width,
        height: sliderRect.height
      });
      
      // 按照与滑块的距离排序
      possibleBgs.sort((a, b) => {
        const aRect = a.getBoundingClientRect();
        const bRect = b.getBoundingClientRect();
        
        const aDist = Math.abs(aRect.top - sliderRect.top);
        const bDist = Math.abs(bRect.top - sliderRect.top);
        
        return aDist - bDist;
      });
      
      // 选择最近的元素作为背景
      if (possibleBgs.length > 0) {
        background = possibleBgs[0];
        const bgRect = background.getBoundingClientRect();
        console.log('基于位置关系找到可能的背景元素:', {
          tagName: background.tagName,
          className: background.className || '无类名',
          id: background.id || '无ID',
          distance: Math.abs(bgRect.top - sliderRect.top),
          size: `${bgRect.width}x${bgRect.height}`
        });
      }
    }
  }
  
  // 最终结果
  console.log('滑块验证码结构分析结果:', {
    foundSlider: !!slider,
    foundBackground: !!background,
    foundContainer: !!container
  });
  
  return { slider, background, container };
}

/**
 * 从元素中提取图像数据
 * @param {HTMLElement} element - 图像元素或带背景图的元素
 * @returns {Promise<HTMLCanvasElement>} - 包含图像的Canvas元素
 */
async function extractImageFromElement(element) {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (element instanceof HTMLImageElement) {
      // 如果是img元素
      if (element.complete) {
        canvas.width = element.naturalWidth || element.width;
        canvas.height = element.naturalHeight || element.height;
        ctx.drawImage(element, 0, 0);
        resolve(canvas);
      } else {
        element.onload = () => {
          canvas.width = element.naturalWidth || element.width;
          canvas.height = element.naturalHeight || element.height;
          ctx.drawImage(element, 0, 0);
          resolve(canvas);
        };
        element.onerror = () => reject(new Error('图片加载失败'));
      }
    } else {
      // 如果是带背景图的元素
      const style = window.getComputedStyle(element);
      if (style.backgroundImage && style.backgroundImage !== 'none') {
        const bgImg = new Image();
        // 提取url("...")中的URL
        const urlMatch = style.backgroundImage.match(/url\(['"]?([^'"]+)['"]?\)/);
        
        if (urlMatch && urlMatch[1]) {
          bgImg.src = urlMatch[1];
          bgImg.onload = () => {
            canvas.width = bgImg.naturalWidth;
            canvas.height = bgImg.naturalHeight;
            ctx.drawImage(bgImg, 0, 0);
            resolve(canvas);
          };
          bgImg.onerror = () => reject(new Error('背景图片加载失败'));
        } else {
          reject(new Error('无法提取背景图片URL'));
        }
      } else {
        // 如果元素没有背景图，尝试直接绘制元素
        const rect = element.getBoundingClientRect();
        canvas.width = rect.width;
        canvas.height = rect.height;
        
        // 使用html2canvas或其他方法绘制元素
        // 这里简化处理，实际可能需要更复杂的逻辑
        try {
          ctx.fillStyle = 'white';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          resolve(canvas);
        } catch (error) {
          reject(new Error('无法绘制元素'));
        }
      }
    }
  });
}

/**
 * 预处理图像，提取图像数据
 * @param {HTMLElement} bgElement - 背景图片元素
 * @param {HTMLElement} sliderElement - 滑块元素
 * @returns {Promise<Object>} - 包含背景和滑块图像数据的对象
 */
async function preprocessImages(bgElement, sliderElement) {
  console.log('开始处理图像...');
  
  try {
    // 提取背景图像
    const bgCanvas = await extractImageFromElement(bgElement);
    const bgCtx = bgCanvas.getContext('2d');
    const bgData = bgCtx.getImageData(0, 0, bgCanvas.width, bgCanvas.height);
    
    // 提取滑块图像
    const sliderCanvas = await extractImageFromElement(sliderElement);
    const sliderCtx = sliderCanvas.getContext('2d');
    const sliderData = sliderCtx.getImageData(0, 0, sliderCanvas.width, sliderCanvas.height);
    
    console.log(`背景图像尺寸: ${bgCanvas.width}x${bgCanvas.height}`);
    console.log(`滑块图像尺寸: ${sliderCanvas.width}x${sliderCanvas.height}`);
    
    return { bgData, sliderData, bgCanvas, sliderCanvas };
  } catch (error) {
    console.error('处理图像时出错:', error);
    throw error;
  }
}

/**
 * 图像灰度化处理
 * @param {ImageData} imageData - 原始图像数据
 * @returns {ImageData} - 灰度化后的图像数据
 */
function grayscale(imageData) {
  const { width, height, data } = imageData;
  const grayscaleData = new Uint8ClampedArray(data.length);
  
  for (let i = 0; i < data.length; i += 4) {
    const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
    grayscaleData[i] = grayscaleData[i + 1] = grayscaleData[i + 2] = gray;
    grayscaleData[i + 3] = data[i + 3]; // 保持原始Alpha通道
  }
  
  return new ImageData(grayscaleData, width, height);
}

/**
 * 使用Sobel算子进行边缘检测
 * @param {ImageData} imageData - 图像数据
 * @returns {ImageData} - 边缘检测后的图像数据
 */
function detectEdges(imageData) {
  // 先进行灰度化处理
  const grayImageData = grayscale(imageData);
  const { width, height, data } = grayImageData;
  const edgeData = new Uint8ClampedArray(data.length);
  
  // 复制Alpha通道
  for (let i = 3; i < data.length; i += 4) {
    edgeData[i] = data[i];
  }
  
  // Sobel边缘检测
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      const idx = (y * width + x) * 4;
      
      // 获取周围像素的灰度值
      const topLeft = data[((y - 1) * width + (x - 1)) * 4];
      const top = data[((y - 1) * width + x) * 4];
      const topRight = data[((y - 1) * width + (x + 1)) * 4];
      const left = data[(y * width + (x - 1)) * 4];
      const right = data[(y * width + (x + 1)) * 4];
      const bottomLeft = data[((y + 1) * width + (x - 1)) * 4];
      const bottom = data[((y + 1) * width + x) * 4];
      const bottomRight = data[((y + 1) * width + (x + 1)) * 4];
      
      // 计算水平和垂直梯度
      const gx = -topLeft + topRight - 2 * left + 2 * right - bottomLeft + bottomRight;
      const gy = -topLeft - 2 * top - topRight + bottomLeft + 2 * bottom + bottomRight;
      
      // 计算梯度强度
      const magnitude = Math.sqrt(gx * gx + gy * gy);
      
      // 应用阈值
      const threshold = 100;
      const edgeValue = magnitude > threshold ? 255 : 0;
      
      edgeData[idx] = edgeData[idx + 1] = edgeData[idx + 2] = edgeValue;
    }
  }
  
  return new ImageData(edgeData, width, height);
}

/**
 * 查找滑块应该移动的位置
 * @param {ImageData} bgEdges - 背景图像的边缘数据
 * @param {ImageData} sliderEdges - 滑块图像的边缘数据
 * @returns {number} - 滑块应移动的距离
 */
function findSliderPosition(bgEdges, sliderEdges) {
  const { width: bgWidth, height: bgHeight, data: bgData } = bgEdges;
  const { width: sliderWidth, height: sliderHeight, data: sliderData } = sliderEdges;
  
  console.log(`开始寻找滑块位置，背景尺寸: ${bgWidth}x${bgHeight}，滑块尺寸: ${sliderWidth}x${sliderHeight}`);
  
  let bestMatch = { x: 0, score: Infinity };
  
  // 在背景图上滑动模板，寻找最佳匹配位置
  for (let x = 0; x < bgWidth - sliderWidth; x++) {
    let diffScore = 0;
    
    // 只计算滑块中心区域的差异，提高性能
    const sampleStep = 2; // 每隔2个像素采样一次
    let sampleCount = 0;
    
    for (let sy = Math.floor(sliderHeight * 0.2); sy < sliderHeight * 0.8; sy += sampleStep) {
      for (let sx = Math.floor(sliderWidth * 0.2); sx < sliderWidth * 0.8; sx += sampleStep) {
        const sliderIdx = (sy * sliderWidth + sx) * 4;
        
        // 只比较有边缘的像素（值为255的像素）
        if (sliderData[sliderIdx] === 255) {
          const bgIdx = (sy * bgWidth + (x + sx)) * 4;
          
          // 计算像素差异
          const diff = Math.abs(bgData[bgIdx] - sliderData[sliderIdx]);
          diffScore += diff;
          sampleCount++;
        }
      }
    }
    
    // 计算平均差异
    if (sampleCount > 0) {
      diffScore /= sampleCount;
    }
    
    // 更新最佳匹配
    if (diffScore < bestMatch.score) {
      bestMatch = { x, score: diffScore };
    }
  }
  
  console.log(`找到最佳匹配位置: x=${bestMatch.x}, 差异分数=${bestMatch.score}`);
  return bestMatch.x;
}

/**
 * 模拟人类拖动行为
 * @param {HTMLElement} slider - 滑块元素
 * @param {number} distance - 需要移动的距离
 * @returns {Promise<void>}
 */
async function simulateHumanDrag(slider, distance) {
  console.log(`开始模拟人类拖动，目标距离: ${distance}px`);
  
  try {
    // 记录滑块元素信息
    console.log('滑块元素信息:', {
      tagName: slider.tagName,
      className: slider.className,
      id: slider.id,
      style: slider.getAttribute('style') || '无样式'
    });
    
    // 获取滑块元素的位置
    const rect = slider.getBoundingClientRect();
    console.log('滑块位置信息:', {
      left: rect.left,
      top: rect.top,
      width: rect.width,
      height: rect.height
    });
    
    const startX = rect.left + rect.width / 2;
    const startY = rect.top + rect.height / 2;
    console.log(`起始坐标: (${startX}, ${startY})`);
    
    // 创建鼠标按下事件
    console.log('准备创建鼠标按下事件...');
    const mouseDown = new MouseEvent('mousedown', {
      bubbles: true,
      cancelable: true,
      view: window,
      clientX: startX,
      clientY: startY
    });
    
    // 记录事件属性
    console.log('mouseDown事件属性:', {
      bubbles: mouseDown.bubbles,
      cancelable: mouseDown.cancelable,
      clientX: mouseDown.clientX,
      clientY: mouseDown.clientY
    });
    
    // 触发鼠标按下事件
    console.log('触发鼠标按下事件...');
    const mouseDownResult = slider.dispatchEvent(mouseDown);
    console.log('鼠标按下事件触发结果:', mouseDownResult ? '成功' : '被取消');
    
    // 等待一小段时间，模拟人类反应
    console.log('等待人类反应时间(100ms)...');
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 分段模拟移动，使轨迹更自然
    const steps = 30;
    const duration = 1000; // 总时长1秒
    
    // 添加随机波动，模拟人手抖动
    function getRandomOffset() {
      return Math.random() * 2 - 1; // -1到1之间的随机值
    }
    
    // 模拟非线性移动速度（先慢后快再慢）
    function easeInOutQuad(t) {
      return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    }
    
    console.log(`开始分段移动，总步数: ${steps}, 总时长: ${duration}ms`);
    
    // 分段移动
    for (let i = 1; i <= steps; i++) {
      const progress = i / steps;
      const easeProgress = easeInOutQuad(progress);
      const moveX = startX + easeProgress * distance + getRandomOffset();
      const moveY = startY + getRandomOffset();
      
      if (i % 5 === 0 || i === 1 || i === steps) {
        console.log(`移动进度: ${Math.round(progress * 100)}%, 坐标: (${moveX.toFixed(2)}, ${moveY.toFixed(2)})`);
      }
      
      const mouseMove = new MouseEvent('mousemove', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: moveX,
        clientY: moveY
      });
      
      try {
        const mouseMoveResult = slider.dispatchEvent(mouseMove);
        if (i === 1 || i === Math.floor(steps/2) || i === steps) {
          console.log(`步骤 ${i}: 鼠标移动事件触发结果:`, mouseMoveResult ? '成功' : '被取消');
        }
        
        // 检查滑块是否真的移动了
        if (i === Math.floor(steps/2)) {
          const currentRect = slider.getBoundingClientRect();
          console.log('滑块中途位置:', {
            left: currentRect.left,
            moved: currentRect.left - rect.left
          });
        }
      } catch (moveError) {
        console.error(`步骤 ${i}: 触发鼠标移动事件时出错:`, moveError);
      }
      
      // 每步之间添加随机延迟
      const stepDelay = (duration / steps) * (0.8 + Math.random() * 0.4);
      await new Promise(resolve => setTimeout(resolve, stepDelay));
    }
    
    // 检查滑块是否移动到位
    const finalRect = slider.getBoundingClientRect();
    console.log('滑块最终位置:', {
      left: finalRect.left,
      moved: finalRect.left - rect.left,
      expectedMove: distance
    });
    
    // 触发鼠标释放事件
    console.log('准备触发鼠标释放事件...');
    const mouseUp = new MouseEvent('mouseup', {
      bubbles: true,
      cancelable: true,
      view: window,
      clientX: startX + distance,
      clientY: startY
    });
    
    console.log('mouseUp事件属性:', {
      bubbles: mouseUp.bubbles,
      cancelable: mouseUp.cancelable,
      clientX: mouseUp.clientX,
      clientY: mouseUp.clientY
    });
    
    const mouseUpResult = slider.dispatchEvent(mouseUp);
    console.log('鼠标释放事件触发结果:', mouseUpResult ? '成功' : '被取消');
    
    // 尝试触发额外的事件，以防某些验证码需要
    console.log('尝试触发额外事件...');
    try {
      // 有些滑块可能需要click事件
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: startX + distance,
        clientY: startY
      });
      slider.dispatchEvent(clickEvent);
      
      // 有些可能需要mouseout事件
      const mouseOutEvent = new MouseEvent('mouseout', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: startX + distance,
        clientY: startY
      });
      slider.dispatchEvent(mouseOutEvent);
    } catch (extraError) {
      console.log('触发额外事件时出错(非关键):', extraError);
    }
    
    console.log('模拟拖动完成');
  } catch (error) {
    console.error('模拟拖动过程中发生错误:', error);
    throw error;
  }
}

/**
 * 检查验证结果
 * @returns {Promise<boolean>} - 验证是否成功
 */
function checkVerificationResult() {
  return new Promise((resolve) => {
    console.log('等待验证结果...');
    
    // 设置超时，最多等待3秒
    const timeout = setTimeout(() => {
      console.log('验证结果等待超时');
      resolve(false);
    }, 3000);
    
    // 创建MutationObserver监听DOM变化
    const observer = new MutationObserver((mutations) => {
      // 检查是否出现成功或失败的提示
      const successSelectors = [
        '.success-tip', 
        '.verify-success',
        '.captcha-success',
        '.valid-success',
        '[class*="success"]'
      ];
      
      const failSelectors = [
        '.fail-tip', 
        '.verify-fail',
        '.captcha-fail',
        '.valid-error',
        '[class*="fail"]',
        '[class*="error"]'
      ];
      
      const successElement = document.querySelector(successSelectors.join(', '));
      const failElement = document.querySelector(failSelectors.join(', '));
      
      if (successElement) {
        console.log('检测到验证成功提示');
        clearTimeout(timeout);
        observer.disconnect();
        resolve(true);
      } else if (failElement) {
        console.log('检测到验证失败提示');
        clearTimeout(timeout);
        observer.disconnect();
        resolve(false);
      }
    });
    
    // 开始观察整个文档的变化
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true
    });
  });
}

/**
 * 解决滑块验证码
 * @returns {Promise<boolean>} - 是否成功解决
 */
async function solveSliderCaptcha() {
  try {
    console.log('开始解决滑块验证码...');
    
    // 检测滑块验证码元素
    console.log('检测滑块验证码元素...');
    const sliderElements = detectSliderCaptcha();
    
    if (!sliderElements || sliderElements.length === 0) {
      console.log('未检测到滑块验证码元素，可能不是滑块验证码或元素选择器需要更新');
      return false;
    }
    
    console.log(`检测到 ${sliderElements.length} 个可能的滑块验证码元素`);
    
    // 分析滑块结构
    console.log('分析滑块验证码结构...');
    const { slider, background, container } = analyzeSliderStructure(sliderElements);
    
    if (!slider) {
      console.error('未找到滑块元素，无法继续');
      return false;
    }
    
    if (!background) {
      console.warn('未找到背景图片，将尝试直接拖动滑块');
    }
    
    // 如果有背景图片，尝试识别缺口位置
    let distance = 0;
    if (background) {
      try {
        console.log('尝试识别缺口位置...');
        distance = await identifyGap(background);
        console.log(`识别到缺口距离: ${distance}px`);
      } catch (error) {
        console.error('识别缺口位置失败:', error);
        // 如果识别失败，尝试使用固定距离
        distance = estimateDistance(slider, container);
        console.log(`使用估算距离: ${distance}px`);
      }
    } else {
      // 如果没有背景图片，估算一个距离
      distance = estimateDistance(slider, container);
      console.log(`无背景图片，使用估算距离: ${distance}px`);
    }
    
    // 确保距离合理
    if (!distance || distance <= 0) {
      console.warn('获取到的距离不合理，使用默认值');
      // 使用一个默认值，通常滑块需要移动到容器宽度的60%-80%
      if (container) {
        const containerWidth = container.getBoundingClientRect().width;
        distance = containerWidth * 0.7;
        console.log(`使用容器宽度的70%作为默认距离: ${distance}px`);
      } else {
        distance = 150; // 完全找不到参考时的默认值
        console.log(`使用固定默认距离: ${distance}px`);
      }
    }
    
    // 模拟人类拖动行为
    console.log(`准备拖动滑块，目标距离: ${distance}px`);
    
    // 尝试最多3次
    let success = false;
    let attempts = 0;
    const maxAttempts = 3;
    
    while (!success && attempts < maxAttempts) {
      attempts++;
      console.log(`尝试拖动滑块 (第 ${attempts}/${maxAttempts} 次)`);
      
      try {
        await simulateHumanDrag(slider, distance);
        
        // 等待验证结果
        console.log('等待验证结果...');
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // 检查是否验证成功
        success = await checkVerificationSuccess();
        console.log(`验证结果: ${success ? '成功' : '失败'}`);
        
        if (success) {
          console.log('滑块验证码解决成功');
          return true;
        } else {
          console.log(`第 ${attempts} 次尝试失败，${attempts < maxAttempts ? '将重试' : '已达最大尝试次数'}`);
          
          // 如果失败但还有重试机会，调整距离再试
          if (attempts < maxAttempts) {
            // 随机调整距离，模拟人类再次尝试的行为
            const adjustment = (Math.random() * 20) - 10; // -10到10之间的随机调整
            distance += adjustment;
            console.log(`调整距离为: ${distance}px (${adjustment > 0 ? '+' : ''}${adjustment.toFixed(2)}px)`);
            
            // 等待一段时间再重试
            const waitTime = 1000 + Math.random() * 1000;
            console.log(`等待 ${waitTime.toFixed(0)}ms 后重试...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
          }
        }
      } catch (error) {
        console.error(`第 ${attempts} 次拖动滑块时出错:`, error);
        
        // 如果是因为元素不可交互导致的错误，尝试重新获取元素
        if (attempts < maxAttempts) {
          console.log('尝试重新获取滑块元素...');
          const newSliderElements = detectSliderCaptcha();
          const newStructure = analyzeSliderStructure(newSliderElements);
          
          if (newStructure.slider) {
            console.log('成功重新获取滑块元素');
            slider = newStructure.slider;
          } else {
            console.error('重新获取滑块元素失败');
          }
          
          // 等待一段时间再重试
          const waitTime = 1000 + Math.random() * 1000;
          console.log(`等待 ${waitTime.toFixed(0)}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }
    
    console.log(`滑块验证码解决${success ? '成功' : '失败'}`);
    return success;
  } catch (error) {
    console.error('解决滑块验证码过程中发生错误:', error);
    return false;
  }
}

/**
 * 估算滑块需要移动的距离
 * @param {HTMLElement} slider - 滑块元素
 * @param {HTMLElement} container - 容器元素
 * @returns {number} - 估算的距离
 */
function estimateDistance(slider, container) {
  try {
    console.log('估算滑块移动距离...');
    
    if (!container) {
      console.log('无容器元素，使用默认距离');
      return 150; // 默认值
    }
    
    const containerRect = container.getBoundingClientRect();
    const sliderRect = slider.getBoundingClientRect();
    
    console.log('容器尺寸:', {
      width: containerRect.width,
      height: containerRect.height
    });
    
    console.log('滑块尺寸:', {
      width: sliderRect.width,
      height: sliderRect.height
    });
    
    // 通常滑块需要移动到容器宽度的60%-80%
    const distance = containerRect.width * 0.7 - sliderRect.width / 2;
    console.log(`估算距离: ${distance}px (容器宽度的70% - 滑块宽度的一半)`);
    
    return distance;
  } catch (error) {
    console.error('估算距离时出错:', error);
    return 150; // 出错时返回默认值
  }
}

/**
 * 检查验证是否成功
 * @returns {Promise<boolean>} - 是否验证成功
 */
async function checkVerificationSuccess() {
  try {
    console.log('检查验证结果...');
    
    // 等待一小段时间，让验证结果显示
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 检查是否存在成功提示
    const successIndicators = [
      // 成功提示文本
      '验证成功',
      '验证通过',
      'success',
      '成功',
      // 成功图标类
      'success-icon',
      'icon-success'
    ];
    
    // 检查DOM中是否存在成功提示
    for (const indicator of successIndicators) {
      const elements = document.querySelectorAll(`*:contains('${indicator}'), .${indicator}`);
      if (elements.length > 0) {
        console.log(`找到成功指示器: "${indicator}"`);
        return true;
      }
    }
    
    // 检查是否存在失败提示
    const failureIndicators = [
      '验证失败',
      '重试',
      'try again',
      'failed',
      'failure',
      '失败'
    ];
    
    for (const indicator of failureIndicators) {
      const elements = document.querySelectorAll(`*:contains('${indicator}')`);
      if (elements.length > 0) {
        console.log(`找到失败指示器: "${indicator}"`);
        return false;
      }
    }
    
    // 如果没有明确的成功或失败指示，检查滑块是否移动到了终点
    const sliderElements = detectSliderCaptcha();
    if (sliderElements && sliderElements.length > 0) {
      const { slider, container } = analyzeSliderStructure(sliderElements);
      
      if (slider && container) {
        const sliderRect = slider.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        
        // 如果滑块接近容器右侧，可能表示成功
        const distanceToRight = containerRect.right - sliderRect.right;
        console.log(`滑块到容器右侧的距离: ${distanceToRight}px`);
        
        if (distanceToRight < 20) {
          console.log('滑块已接近容器右侧，可能验证成功');
          return true;
        }
      }
    }
    
    console.log('未找到明确的验证结果指示，假定验证失败');
    return false;
  } catch (error) {
    console.error('检查验证结果时出错:', error);
    return false;
  }
}

/**
 * 调试函数：显示滑块验证码处理过程
 */
function debugSliderCaptcha() {
  console.log('🔍 开始滑块验证码调试...');
  
  // 检测滑块验证码
  const elements = detectSliderCaptcha();
  if (elements.length === 0) {
    console.log('未检测到滑块验证码');
    return;
  }
  
  console.log(`检测到 ${elements.length} 个可能的滑块验证码元素`);
  
  // 高亮显示这些元素
  elements.forEach((el, index) => {
    const originalBorder = el.style.border;
    const originalBoxShadow = el.style.boxShadow;
    
    el.style.border = '2px solid red';
    el.style.boxShadow = '0 0 5px red';
    
    console.log(`元素 ${index + 1}:`, el);
    console.log(`  - tagName: ${el.tagName}`);
    console.log(`  - className: ${el.className}`);
    console.log(`  - id: ${el.id}`);
    console.log(`  - 文本内容: ${el.textContent}`);
    
    // 5秒后恢复原样
    setTimeout(() => {
      el.style.border = originalBorder;
      el.style.boxShadow = originalBoxShadow;
    }, 5000);
  });
  
  // 分析结构
  const { slider, background, container } = analyzeSliderStructure(elements);
  
  if (slider) {
    console.log('找到滑块元素:', slider);
    slider.style.border = '2px solid blue';
    slider.style.boxShadow = '0 0 5px blue';
  } else {
    console.log('未找到滑块元素');
  }
  
  if (background) {
    console.log('找到背景元素:', background);
    background.style.border = '2px solid green';
    background.style.boxShadow = '0 0 5px green';
  } else {
    console.log('未找到背景元素');
  }
  
  if (container) {
    console.log('找到容器元素:', container);
    const originalBorder = container.style.border;
    container.style.border = '1px dashed purple';
    
    // 5秒后恢复
    setTimeout(() => {
      container.style.border = originalBorder;
    }, 5000);
  }
}

// 暴露关键函数到全局作用域
window.solveSliderCaptcha = solveSliderCaptcha;
window.detectSliderCaptcha = detectSliderCaptcha;
window.analyzeSliderStructure = analyzeSliderStructure;
window.simulateHumanDrag = simulateHumanDrag;

// 添加自检函数，用于验证模块是否正确加载
window.checkSliderCaptchaModule = function() {
  console.log('滑块验证码模块自检...');
  
  const functions = {
    'solveSliderCaptcha': typeof solveSliderCaptcha === 'function',
    'detectSliderCaptcha': typeof detectSliderCaptcha === 'function',
    'analyzeSliderStructure': typeof analyzeSliderStructure === 'function',
    'simulateHumanDrag': typeof simulateHumanDrag === 'function'
  };
  
  console.log('滑块验证码模块函数可用性:', functions);
  
  const allAvailable = Object.values(functions).every(available => available);
  console.log(`滑块验证码模块状态: ${allAvailable ? '正常' : '异常'}`);
  
  return allAvailable;
};

// 自动执行自检
(function() {
  console.log('滑块验证码处理模块已加载');
  
  try {
    // 延迟执行自检，确保所有函数都已定义
    setTimeout(() => {
      window.checkSliderCaptchaModule();
    }, 100);
  } catch (error) {
    console.error('滑块验证码模块自检失败:', error);
  }
})(); 
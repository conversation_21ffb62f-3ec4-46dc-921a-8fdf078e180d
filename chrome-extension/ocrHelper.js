console.log('OCR 辅助模块已加载');

// 获取OCR服务URL
async function getOCRServiceURL() {
  try {
    // 直接使用外网OCR服务，避免复杂的服务器检测和缓存逻辑
    // 这样确保生产环境始终使用正确的OCR接口
    const defaultUrl = 'https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize';
    console.log(`使用外网OCR服务: ${defaultUrl}`);
    
    // 清除可能存在的旧缓存
    if (typeof chrome !== 'undefined' && chrome.storage) {
      try {
        await chrome.storage.local.remove(['lastSuccessfulServerUrl']);
        console.log('已清除OCR服务器缓存');
      } catch (e) {
        console.log('无法清除存储缓存');
      }
    }
    
    return defaultUrl;
    
  } catch (error) {
    console.error('获取OCR服务URL失败:', error);
    return 'https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize'; // 默认使用生产环境
  }
}

/**
 * 限流器类 - 使用令牌桶算法实现API调用限流
 * 确保每秒最多调用一次API
 */
class RateLimiter {
  constructor(maxRequestsPerSecond = 1) {
    this.maxRequestsPerSecond = maxRequestsPerSecond;
    this.lastRequestTime = 0;
    this.queue = [];
    this.processing = false;
  }

  /**
   * 添加请求到队列并处理
   * @param {Function} requestFn - 实际发送请求的函数
   * @returns {Promise} - 请求的结果
   */
  async addRequest(requestFn) {
    return new Promise((resolve, reject) => {
      // 将请求添加到队列
      this.queue.push({ requestFn, resolve, reject });
      
      // 如果没有正在处理的请求，开始处理队列
      if (!this.processing) {
        this.processQueue();
      }
    });
  }

  /**
   * 处理请求队列
   */
  async processQueue() {
    if (this.queue.length === 0) {
      this.processing = false;
      return;
    }

    this.processing = true;
    const { requestFn, resolve, reject } = this.queue.shift();
    
    try {
      // 计算需要等待的时间
      const now = Date.now();
      const timeElapsed = now - this.lastRequestTime;
      const minInterval = 1000 / this.maxRequestsPerSecond; // 毫秒
      
      let waitTime = 0;
      if (timeElapsed < minInterval) {
        waitTime = minInterval - timeElapsed;
      }
      
      // 如果需要等待，则延迟执行
      if (waitTime > 0) {
        console.log(`限流：等待 ${waitTime}ms 后发送请求`);
        await new Promise(r => setTimeout(r, waitTime));
      }
      
      // 执行请求
      this.lastRequestTime = Date.now();
      const result = await requestFn();
      resolve(result);
    } catch (error) {
      reject(error);
    }
    
    // 继续处理队列中的下一个请求
    this.processQueue();
  }
}

// 创建全局限流器实例
const rateLimiter = new RateLimiter(1); // 每秒最多1个请求

/**
 * 将图片元素转换为 Base64 编码的字符串
 * @param {HTMLImageElement} imgElement - 图片元素
 * @returns {Promise<string>} - Base64 编码的图片数据
 */
async function imageToBase64(imgElement) {
  return new Promise((resolve, reject) => {
    try {
      // 确保图片已加载完成
      if (imgElement.complete && imgElement.naturalHeight !== 0) {
        convertImageToBase64();
      } else {
        imgElement.onload = convertImageToBase64;
        imgElement.onerror = () => reject(new Error('图片加载失败'));
      }

      function convertImageToBase64() {
        try {
          // 创建 Canvas 元素
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          // 设置 Canvas 尺寸与图片一致
          canvas.width = imgElement.naturalWidth || imgElement.width;
          canvas.height = imgElement.naturalHeight || imgElement.height;
          
          // 将图片绘制到 Canvas
          ctx.drawImage(imgElement, 0, 0);
          
          // 将 Canvas 转换为 Base64 字符串
          const base64String = canvas.toDataURL('image/png');
          console.log('图片已成功转换为 Base64');
          
          resolve(base64String);
        } catch (error) {
          console.error('转换图片为 Base64 时出错:', error);
          reject(error);
        }
      }
    } catch (error) {
      console.error('处理图片时出错:', error);
      reject(error);
    }
  });
}

/**
 * 使用本地识别逻辑识别验证码
 * @param {HTMLImageElement} captchaImg - 验证码图片元素
 * @returns {Promise<Object>} - 识别结果，包含 text 和 confidence
 */
async function recognizeCaptchaLocally(captchaImg) {
  try {
    console.log('开始使用本地识别逻辑...');
    
    // 创建 Canvas 元素
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // 设置 Canvas 尺寸与图片一致
    canvas.width = captchaImg.naturalWidth || captchaImg.width;
    canvas.height = captchaImg.naturalHeight || captchaImg.height;
    
    // 将图片绘制到 Canvas
    ctx.drawImage(captchaImg, 0, 0);
    
    // 检查 simpleOCR 函数是否可用
    if (typeof window.simpleOCR !== 'function') {
      console.error('本地识别函数 simpleOCR 不可用');
      return { text: '', confidence: 0, method: 'local_failed' };
    }
    
    // 使用本地识别函数
    const result = window.simpleOCR(canvas);
    
    // 验证结果是否为有效的数字
    if (result && /^\d+$/.test(result) && result.length >= 4 && result.length <= 6) {
      console.log('本地识别成功:', result);
      return { text: result, confidence: 0.8, method: 'local' };
    } else {
      console.log('本地识别结果无效:', result);
      return { text: '', confidence: 0, method: 'local_invalid' };
    }
  } catch (error) {
    console.error('本地识别出错:', error);
    return { text: '', confidence: 0, method: 'local_error' };
  }
}

/**
 * 调用 OCR 服务识别验证码
 * @param {string} base64Image - Base64 编码的图片数据
 * @returns {Promise<Object>} - 识别结果，包含 text 和 confidence
 */
async function recognizeCaptchaWithAPI(base64Image) {
  try {
    console.log('开始调用 OCR 服务...');
    
    // 使用限流器发送请求
    const result = await rateLimiter.addRequest(async () => {
      console.log('发送 OCR 请求...');
      
      // 动态获取OCR服务URL
      const ocrServiceURL = await getOCRServiceURL();
      console.log(`使用OCR服务URL: ${ocrServiceURL}`);
      
      const response = await fetch(ocrServiceURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData: base64Image
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OCR 服务响应错误 (${response.status}): ${errorData.error?.message || response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(`OCR 识别失败: ${data.error?.message || '未知错误'}`);
      }
      
      console.log('OCR 识别成功:', data.data);
      return data.data;
    });
    
    // 过滤结果，只保留数字
    if (result && result.text) {
      const filteredText = result.text.replace(/[^0-9]/g, '');
      console.log('过滤后的OCR结果:', filteredText);
      
      return { 
        text: filteredText, 
        confidence: result.confidence,
        method: 'baidu_ocr'
      };
    }
    
    return { ...result, method: 'baidu_ocr' };
  } catch (error) {
    console.error('调用 OCR 服务失败:', error);
    throw error;
  }
}

/**
 * 处理验证码图片并进行识别
 * @param {HTMLImageElement} captchaImg - 验证码图片元素
 * @returns {Promise<string>} - 识别出的验证码文本
 */
async function processCaptcha(captchaImg) {
  try {
    if (!captchaImg || !(captchaImg instanceof HTMLImageElement)) {
      throw new Error('无效的验证码图片元素');
    }
    
    console.log('处理验证码图片，直接使用百度OCR...');
    alert(JSON.stringify(captchaImg))
    // 将图片转换为 Base64
    const base64Image = await imageToBase64(captchaImg);
    console.log(base64Image)

    // 直接调用 OCR 服务识别验证码
    const apiResult = await recognizeCaptchaWithAPI(base64Image);
    
    // 验证结果是否为有效的数字
    if (apiResult.text && /^\d+$/.test(apiResult.text) && apiResult.text.length >= 4 && apiResult.text.length <= 6) {
      console.log('使用百度OCR识别结果:', apiResult.text);
      return apiResult.text;
    } else {
      console.log('百度OCR识别结果无效:', apiResult.text);
      return '';
    }
  } catch (error) {
    console.error('处理验证码失败:', error);
    throw error;
  }
}

// 导出模块函数
window.ocrHelper = {
  processCaptcha
};

// 导出为全局变量，方便在 content.js 中使用
window.processCaptcha = processCaptcha; 
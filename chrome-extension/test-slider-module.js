/**
 * 滑块验证码处理测试工具
 * 用于调试滑块验证码处理功能
 */

// 创建测试UI
function createTestUI() {
  // 创建测试面板
  const panel = document.createElement('div');
  panel.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    width: 300px;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 10px;
    border-radius: 5px;
    font-family: Arial, sans-serif;
    font-size: 12px;
    z-index: 9999999;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    max-height: 90vh;
    overflow-y: auto;
  `;
  
  // 添加标题
  const title = document.createElement('h3');
  title.textContent = '滑块验证码测试工具';
  title.style.margin = '0 0 10px 0';
  panel.appendChild(title);
  
  // 添加状态显示
  const status = document.createElement('div');
  status.id = 'slider-test-status';
  status.style.cssText = `
    margin-bottom: 10px;
    padding: 5px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
  `;
  status.textContent = '准备就绪';
  panel.appendChild(status);
  
  // 添加按钮容器
  const buttonContainer = document.createElement('div');
  buttonContainer.style.display = 'flex';
  buttonContainer.style.flexDirection = 'column';
  buttonContainer.style.gap = '5px';
  panel.appendChild(buttonContainer);
  
  // 添加测试按钮
  const buttons = [
    { id: 'check-module', text: '检查模块加载状态' },
    { id: 'detect-slider', text: '检测滑块验证码' },
    { id: 'solve-slider', text: '解决滑块验证码' },
    { id: 'test-drag', text: '测试拖动功能' },
    { id: 'close-panel', text: '关闭测试面板' }
  ];
  
  buttons.forEach(btn => {
    const button = document.createElement('button');
    button.id = btn.id;
    button.textContent = btn.text;
    button.style.cssText = `
      padding: 5px;
      margin-bottom: 5px;
      cursor: pointer;
      background: #2c3e50;
      border: none;
      border-radius: 3px;
      color: white;
    `;
    button.onmouseover = () => {
      button.style.background = '#34495e';
    };
    button.onmouseout = () => {
      button.style.background = '#2c3e50';
    };
    buttonContainer.appendChild(button);
  });
  
  // 添加日志区域
  const logContainer = document.createElement('div');
  logContainer.style.cssText = `
    margin-top: 10px;
    max-height: 300px;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.3);
    padding: 5px;
    border-radius: 3px;
    font-family: monospace;
    white-space: pre-wrap;
  `;
  logContainer.id = 'slider-test-log';
  panel.appendChild(logContainer);
  
  // 添加到页面
  document.body.appendChild(panel);
  
  // 添加事件监听器
  document.getElementById('check-module').addEventListener('click', checkModuleStatus);
  document.getElementById('detect-slider').addEventListener('click', detectSliderCaptchaTest);
  document.getElementById('solve-slider').addEventListener('click', solveSliderCaptchaTest);
  document.getElementById('test-drag').addEventListener('click', testDragFunction);
  document.getElementById('close-panel').addEventListener('click', () => panel.remove());
  
  return panel;
}

// 更新状态
function updateStatus(message, isError = false) {
  const status = document.getElementById('slider-test-status');
  if (status) {
    status.textContent = message;
    status.style.color = isError ? '#ff5252' : '#4caf50';
  }
}

// 添加日志
function log(message, isError = false) {
  const logContainer = document.getElementById('slider-test-log');
  if (logContainer) {
    const logEntry = document.createElement('div');
    logEntry.style.color = isError ? '#ff5252' : '#fff';
    logEntry.style.borderBottom = '1px solid rgba(255,255,255,0.1)';
    logEntry.style.padding = '2px 0';
    
    // 添加时间戳
    const timestamp = new Date().toTimeString().split(' ')[0];
    logEntry.textContent = `[${timestamp}] ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
  }
  
  // 同时输出到控制台
  if (isError) {
    console.error(message);
  } else {
    console.log(message);
  }
}

// 检查模块加载状态
async function checkModuleStatus() {
  log('检查滑块验证码模块加载状态...');
  updateStatus('检查中...');
  
  try {
    // 检查关键函数是否存在
    const functions = [
      'solveSliderCaptcha',
      'detectSliderCaptcha',
      'analyzeSliderStructure',
      'simulateHumanDrag'
    ];
    
    let allAvailable = true;
    functions.forEach(func => {
      const isAvailable = typeof window[func] === 'function';
      log(`函数 ${func}: ${isAvailable ? '可用' : '不可用'}`);
      if (!isAvailable) allAvailable = false;
    });
    
    // 尝试调用自检函数
    if (typeof window.checkSliderCaptchaModule === 'function') {
      const checkResult = window.checkSliderCaptchaModule();
      log(`模块自检结果: ${checkResult ? '通过' : '失败'}`);
    } else {
      log('模块自检函数不可用', true);
      allAvailable = false;
    }
    
    if (allAvailable) {
      updateStatus('模块加载正常');
    } else {
      updateStatus('模块加载异常', true);
      
      // 尝试手动加载模块
      log('尝试手动加载模块...');
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL('sliderCaptcha.js');
      script.onload = () => {
        log('模块手动加载完成，请重新检查状态');
      };
      document.head.appendChild(script);
    }
  } catch (error) {
    log(`检查模块状态时出错: ${error.message}`, true);
    updateStatus('检查失败', true);
  }
}

// 测试检测滑块验证码
async function detectSliderCaptchaTest() {
  log('测试检测滑块验证码...');
  updateStatus('检测中...');
  
  try {
    if (typeof window.detectSliderCaptcha !== 'function') {
      log('detectSliderCaptcha函数不可用', true);
      updateStatus('检测失败: 函数不可用', true);
      return;
    }
    
    const elements = window.detectSliderCaptcha();
    log(`检测到 ${elements.length} 个可能的滑块验证码元素`);
    
    if (elements.length > 0) {
      // 分析滑块结构
      if (typeof window.analyzeSliderStructure === 'function') {
        const { slider, background, container } = window.analyzeSliderStructure(elements);
        log(`分析结果: 滑块=${!!slider}, 背景=${!!background}, 容器=${!!container}`);
        
        // 高亮显示找到的元素
        if (slider) highlightElement(slider, 'red');
        if (background) highlightElement(background, 'blue');
        if (container) highlightElement(container, 'green');
        
        updateStatus('检测完成: 找到滑块验证码');
      } else {
        log('analyzeSliderStructure函数不可用', true);
        updateStatus('检测部分完成', true);
      }
    } else {
      updateStatus('未检测到滑块验证码');
    }
  } catch (error) {
    log(`检测滑块验证码时出错: ${error.message}`, true);
    updateStatus('检测失败', true);
  }
}

// 测试解决滑块验证码
async function solveSliderCaptchaTest() {
  log('测试解决滑块验证码...');
  updateStatus('解决中...');
  
  try {
    if (typeof window.solveSliderCaptcha !== 'function') {
      log('solveSliderCaptcha函数不可用', true);
      updateStatus('解决失败: 函数不可用', true);
      return;
    }
    
    const result = await window.solveSliderCaptcha();
    log(`解决结果: ${result ? '成功' : '失败'}`);
    updateStatus(`解决${result ? '成功' : '失败'}`);
  } catch (error) {
    log(`解决滑块验证码时出错: ${error.message}`, true);
    updateStatus('解决失败', true);
  }
}

// 测试拖动功能
async function testDragFunction() {
  log('测试拖动功能...');
  updateStatus('测试中...');
  
  try {
    if (typeof window.simulateHumanDrag !== 'function') {
      log('simulateHumanDrag函数不可用', true);
      updateStatus('测试失败: 函数不可用', true);
      return;
    }
    
    // 检测滑块元素
    if (typeof window.detectSliderCaptcha !== 'function' || 
        typeof window.analyzeSliderStructure !== 'function') {
      log('检测函数不可用', true);
      updateStatus('测试失败: 检测函数不可用', true);
      return;
    }
    
    const elements = window.detectSliderCaptcha();
    if (elements.length === 0) {
      log('未检测到滑块元素', true);
      updateStatus('测试失败: 未检测到滑块', true);
      return;
    }
    
    const { slider, container } = window.analyzeSliderStructure(elements);
    if (!slider) {
      log('未找到滑块元素', true);
      updateStatus('测试失败: 未找到滑块', true);
      return;
    }
    
    // 计算拖动距离
    let distance = 150; // 默认距离
    if (container) {
      const containerWidth = container.getBoundingClientRect().width;
      distance = containerWidth * 0.7;
      log(`使用容器宽度计算的距离: ${distance}px`);
    } else {
      log('未找到容器，使用默认距离: 150px');
    }
    
    // 执行拖动
    log(`开始拖动滑块，距离: ${distance}px`);
    await window.simulateHumanDrag(slider, distance);
    log('拖动完成');
    updateStatus('拖动测试完成');
  } catch (error) {
    log(`拖动测试时出错: ${error.message}`, true);
    updateStatus('拖动测试失败', true);
  }
}

// 高亮显示元素
function highlightElement(element, color = 'red') {
  const originalOutline = element.style.outline;
  const originalZIndex = element.style.zIndex;
  
  element.style.outline = `2px solid ${color}`;
  element.style.zIndex = '9999';
  
  // 5秒后恢复
  setTimeout(() => {
    element.style.outline = originalOutline;
    element.style.zIndex = originalZIndex;
  }, 5000);
  
  log(`已高亮显示元素 (${element.tagName.toLowerCase()}) - 颜色: ${color}`);
}

// 初始化测试工具
function initTestTool() {
  console.log('初始化滑块验证码测试工具...');
  
  // 检查是否已存在测试面板
  if (document.getElementById('slider-test-status')) {
    console.log('测试面板已存在');
    return;
  }
  
  // 创建测试UI
  createTestUI();
  console.log('滑块验证码测试工具已初始化');
}

// 执行初始化
initTestTool(); 
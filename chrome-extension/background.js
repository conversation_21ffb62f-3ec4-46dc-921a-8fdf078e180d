// chrome-extension/background.js

// 生成唯一ID的函数
function generateUniqueId() {
  return 'ext_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// 收集所有可能的IP地址
async function collectAllIps() {
  return new Promise((resolve) => {
    const foundIps = new Set();
    const pcs = [];
    
    // 创建多个连接以增加发现几率
    for (let i = 0; i < 3; i++) {
      const pc = new RTCPeerConnection({ iceServers: [] });
      pc.createDataChannel('');
      
      pc.onicecandidate = (event) => {
        if (event.candidate) {
          const ipMatch = /([0-9]{1,3}(\.[0-9]{1,3}){3})/.exec(event.candidate.candidate);
          if (ipMatch) {
            foundIps.add(ipMatch[1]);
          }
        }
      };
      
      pc.createOffer()
        .then(offer => pc.setLocalDescription(offer))
        .catch(err => console.error('创建offer失败:', err));
      
      pcs.push(pc);
    }
    
    // 设置超时，收集足够的IP后返回结果
    setTimeout(() => {
      // 关闭所有连接
      pcs.forEach(pc => pc.close());
      
      // 转换Set为数组并返回
      const ips = Array.from(foundIps);
      console.log('收集到的所有IP:', ips);
      resolve(ips);
    }, 3000); // 增加等待时间，确保收集到足够的候选项
  });
}

// 获取详细的IP信息（内网IP和外网IP）
async function getDetailedIpInfo() {
  try {
    // 使用WebRTC获取IP
    const ips = await collectAllIps();
    
    // 分类IP
    const internalIps = ips.filter(ip => 
      ip.startsWith('192.168.') || 
      ip.startsWith('10.') || 
      (ip.startsWith('172.') && parseInt(ip.split('.')[1]) >= 16 && parseInt(ip.split('.')[1]) <= 31)
    );
    
    const externalIps = ips.filter(ip => 
      !ip.startsWith('192.168.') && 
      !ip.startsWith('10.') && 
      !(ip.startsWith('172.') && parseInt(ip.split('.')[1]) >= 16 && parseInt(ip.split('.')[1]) <= 31) &&
      ip !== '127.0.0.1' && 
      ip !== '::1'
    );
    
    console.log('内网IP列表:', internalIps);
    console.log('外网IP列表:', externalIps);
    
    // 优先选择192.168开头的内网IP
    let bestInternalIp = internalIps.find(ip => ip.startsWith('192.168.'));
    
    // 如果没有192.168开头的IP，使用其他内网IP
    if (!bestInternalIp && internalIps.length > 0) {
      bestInternalIp = internalIps[0];
    } else if (!bestInternalIp) {
      bestInternalIp = 'unknown';
    }
    
    // 如果没有找到外网IP，使用外部服务获取
    let bestExternalIp = externalIps.length > 0 ? externalIps[0] : await tryExternalServices();
    
    return {
      internalIp: bestInternalIp,
      externalIp: bestExternalIp,
      allIps: ips
    };
  } catch (error) {
    console.error('获取详细IP信息失败:', error);
    const externalIp = await tryExternalServices();
    return {
      internalIp: 'unknown',
      externalIp: externalIp,
      allIps: [externalIp]
    };
  }
}

// 尝试使用外部服务获取IP
async function tryExternalServices() {
  // 方法2: 使用外部服务获取IP
  try {
    const response = await fetch('https://api.ipify.org?format=json');
    if (response.ok) {
      const data = await response.json();
      console.log('通过ipify获取到IP:', data.ip);
      return data.ip;
    }
  } catch (error) {
    console.log('通过ipify获取IP失败:', error);
  }
  
  // 方法3: 备用服务
  try {
    const response = await fetch('https://api.ip.sb/ip');
    if (response.ok) {
      const ip = await response.text();
      console.log('通过ip.sb获取到IP:', ip.trim());
      return ip.trim();
    }
  } catch (error) {
    console.log('通过ip.sb获取IP失败:', error);
  }
  
  // 方法4: 再一个备用服务
  try {
    const response = await fetch('https://ifconfig.me/ip');
    if (response.ok) {
      const ip = await response.text();
      console.log('通过ifconfig.me获取到IP:', ip.trim());
      return ip.trim();
    }
  } catch (error) {
    console.log('通过ifconfig.me获取IP失败:', error);
  }
  
  return 'unknown';
}

// 确保插件ID存在于存储中
async function ensurePluginIdExists() {
  try {
    const result = await chrome.storage.local.get('pluginId');
    if (!result.pluginId) {
      const newId = generateUniqueId();
      await chrome.storage.local.set({ pluginId: newId });
      console.log('已生成并存储新的插件ID:', newId);
      return newId;
    } else {
      console.log('已存在的插件ID:', result.pluginId);
      return result.pluginId;
    }
  } catch (error) {
    console.error('存储插件ID时出错:', error);
    return null;
  }
}

// 注册相关逻辑已移除

// 设置标签页更新监听
function setupTabUpdateListener() {
  try {
    // 监听标签页更新事件
    const listener = (tabId, changeInfo, tab) => {
      // 检查是否是登录页面，尝试自动登录
      if (changeInfo.status === 'complete' && tab.url) {
        console.log('标签页加载完成，检查是否需要自动登录:', tab.url);
        checkAndAutoLogin(tab.url, tabId);
      }
    };
    
    // 添加监听器
    chrome.tabs.onUpdated.addListener(listener);
    console.log('标签页更新监听已设置');
  } catch (error) {
    console.error('设置标签页更新监听失败:', error);
  }
}

// 定期注册逻辑已移除

// 初始化插件
function initializeExtension() {
  console.log('初始化Chrome插件...');
  
  // 确保插件ID存在
  ensurePluginIdExists().then(pluginId => {
    console.log('插件ID:', pluginId);
  });
  
  // 设置标签页更新监听
  setupTabUpdateListener();
}

// 在插件启动时初始化
initializeExtension();

// 监听来自外部（我们的Next.js应用）的消息
chrome.runtime.onMessageExternal.addListener((request, sender, sendResponse) => {
  console.log('收到来自网页的消息:', request);

  // 检查消息类型是否为"自动登录"
  if (request.type === 'AUTO_LOGIN') {
    const { loginUrl, username, password, usernameXpath, passwordXpath, captchaXpath, loginButtonXpath } = request.data;

    // 1. 创建一个新的标签页并导航到登录URL
    chrome.tabs.create({ url: loginUrl }, (newTab) => {
      // 2. 监听标签页更新，确保页面加载完成后再注入脚本
      const listener = (tabId, changeInfo, tab) => {
        if (tabId === newTab.id && changeInfo.status === 'complete') {
          // 移除监听器，避免重复执行
          chrome.tabs.onUpdated.removeListener(listener);

          // 3. 注入内容脚本来执行登录操作
          chrome.scripting.executeScript({
            target: { tabId: newTab.id },
            files: ['content.js']
          }).then(() => {
            console.log('内容脚本已注入，准备发送登录数据。');
            // 4. 发送真正的登录数据给刚刚注入的content.js
            chrome.tabs.sendMessage(newTab.id, {
              type: 'EXECUTE_LOGIN',
              data: request.data
            });
          }).catch(err => console.error('脚本注入失败:', err));
        }
      };
      chrome.tabs.onUpdated.addListener(listener);
    });

    // 异步响应，表示我们会稍后发送响应
    sendResponse({ success: true, message: '登录请求已收到，正在处理...' });
    return true; 
  }

  // 对其他类型的消息不作处理
  sendResponse({ success: false, message: '未知的消息类型' });
  return false;
});

// 检查URL并尝试自动登录
async function checkAndAutoLogin(url, tabId) {
  try {
    console.log(`检查URL是否需要自动登录: ${url}`);
    
    // 1. 首先检查是否为目标域名
    if (!shouldAutoLogin(url)) {
      console.log('URL不在自动登录域名列表中，跳过自动登录');
      return;
    }
    
    // 2. 根据环境标识智能选择服务器
    const serverUrl = await getServerUrl();
    console.log(`使用服务器: ${serverUrl}`);
    
    try {
      // 3. 构建API URL并发送请求
      const apiUrl = `${serverUrl}/api/account/findByLoginUrl`;
      
      console.log(`发送API请求到: ${apiUrl}`);
      console.log(`请求数据:`, { loginUrl: url });
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': chrome.runtime.getURL('')
        },
        body: JSON.stringify({ loginUrl: url })
      });
      
      console.log(`API响应状态码: ${response.status}`);
      console.log(`API响应状态文本: ${response.statusText}`);
      console.log(`API响应头:`, Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        console.error(`API请求失败，状态码: ${response.status}`);
        // 尝试读取错误响应内容
        try {
          const errorText = await response.text();
          console.error(`错误响应内容:`, errorText);
        } catch (e) {
          console.error(`无法读取错误响应:`, e);
        }
        return;
      }
      
      // 先获取原始文本响应
      const responseText = await response.text();
      console.log(`原始响应内容:`, responseText);
      
      // 尝试解析为JSON
      let data;
      try {
        data = JSON.parse(responseText);
        console.log(`解析后的JSON数据:`, data);
      } catch (e) {
        console.error(`JSON解析失败:`, e);
        console.error(`响应不是有效的JSON格式`);
        return;
      }
      
      // 4. 如果找到匹配的账号，执行自动登录
      if (data.success && data.account) {
        console.log(`找到匹配的账号: ${data.account.name}，准备自动登录`);
        
        // 保存成功的服务器URL
        await chrome.storage.local.set({ lastSuccessfulServerUrl: serverUrl });
        
        // 执行自动登录
        await autoLogin(tabId, data.account);
      } else {
        console.log('未找到匹配的账号');
      }
    } catch (apiError) {
      console.error(`API请求失败:`, apiError);
    }
  } catch (error) {
    console.error('检查自动登录失败:', error);
  }
}

// 检查是否应该对该URL执行自动登录
function shouldAutoLogin(url) {
  try {
    // 获取配置的域名列表，默认为指定的两个域名
    const targetDomains = [
      'user.lotsmall.cn',
      'wap.lotsmall.cn'
    ];
    
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    
    // 检查是否包含目标域名
    return targetDomains.some(domain => hostname.includes(domain));
  } catch (error) {
    console.error('检查域名失败:', error);
    return false;
  }
}

// 根据环境标识智能选择服务器URL
async function getServerUrl() {
  try {
    // 从存储中获取环境配置
    const result = await chrome.storage.local.get(['environment', 'lastSuccessfulServerUrl']);
    
    // 如果有上次成功的服务器URL，优先使用
    if (result.lastSuccessfulServerUrl) {
      console.log(`使用上次成功的服务器: ${result.lastSuccessfulServerUrl}`);
      return result.lastSuccessfulServerUrl;
    }
    
    // 根据环境标识选择服务器
    const environment = result.environment || 'auto';
    
    if (environment === 'prod' || environment === 'production') {
      return 'http://***************';
    } else if (environment === 'test' || environment === 'testing') {
      return 'http://**************';
    } else {
      // 自动检测环境（默认行为）
      return await autoDetectServerUrl();
    }
  } catch (error) {
    console.error('获取服务器URL失败:', error);
    return 'http://***************'; // 默认使用生产环境
  }
}

// 自动检测可用的服务器URL
async function autoDetectServerUrl() {
  const serverUrls = [
    'http://***************',  // 生产环境优先
    'http://**************'    // 测试环境
  ];
  
  for (const serverUrl of serverUrls) {
    try {
      console.log(`检测服务器可用性: ${serverUrl}`);
      
      // 发送一个简单的健康检查请求
      const healthUrl = `${serverUrl}/api/health`;
      console.log(`健康检查URL: ${healthUrl}`);
      
      const response = await fetch(healthUrl, {
        method: 'GET',
        timeout: 3000 // 3秒超时
      });
      
      console.log(`健康检查响应状态: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        // 读取响应内容
        const healthText = await response.text();
        console.log(`健康检查响应内容: ${healthText}`);
        
        console.log(`服务器可用: ${serverUrl}`);
        // 保存检测到的可用服务器
        await chrome.storage.local.set({ lastSuccessfulServerUrl: serverUrl });
        return serverUrl;
      } else {
        const errorText = await response.text();
        console.log(`健康检查失败响应: ${errorText}`);
      }
    } catch (error) {
      console.log(`服务器不可用: ${serverUrl}`, error.message);
    }
  }
  
  // 如果都不可用，返回默认的生产环境
  console.log('所有服务器都不可用，使用默认服务器');
  return 'http://***************';
}

// 执行自动登录
async function autoLogin(tabId, account) {
  try {
    console.log(`开始自动登录: ${account.name}`);
    
    // 首先注入content.js脚本
    await chrome.scripting.executeScript({
      target: { tabId },
      files: ['content.js']
    });
    
    console.log('content.js已注入，准备发送登录数据');
    
    // 等待一下确保脚本加载完成
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 使用content.js中的高级自动填充逻辑
    const loginData = {
      username: account.username || '',
      password: account.encrypted_password || '',
      usernameXpath: account.username_xpath || '//input[@type="text" or @type="email" or contains(@name,"user") or contains(@name,"email") or contains(@name,"account")]',
      passwordXpath: account.password_xpath || '//input[@type="password"]',
      captchaXpath: account.captcha_xpath || '//img[contains(@src,"captcha") or contains(@src,"code") or contains(@alt,"验证码")]',
      captchaInputXpath: account.captcha_input_xpath || '//input[contains(@name,"captcha") or contains(@name,"code") or contains(@placeholder,"验证码")]',
      loginButtonXpath: account.login_button_xpath || '//button[@type="submit"] | //input[@type="submit"] | //button[contains(text(),"登录")] | //button[contains(text(),"登陆")] | //a[contains(text(),"登录")]'
    };
    
    console.log('发送登录数据到content.js:', {
      name: account.name,
      username: loginData.username,
      hasPassword: !!loginData.password,
      usernameXpath: loginData.usernameXpath,
      passwordXpath: loginData.passwordXpath,
      captchaXpath: loginData.captchaXpath,
      captchaInputXpath: loginData.captchaInputXpath,
      loginButtonXpath: loginData.loginButtonXpath
    });
    
    // 发送登录命令到content.js
    const response = await chrome.tabs.sendMessage(tabId, {
      type: 'EXECUTE_LOGIN',
      data: loginData
    });
    
    console.log('自动登录响应:', response);
    
    // 等待短暂时间后刷新
    setTimeout(() => {
      console.log(`[Action] 1.5秒延迟结束，正在对 Tab: ${tabId} 执行强制刷新...`);
      chrome.tabs.reload(tabId);
    }, 1500); // 等待1.5秒
    return; // 阻止后续代码执行，等待刷新
  } catch (error) {
    console.error('执行自动登录失败:', error);
  }
}
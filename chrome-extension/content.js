// chrome-extension/content.js
console.log('自动登录内容脚本已加载（v5.3 - 增强元素查找和验证码处理，支持滑块验证码）。');

// Helper function to find elements, with added error handling and multiple methods.
const getElementByXPath = (xpath) => {
  if (!xpath) return null;
  try {
    return document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
  } catch (e) {
    console.error(`无效的 XPath 表达式: "${xpath}"`, e);
    return null;
  }
};

// 新增：多种方式查找元素
const findElement = (selectors) => {
  // 如果传入的是字符串，则视为XPath
  if (typeof selectors === 'string') {
    return getElementByXPath(selectors);
  }
  
  // 如果传入的是对象，则尝试多种查找方式
  if (selectors && typeof selectors === 'object') {
    // 1. 尝试XPath
    if (selectors.xpath) {
      const element = getElementByXPath(selectors.xpath);
      if (element) return element;
    }
    
    // 2. 尝试CSS选择器
    if (selectors.css) {
      try {
        const element = document.querySelector(selectors.css);
        if (element) return element;
      } catch (e) {
        console.error(`无效的 CSS 选择器: "${selectors.css}"`, e);
      }
    }
    
    // 3. 尝试ID
    if (selectors.id) {
      const element = document.getElementById(selectors.id);
      if (element) return element;
    }
    
    // 4. 尝试name属性
    if (selectors.name) {
      const elements = document.getElementsByName(selectors.name);
      if (elements && elements.length > 0) return elements[0];
    }
    
    // 5. 尝试通用选择器（如input[type="text"]等）
    if (selectors.generic) {
      try {
        const elements = document.querySelectorAll(selectors.generic);
        if (elements && elements.length > 0) {
          // 对于用户名，通常是第一个文本输入框
          if (selectors.isUsername && elements.length > 1) {
            return elements[0];
          }
          // 对于密码，通常是type="password"的输入框
          if (selectors.isPassword) {
            for (let i = 0; i < elements.length; i++) {
              if (elements[i].type === 'password') return elements[i];
            }
          }
          return elements[0];
        }
      } catch (e) {
        console.error(`通用选择器查找失败: "${selectors.generic}"`, e);
      }
    }
  }
  
  return null;
};

// 生成备用验证码（纯数字）
function generateFallbackCaptcha() {
  const length = 5; // 默认长度
  let result = '';
  for (let i = 0; i < length; i++) {
    result += Math.floor(Math.random() * 10);
  }
  console.log(`生成备用验证码: ${result}`);
  return result;
}

// 新增：填充输入框并触发事件
const fillInputField = (element, value) => {
  if (!element) return false;
  
  try {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  } catch (e) {
    console.error('填充输入框失败:', e);
    return false;
  }
};

// 滑块验证码处理模块加载状态
let sliderCaptchaModuleLoaded = false;
let sliderCaptchaLoadAttempts = 0;

// 设置标志，禁用滑块验证功能
const DISABLE_SLIDER_CAPTCHA = true; // 设置为true表示禁用滑块验证功能

/**
 * 确保滑块验证码处理模块已加载
 * @returns {Promise<boolean>} - 是否成功加载模块
 */
async function ensureSliderCaptchaModuleLoaded() {
  if (DISABLE_SLIDER_CAPTCHA) {
    console.log('滑块验证功能已禁用，跳过加载滑块验证模块');
    return false;
  }
  
  // 原有逻辑
  const MAX_SLIDER_MODULE_LOAD_ATTEMPTS = 3;
  
  try {
    // 如果已经加载，直接返回
    if (sliderCaptchaModuleLoaded) {
      return true;
    }
    
    // 如果尝试次数过多，放弃加载
    if (sliderCaptchaLoadAttempts >= MAX_SLIDER_MODULE_LOAD_ATTEMPTS) {
      console.error(`已尝试加载滑块验证码处理模块 ${MAX_SLIDER_MODULE_LOAD_ATTEMPTS} 次，放弃加载`);
      return false;
    }
    
    sliderCaptchaLoadAttempts++;
    console.log(`尝试加载滑块验证码处理模块 (第 ${sliderCaptchaLoadAttempts}/${MAX_SLIDER_MODULE_LOAD_ATTEMPTS} 次)`);
    
    // 检查是否已经加载
    if (typeof solveSliderCaptcha === 'function') {
      console.log('滑块验证码处理模块已加载');
      sliderCaptchaModuleLoaded = true;
      return true;
    }
    
    // 等待一段时间，确保sliderCaptcha.js有足够时间加载
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 再次检查
    if (typeof solveSliderCaptcha === 'function') {
      console.log('滑块验证码处理模块已加载（延迟检测）');
      sliderCaptchaModuleLoaded = true;
      return true;
    }
    
    // 尝试手动加载脚本
    console.log('尝试手动加载滑块验证码处理模块...');
    
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('sliderCaptcha.js');
    script.onload = () => {
      console.log('滑块验证码处理模块加载成功');
      
      if (typeof solveSliderCaptcha === 'function') {
        console.log('滑块验证码处理函数可用');
        sliderCaptchaModuleLoaded = true;
      } else {
        console.error('滑块验证码处理模块加载成功，但函数不可用');
      }
    };
    script.onerror = (error) => {
      console.error('加载滑块验证码处理模块失败:', error);
    };
    
    document.head.appendChild(script);
    
    // 等待脚本加载
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return typeof solveSliderCaptcha === 'function';
  } catch (error) {
    console.error('加载滑块验证码处理模块时出错:', error);
    return false;
  }
}

/**
 * 使用Canvas API分析图像，检测拼图块和计算距离
 * @returns {Promise<number>} - 返回计算出的距离，如果失败则返回null
 */
async function detectPuzzlePiecesAndDistance() {
  try {
    console.log('开始使用高级图像分析方法检测拼图块...');
    
    // 查找puzzle容器
    const puzzleContainer = document.getElementById('puzzle');
    if (!puzzleContainer) {
      console.log('未找到puzzle容器');
      return null;
    }
    
    // 查找背景图片
    const backgroundImage = puzzleContainer.querySelector('img:not([style*="left"])');
    if (!backgroundImage) {
      console.log('未找到背景图片');
      return null;
    }
    
    // 查找可能的拼图块图片
    const puzzlePieces = Array.from(puzzleContainer.querySelectorAll('img[style*="left"], img[style*="position"]'));
    if (puzzlePieces.length === 0) {
      console.log('未找到拼图块图片');
      return null;
    }
    
    console.log(`找到背景图片和 ${puzzlePieces.length} 个可能的拼图块图片`);
    
    // 创建Canvas元素
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // 设置Canvas大小与背景图片相同
    const bgRect = backgroundImage.getBoundingClientRect();
    canvas.width = bgRect.width;
    canvas.height = bgRect.height;
    
    // 记录图像信息
    console.log('背景图片信息:', {
      src: backgroundImage.src,
      width: bgRect.width,
      height: bgRect.height
    });
    
    // 分析拼图块位置
    const piecePositions = [];
    
    for (const piece of puzzlePieces) {
      const pieceRect = piece.getBoundingClientRect();
      const pieceStyle = window.getComputedStyle(piece);
      
      // 记录拼图块信息
      const pieceInfo = {
        src: piece.src,
        left: pieceRect.left - bgRect.left,
        top: pieceRect.top - bgRect.top,
        width: pieceRect.width,
        height: pieceRect.height,
        style: {
          left: pieceStyle.left,
          top: pieceStyle.top,
          position: pieceStyle.position
        }
      };
      
      console.log('拼图块信息:', pieceInfo);
      piecePositions.push(pieceInfo);
    }
    
    // 查找红框标记的拼图块
    const redBorderElements = puzzleContainer.querySelectorAll('[style*="border"],[style*="outline"],[style*="box-shadow"],[class*="target"],[class*="hole"]');
    
    if (redBorderElements.length > 0) {
      console.log(`找到 ${redBorderElements.length} 个可能的目标位置标记`);
      
      for (const element of redBorderElements) {
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        console.log('目标位置标记:', {
          tagName: element.tagName,
          className: element.className,
          position: `(${rect.left - bgRect.left}, ${rect.top - bgRect.top})`,
          size: `${rect.width}x${rect.height}`,
          border: style.border,
          outline: style.outline,
          boxShadow: style.boxShadow
        });
      }
    }
    
    // 如果有两个拼图块，计算它们之间的水平距离
    if (piecePositions.length >= 2) {
      // 按照left值排序
      piecePositions.sort((a, b) => a.left - b.left);
      
      // 计算距离
      const distance = piecePositions[1].left - piecePositions[0].left;
      
      console.log('拼图块距离分析结果:', {
        滑块位置: piecePositions[0].left,
        目标位置: piecePositions[1].left,
        距离: distance
      });
      
      return distance > 0 ? distance : null;
    }
    
    // 如果只有一个拼图块和一个标记，计算它们之间的距离
    if (piecePositions.length === 1 && redBorderElements.length > 0) {
      const piecePos = piecePositions[0].left;
      const targetPos = redBorderElements[0].getBoundingClientRect().left - bgRect.left;
      
      const distance = targetPos - piecePos;
      
      console.log('拼图块与标记距离:', {
        滑块位置: piecePos,
        目标位置: targetPos,
        距离: distance
      });
      
      return distance > 0 ? distance : null;
    }
    
    console.log('无法确定拼图块之间的距离');
    return null;
  } catch (error) {
    console.error('使用高级图像分析方法检测拼图块时出错:', error);
    return null;
  }
}

/**
 * 计算拼图块之间的距离
 * @returns {Promise<number>} - 返回计算出的距离，如果失败则返回null
 */
async function calculatePuzzleDistance() {
  try {
    console.log('开始计算拼图块之间的距离...');
    
    // 首先尝试使用高级图像分析方法
    const advancedDistance = await detectPuzzlePiecesAndDistance();
    if (advancedDistance !== null) {
      console.log(`使用高级图像分析方法计算的距离: ${advancedDistance}px`);
      return advancedDistance;
    }
    
    // 如果高级方法失败，使用基本方法
    console.log('高级图像分析方法失败，尝试使用基本方法...');
    
    // 查找puzzle容器
    const puzzleContainer = document.getElementById('puzzle');
    if (!puzzleContainer) {
      console.log('未找到puzzle容器');
      return null;
    }
    
    // 查找所有图片元素
    const images = puzzleContainer.querySelectorAll('img');
    console.log(`在puzzle容器中找到 ${images.length} 个图片元素`);
    
    if (images.length < 2) {
      console.log('图片元素数量不足，无法计算距离');
      return null;
    }
    
    // 记录图片元素信息
    const imageInfos = [];
    for (let i = 0; i < images.length; i++) {
      const img = images[i];
      const rect = img.getBoundingClientRect();
      
      imageInfos.push({
        element: img,
        index: i,
        rect: {
          left: rect.left,
          top: rect.top,
          width: rect.width,
          height: rect.height,
          right: rect.right,
          bottom: rect.bottom
        },
        src: img.src,
        classes: img.className,
        visible: rect.width > 0 && rect.height > 0 && window.getComputedStyle(img).display !== 'none'
      });
      
      console.log(`图片 ${i + 1} 信息:`, {
        src: img.src,
        position: `(${rect.left}, ${rect.top})`,
        size: `${rect.width}x${rect.height}`,
        visible: rect.width > 0 && rect.height > 0 && window.getComputedStyle(img).display !== 'none',
        classes: img.className
      });
    }
    
    // 筛选可见的图片元素
    const visibleImages = imageInfos.filter(info => info.visible);
    
    if (visibleImages.length < 2) {
      console.log('可见图片元素数量不足，无法计算距离');
      return null;
    }
    
    // 假设第一个图片是滑块，第二个是目标位置
    // 也可以通过其他特征（如类名、大小等）来识别
    const sliderImage = visibleImages[0];
    const targetImage = visibleImages[1];
    
    // 计算水平距离
    const distance = Math.abs(targetImage.rect.left - sliderImage.rect.left);
    
    console.log('拼图块距离计算结果:', {
      滑块位置: sliderImage.rect.left,
      目标位置: targetImage.rect.left,
      距离: distance
    });
    
    // 如果距离太小或太大，可能是识别错误
    if (distance < 10 || distance > 400) {
      console.log('计算出的距离异常，尝试其他方法');
      
      // 尝试查找特定的拼图元素
      const puzzlePieces = puzzleContainer.querySelectorAll('[class*="puzzle"], [class*="jigsaw"]');
      
      if (puzzlePieces.length >= 2) {
        console.log(`找到 ${puzzlePieces.length} 个可能的拼图块元素`);
        
        // 获取拼图块位置
        const piece1 = puzzlePieces[0].getBoundingClientRect();
        const piece2 = puzzlePieces[1].getBoundingClientRect();
        
        // 计算水平距离
        const alternativeDistance = Math.abs(piece2.left - piece1.left);
        
        console.log('使用拼图块元素计算的距离:', {
          块1位置: piece1.left,
          块2位置: piece2.left,
          距离: alternativeDistance
        });
        
        return alternativeDistance;
      }
    }
    
    return distance;
  } catch (error) {
    console.error('计算拼图块距离时出错:', error);
    return null;
  }
}

/**
 * 测试拼图块距离计算函数，可以从控制台直接调用
 * @returns {Promise<void>}
 */
async function testPuzzleDistanceCalculation() {
  console.log('开始测试拼图块距离计算...');
  
  try {
    // 1. 使用高级图像分析方法
    console.log('=== 方法1: 高级图像分析 ===');
    const advancedDistance = await detectPuzzlePiecesAndDistance();
    console.log(`高级图像分析结果: ${advancedDistance !== null ? advancedDistance + 'px' : '计算失败'}`);
    
    // 2. 使用基本方法
    console.log('=== 方法2: 基本图片元素分析 ===');
    
    // 查找puzzle容器
    const puzzleContainer = document.getElementById('puzzle');
    if (!puzzleContainer) {
      console.log('未找到puzzle容器');
    } else {
      // 查找所有图片元素
      const images = puzzleContainer.querySelectorAll('img');
      console.log(`在puzzle容器中找到 ${images.length} 个图片元素`);
      
      if (images.length >= 2) {
        const image1 = images[0];
        const image2 = images[1];
        
        const rect1 = image1.getBoundingClientRect();
        const rect2 = image2.getBoundingClientRect();
        
        console.log('图片1信息:', {
          src: image1.src,
          position: `(${rect1.left}, ${rect1.top})`,
          size: `${rect1.width}x${rect1.height}`
        });
        
        console.log('图片2信息:', {
          src: image2.src,
          position: `(${rect2.left}, ${rect2.top})`,
          size: `${rect2.width}x${rect2.height}`
        });
        
        const distance = Math.abs(rect2.left - rect1.left);
        console.log(`基本计算结果: 图片间距离 = ${distance}px`);
      } else {
        console.log('图片元素数量不足，无法计算距离');
      }
    }
    
    // 3. 查找特定的拼图元素
    console.log('=== 方法3: 特定拼图元素分析 ===');
    
    // 查找可能的拼图元素
    const puzzleElements = document.querySelectorAll('[class*="puzzle"], [class*="jigsaw"], [id*="puzzle"]');
    console.log(`找到 ${puzzleElements.length} 个可能的拼图相关元素`);
    
    // 查找带有红框或特殊标记的元素
    const markedElements = document.querySelectorAll('[style*="border"],[style*="outline"],[style*="box-shadow"],[class*="target"],[class*="hole"]');
    console.log(`找到 ${markedElements.length} 个可能的标记元素`);
    
    // 4. 综合计算
    console.log('=== 综合计算结果 ===');
    const finalDistance = await calculatePuzzleDistance();
    console.log(`综合计算的拼图块距离: ${finalDistance !== null ? finalDistance + 'px' : '计算失败'}`);
    
    // 将结果添加到页面上，方便查看
    const resultDiv = document.createElement('div');
    resultDiv.style.position = 'fixed';
    resultDiv.style.top = '10px';
    resultDiv.style.right = '10px';
    resultDiv.style.padding = '10px';
    resultDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    resultDiv.style.color = '#fff';
    resultDiv.style.zIndex = '9999';
    resultDiv.style.borderRadius = '5px';
    resultDiv.style.maxWidth = '300px';
    
    resultDiv.innerHTML = `
      <h3 style="margin: 0 0 10px 0;">拼图块距离分析结果</h3>
      <p>高级分析: ${advancedDistance !== null ? advancedDistance + 'px' : '计算失败'}</p>
      <p>综合结果: ${finalDistance !== null ? finalDistance + 'px' : '计算失败'}</p>
      <p style="font-size: 12px; margin-top: 10px;">更多详情请查看控制台输出</p>
    `;
    
    document.body.appendChild(resultDiv);
    
    // 5秒后自动移除
    setTimeout(() => {
      if (document.body.contains(resultDiv)) {
        document.body.removeChild(resultDiv);
      }
    }, 10000);
    
    return finalDistance;
  } catch (error) {
    console.error('测试拼图块距离计算时出错:', error);
    return null;
  }
}

// 将测试函数暴露到全局作用域，方便从控制台调用
window.testPuzzleDistance = testPuzzleDistanceCalculation;
console.log('拼图块距离计算测试函数已加载，可以在控制台使用 testPuzzleDistance() 进行测试');

/**
 * 处理滑块验证码
 * @returns {Promise<boolean>} - 是否成功处理
 */
async function handleSliderCaptcha() {
  if (DISABLE_SLIDER_CAPTCHA) {
    console.log('滑块验证功能已禁用，跳过处理滑块验证');
    return false;
  }
  
  console.log('开始处理滑块验证码...');
  
  try {
    // 计算拼图块之间的距离
    const calculatedDistance = await calculatePuzzleDistance();
    if (calculatedDistance) {
      console.log(`自动计算的拼图块距离: ${calculatedDistance}px`);
    }
    
    // 直接查找特定的puzzle div滑块
    console.log('尝试查找特定的puzzle div滑块...');
    const specificXPath = '//*[@id="puzzle"]/div[2]/div[2]';
    
    const xpathResult = document.evaluate(
      specificXPath, 
      document, 
      null, 
      XPathResult.FIRST_ORDERED_NODE_TYPE, 
      null
    );
    
    if (xpathResult.singleNodeValue) {
      const slider = xpathResult.singleNodeValue;
      console.log('找到特定的puzzle div滑块:', {
        tagName: slider.tagName,
        className: slider.className || '无类名',
        id: slider.id || '无ID',
        style: slider.getAttribute('style') || '无样式'
      });
      
      // 获取容器
      const container = document.getElementById('puzzle') || slider.closest('#puzzle') || slider.parentElement;
      
      // 设置滑动距离
      let distance = 108; // 默认值
      
      // 如果成功计算了拼图块距离，则使用计算结果
      if (calculatedDistance && calculatedDistance > 10 && calculatedDistance < 400) {
        distance = calculatedDistance;
        console.log(`使用计算出的拼图块距离: ${distance}px`);
      } 
      // 如果找到容器，使用容器宽度的比例
      else if (container) {
        const containerWidth = container.getBoundingClientRect().width;
        distance = Math.round(containerWidth * 0.6); // 使用60%作为估计值
        console.log(`容器宽度: ${containerWidth}px, 设置滑动距离: ${distance}px`);
      }
      
      // 直接修改滑块的left样式
      console.log('开始修改滑块left样式...');
      
      // 记录原始样式
      const originalLeft = slider.style.left || '0px';
      const originalTransition = slider.style.transition || '';
      
      console.log('滑块原始样式:', {
        left: originalLeft,
        transition: originalTransition
      });
      
      // 设置transition以实现平滑移动
      slider.style.transition = 'left 0.5s ease-in-out';
      
      // 直接设置最终位置
      slider.style.left = `${distance}px`;
      console.log(`已设置left值为 ${distance}px`);
      
      // 等待transition完成
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 检查最终位置
      console.log('滑块最终样式:', {
        left: slider.style.left,
        transition: slider.style.transition
      });
      
      console.log('滑块移动完成');
      return true;
    } else {
      console.log('未找到特定的puzzle div滑块');
      
      // 尝试查找任何包含puzzle的元素
      const puzzleElements = document.querySelectorAll('[id*="puzzle"], [class*="puzzle"]');
      console.log(`找到 ${puzzleElements.length} 个可能的puzzle元素`);
      
      if (puzzleElements.length > 0) {
        // 查找可能的滑块元素
        for (const element of puzzleElements) {
          console.log('可能的puzzle元素:', {
            tagName: element.tagName,
            className: element.className || '无类名',
            id: element.id || '无ID'
          });
          
          // 查找子元素中可能的滑块
          const possibleSliders = element.querySelectorAll('div[style*="left"], div[style*="transform"]');
          
          if (possibleSliders.length > 0) {
            const slider = possibleSliders[0];
            console.log('找到可能的滑块元素:', {
              tagName: slider.tagName,
              className: slider.className || '无类名',
              id: slider.id || '无ID',
              style: slider.getAttribute('style') || '无样式'
            });
            
            // 设置滑动距离
            let distance = 108;
            
            // 如果成功计算了拼图块距离，则使用计算结果
            if (calculatedDistance && calculatedDistance > 10 && calculatedDistance < 400) {
              distance = calculatedDistance;
              console.log(`使用计算出的拼图块距离: ${distance}px`);
            }
            
            // 直接修改滑块的left样式
            slider.style.transition = 'left 0.5s ease-in-out';
            slider.style.left = `${distance}px`;
            console.log(`已设置left值为 ${distance}px`);
            
            // 等待transition完成
            await new Promise(resolve => setTimeout(resolve, 500));
            
            console.log('滑块移动完成');
            return true;
          }
        }
      }
    }
    
    // 如果上述方法都失败，尝试使用通用方法
    if (typeof solveSliderCaptcha === 'function') {
      console.log('尝试使用通用滑块处理方法');
      return await solveSliderCaptcha();
    } else {
      console.log('未找到滑块验证码处理函数');
      return false;
    }
  } catch (error) {
    console.error('处理滑块验证码时出错:', error);
    return false;
  }
}

// Main message listener - REGISTERED IMMEDIATELY to prevent race conditions.
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.type === 'EXECUTE_LOGIN') {
    console.log('接收到执行登录命令:', request.data);
    
    // Encapsulate the entire logic in an async function for clean execution.
    const performLogin = async () => {
      // 添加重试计数器
      let captchaRetryCount = 0;
      let usernameRetryCount = 0;
      let passwordRetryCount = 0;
      const MAX_RETRY_COUNT = 3; // 最大重试次数
      
      try {
        const {
          username, password,
          usernameXpath, passwordXpath, captchaXpath, loginButtonXpath,
          captchaInputXpath
        } = request.data;

        // --- Step 1: Fill Username and Password (With Retry Logic) ---
        console.log('开始填充账号密码...');
        
        // 用户名填充重试逻辑
        let usernameField = null;
        let usernameFilled = false;
        
        while (!usernameFilled && usernameRetryCount < MAX_RETRY_COUNT) {
          // 首先尝试使用XPath
          usernameField = getElementByXPath(usernameXpath);
          
          // 如果XPath失败，尝试其他方式
          if (!usernameField) {
            console.log(`第 ${usernameRetryCount + 1} 次尝试查找用户名输入框...`);
            
            // 尝试多种查找方式
            usernameField = findElement({
              xpath: usernameXpath,
              css: 'input[type="text"], input:not([type="password"])',
              generic: 'input',
              isUsername: true
            });
            
            // 如果仍然找不到，等待一会再试
            if (!usernameField) {
              console.log('未找到用户名输入框，等待500ms后重试...');
              await new Promise(resolve => setTimeout(resolve, 500));
              usernameRetryCount++;
              continue;
            }
          }
          
          // 找到输入框后，填充用户名
          usernameFilled = fillInputField(usernameField, username);
          
          if (usernameFilled) {
            console.log('用户名已成功填充。');
            break;
          } else {
            console.log('用户名填充失败，重试...');
            usernameRetryCount++;
            await new Promise(resolve => setTimeout(resolve, 300));
          }
        }
        
        if (!usernameFilled) {
          console.error(`在 ${MAX_RETRY_COUNT} 次尝试后仍未能成功填充用户名`);
        }
        
        // 密码填充重试逻辑
        let passwordField = null;
        let passwordFilled = false;
        
        while (!passwordFilled && passwordRetryCount < MAX_RETRY_COUNT) {
          // 首先尝试使用XPath
          passwordField = getElementByXPath(passwordXpath);
          
          // 如果XPath失败，尝试其他方式
          if (!passwordField) {
            console.log(`第 ${passwordRetryCount + 1} 次尝试查找密码输入框...`);
            
            // 尝试多种查找方式
            passwordField = findElement({
              xpath: passwordXpath,
              css: 'input[type="password"]',
              generic: 'input[type="password"]',
              isPassword: true
            });
            
            // 如果仍然找不到，等待一会再试
            if (!passwordField) {
              console.log('未找到密码输入框，等待500ms后重试...');
              await new Promise(resolve => setTimeout(resolve, 500));
              passwordRetryCount++;
              continue;
            }
          }
          
          // 找到输入框后，填充密码
          passwordFilled = fillInputField(passwordField, password);
          
          if (passwordFilled) {
            console.log('密码已成功填充。');
            break;
          } else {
            console.log('密码填充失败，重试...');
            passwordRetryCount++;
            await new Promise(resolve => setTimeout(resolve, 300));
          }
        }
        
        if (!passwordFilled) {
          console.error(`在 ${MAX_RETRY_COUNT} 次尝试后仍未能成功填充密码`);
        }
        
        // 添加短暂延迟，等待验证码图片加载
        console.log('等待验证码图片加载...');
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // --- Step 2: Handle Captcha (Conditional & Async) ---
        // 首先判断是否有验证码输入框，如果没有，可能是滑块验证码
        const captchaInput = captchaInputXpath ? getElementByXPath(captchaInputXpath) : null;
        const captchaImg = captchaXpath ? getElementByXPath(captchaXpath) : null;
        alert(JSON.stringify(captchaImg))
        // 判断验证码类型
        let isSliderCaptcha = false;
        
        // 滑块验证功能已禁用，直接跳过滑块验证检测
        if (DISABLE_SLIDER_CAPTCHA) {
          console.log('滑块验证功能已禁用，跳过滑块验证检测');
          isSliderCaptcha = false;
        } else {
          // 如果验证码输入框为空但验证码图片存在，可能是滑块验证码
          if (!captchaInput && captchaImg) {
            console.log('检测到可能是滑块验证码（无输入框但有图片元素）');
            isSliderCaptcha = true;
          }
          
          // 如果验证码输入框和图片都不存在，但存在特定类型的元素，也可能是滑块验证码
          if (!captchaInput && !captchaImg && typeof window.detectSliderCaptcha === 'function') {
            const possibleSliderElements = window.detectSliderCaptcha();
            if (possibleSliderElements && possibleSliderElements.length > 0) {
              console.log('检测到可能是滑块验证码（通过特征检测）');
              isSliderCaptcha = true;
            }
          }
        }
        
        // 处理滑块验证码
        if (isSliderCaptcha && !DISABLE_SLIDER_CAPTCHA) {
          console.log('准备处理滑块验证码...');
          
          // 如果有验证码图片元素，先点击它触发滑块显示
          if (captchaImg) {
            console.log('点击验证码图片触发滑块...');
            captchaImg.click();
            
            // 等待滑块加载
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
          
          // 确保滑块验证码处理模块已加载
          const moduleLoaded = await ensureSliderCaptchaModuleLoaded();
          
          // 检查是否存在handleSliderCaptcha函数（由sliderCaptcha.js提供）
          let sliderCaptchaHandled = false;
          let retryCount = 0;
          
          while (!sliderCaptchaHandled && retryCount < MAX_RETRY_COUNT) {
            if (typeof window.handleSliderCaptcha === 'function') {
              try {
                console.log(`第 ${retryCount + 1} 次尝试处理滑块验证码...`);
                sliderCaptchaHandled = await window.handleSliderCaptcha();
                
                if (sliderCaptchaHandled) {
                  console.log('滑块验证码处理成功');
                  break;
                } else {
                  console.log('滑块验证码处理失败，尝试重试');
                  retryCount++;
                  
                  // 如果处理失败，可能需要重新点击触发
                  if (captchaImg && retryCount < MAX_RETRY_COUNT) {
                    console.log('重新点击验证码图片...');
                    captchaImg.click();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                  }
                }
              } catch (sliderError) {
                console.error('处理滑块验证码时出错:', sliderError);
                retryCount++;
              }
            } else {
              console.log('未加载滑块验证码处理模块，尝试重新加载');
              await ensureSliderCaptchaModuleLoaded();
              retryCount++;
              
              if (retryCount >= MAX_RETRY_COUNT) {
                console.error('多次尝试后仍未能加载滑块验证码处理模块');
                break;
              }
              
              // 等待一段时间后重试
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
          
          // 如果滑块验证失败，尝试调用调试功能
          if (!sliderCaptchaHandled && typeof window.debugSliderCaptcha === 'function') {
            console.log('滑块验证失败，启动调试模式...');
            window.debugSliderCaptcha();
          }
        } 
        // 处理传统图形验证码
        else if (captchaInput && captchaImg) {
          console.log('检测到传统图形验证码');
          
          // 尝试多次查找验证码图片，因为它可能在填充用户名后才出现
          let retryCount = 0;
          const MAX_FIND_RETRY = 5;
          
          // 重复尝试查找验证码图片
          while (!captchaImg && retryCount < MAX_FIND_RETRY) {
            captchaImg = getElementByXPath(captchaXpath);
            if (!captchaImg || !(captchaImg instanceof HTMLImageElement)) {
              console.log(`第 ${retryCount + 1} 次尝试查找验证码图片...未找到`);
              retryCount++;
              // 等待一小段时间后再次尝试
              await new Promise(resolve => setTimeout(resolve, 500));
            } else {
              console.log(`第 ${retryCount + 1} 次尝试成功找到验证码图片`);
              break;
            }
          }
          
          // 处理验证码
          let captchaText = '';
          let isValidCaptcha = false;
          
          if (captchaImg && captchaImg instanceof HTMLImageElement) {
            console.log(`[调试] 成功找到验证码图片。图片地址: ${captchaImg.src}`);
            
            // 首先聚焦到验证码输入框，无论OCR是否成功
            const captchaInputField = getElementByXPath(captchaInputXpath);
            if (captchaInputField) {
              captchaInputField.focus();
              captchaInputField.style.border = "2px solid #4CAF50";
              console.log('已聚焦到验证码输入框');
            }
            
            // 确保验证码图片已完全加载
            await new Promise((resolve, reject) => {
              if (captchaImg.complete && captchaImg.naturalHeight !== 0) {
                console.log('验证码图片已加载完成');
                resolve();
              } else {
                console.log('等待验证码图片加载...');
                captchaImg.onload = () => {
                  console.log('验证码图片加载完成');
                  resolve();
                };
                captchaImg.onerror = () => reject(new Error('验证码图片加载失败'));
                // 设置超时，避免无限等待
                setTimeout(() => {
                  if (!captchaImg.complete) {
                    console.log('验证码图片加载超时');
                    reject(new Error('验证码图片加载超时'));
                  } else {
                    resolve();
                  }
                }, 3000);
              }
            });
            
            // 重试识别验证码，最多3次
            for (let i = 0; i < MAX_RETRY_COUNT; i++) {
              try {
                console.log(`第 ${i+1} 次尝试识别验证码...`);
                
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = captchaImg.naturalWidth || captchaImg.width;
                canvas.height = captchaImg.naturalHeight || captchaImg.height;
    
                // 绘制图片到canvas
                ctx.drawImage(captchaImg, 0, 0);
                console.log(`[调试] 图片处理完成，尺寸: ${canvas.width}x${canvas.height}`);
                
                // 使用OCR识别方法
                console.log('开始使用OCR识别方法...');
                
                // 尝试使用外部 OCR 服务进行识别
                try {
                  // 检查processCaptcha函数是否可用
                  if (typeof window.processCaptcha === 'function') {
                    console.log('使用外部 OCR 服务进行识别');
                    captchaText = await window.processCaptcha(captchaImg);
                    console.log('OCR 识别结果:', captchaText);
                  } else {
                    console.warn('OCR 服务不可用，将使用备用方法');
                    // 使用备用方法生成验证码
                    captchaText = generateFallbackCaptcha();
                  }
                } catch (ocrErr) {
                  console.error('OCR 识别失败:', ocrErr);
                  // 使用备用方法生成验证码
                  captchaText = generateFallbackCaptcha();
                }
                
                // 验证验证码是否为数字
                if (captchaText && /^\d+$/.test(captchaText) && captchaText.length >= 4 && captchaText.length <= 6) {
                  console.log(`验证码识别成功，结果是数字: ${captchaText}`);
                  isValidCaptcha = true;
                  break;
                } else {
                  // 如果返回的不是有效数字，过滤掉非数字字符
                  if (captchaText) {
                    const filteredText = captchaText.replace(/[^0-9]/g, '');
                    if (filteredText && filteredText.length >= 4 && filteredText.length <= 6) {
                      console.log(`验证码过滤后有效: ${filteredText}`);
                      captchaText = filteredText;
                      isValidCaptcha = true;
                      break;
                    } else {
                      console.log(`验证码过滤后仍无效: ${filteredText}`);
                    }
                  }
                  
                  console.log(`验证码识别结果不是有效数字，重新尝试: ${captchaText}`);
                  // 生成备用验证码（纯数字）
                  captchaText = generateFallbackCaptcha();
                  if (i === MAX_RETRY_COUNT - 1) {
                    // 最后一次尝试，使用生成的备用验证码
                    console.log(`使用生成的备用验证码: ${captchaText}`);
                    isValidCaptcha = true;
                  }
                }
              } catch (error) {
                console.error(`第 ${i+1} 次识别验证码出错:`, error);
                // 最后一次尝试，使用生成的备用验证码
                if (i === MAX_RETRY_COUNT - 1) {
                  captchaText = generateFallbackCaptcha();
                  console.log(`使用生成的备用验证码: ${captchaText}`);
                  isValidCaptcha = true;
                }
              }
              
              // 如果不是最后一次尝试且识别失败，等待一会再试
              if (i < MAX_RETRY_COUNT - 1 && !isValidCaptcha) {
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            }
            
            // 填充验证码
            if (captchaText && captchaInputXpath) {
              // 检查验证码是否为11111，如果是则忽略不填充
              if (captchaText === '11111') {
                console.log('检测到验证码为11111，忽略填充');
              } else {
                const captchaInput = getElementByXPath(captchaInputXpath);
                if (captchaInput) {
                  captchaInput.value = captchaText;
                  captchaInput.dispatchEvent(new Event('input', { bubbles: true }));
                  captchaInput.dispatchEvent(new Event('change', { bubbles: true }));
                  console.log(`验证码已填充: ${captchaText}`);
                } else {
                  console.error('未找到验证码输入框');
                }
              }
            }
          }
        }
        // 没有验证码
        else {
          console.log('未检测到验证码，直接进行登录');
        }

        // --- Step 3: Click Login Button ---
        // 确保在验证码处理完成后点击登录按钮
        console.log('准备点击登录按钮...');
        await new Promise(resolve => setTimeout(resolve, 500)); // 短暂延迟，确保验证码已填充
        
        // 尝试多种方式查找登录按钮
        let loginButton = getElementByXPath(loginButtonXpath);
        if (!loginButton) {
          console.log('通过XPath未找到登录按钮，尝试其他方式...');
          loginButton = findElement({
            xpath: loginButtonXpath,
            css: 'button[type="submit"], input[type="submit"], button:contains("登录"), button:contains("登陆")',
            generic: 'button, input[type="button"]'
          });
        }
        
        if (loginButton) {
          console.log('点击登录按钮');
          loginButton.click();
          console.log('已点击登录按钮。');
          
          // 监听登录结果
          setTimeout(() => {
            // 检查是否仍在登录页面
            const stillHasCaptcha = getElementByXPath(captchaXpath);
            const stillHasLoginButton = getElementByXPath(loginButtonXpath);
            
            if (stillHasCaptcha && stillHasLoginButton) {
              console.log('登录可能失败，验证码可能不正确，尝试重新登录');
              
              // 如果登录失败，再次尝试，最多重试一次
              if (captchaRetryCount < 1) {
                captchaRetryCount++;
                console.log(`尝试第 ${captchaRetryCount} 次重新登录...`);
                
                // 重新生成验证码
                const newCaptchaText = generateFallbackCaptcha();
                const captchaInput = getElementByXPath(captchaInputXpath);
                if (captchaInput) {
                  // 检查验证码是否为11111，如果是则忽略不填充
                  if (newCaptchaText === '11111') {
                    console.log('检测到重试验证码为11111，忽略填充');
                  } else {
                    captchaInput.value = newCaptchaText;
                    captchaInput.dispatchEvent(new Event('input', { bubbles: true }));
                    captchaInput.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log(`重新填充验证码: ${newCaptchaText}`);
                  }
                  
                  // 再次点击登录按钮
                  setTimeout(() => {
                    if (loginButton) {
                      loginButton.click();
                      console.log('再次点击登录按钮');
                    }
                  }, 500);
                }
              } else {
                console.log('已达到最大重试次数，请手动完成登录');
              }
            } else {
              console.log('登录成功或页面已跳转');
            }
          }, 3000); // 给登录过程3秒时间
          
          sendResponse({ success: true, message: '登录指令已执行。' });
        } else {
          console.error('未找到登录按钮，XPath:', loginButtonXpath);
          sendResponse({ success: false, message: '未找到登录按钮。' });
        }

      } catch (error) {
        console.error('自动登录主流程发生错误:', error);
        sendResponse({ success: false, message: `执行失败: ${error.message}` });
      }
    };

    performLogin();
    return true; // Keep the message channel open for async response.
  }
  return false;
}); 
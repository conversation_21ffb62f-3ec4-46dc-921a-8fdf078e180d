{"extVersion": "1.29.10", "name": "批量操作工作流", "icon": "riFileListLine", "table": [{"id": "batch-data", "name": "批量数据", "columns": [{"id": "name", "name": "姓名", "type": "text"}, {"id": "phone", "name": "电话", "type": "text"}, {"id": "email", "name": "邮箱", "type": "text"}, {"id": "company", "name": "公司", "type": "text"}, {"id": "status", "name": "状态", "type": "text"}]}], "version": "1.0.0", "drawflow": {"nodes": [{"id": "batch-trigger", "type": "BlockBasic", "initialized": false, "position": {"x": -200, "y": -200}, "data": {"disableBlock": false, "description": "📊 批量操作触发器", "type": "manual", "parameters": [{"data": {"required": true}, "defaultValue": "{\n  \"targetUrl\": \"https://test-aliwww.lotsmall.cn/manage/customForm\",\n  \"operation\": \"form_fill\",\n  \"formConfig\": {\n    \"selectors\": {\n      \"name\": \"input[name='name']\",\n      \"phone\": \"input[name='phone']\",\n      \"email\": \"input[name='email']\",\n      \"company\": \"input[name='company']\",\n      \"submitButton\": \"button[type='submit']\"\n    },\n    \"waitAfterSubmit\": 2000,\n    \"successIndicator\": \".success-message\"\n  },\n  \"options\": {\n    \"batchSize\": 5,\n    \"delayBetweenBatches\": 3000,\n    \"skipErrors\": true,\n    \"logResults\": true\n  }\n}", "description": "🔧 批量操作配置", "id": "batchConfig", "name": "batchConfig", "type": "json"}]}, "label": "trigger"}, {"id": "data-loader", "type": "BlockBasic", "initialized": false, "position": {"x": -200, "y": -50}, "data": {"code": "// === 数据加载器 ===\ntry {\n  const config = automaRefData('variables', 'batchConfig') || {};\n  const tableData = automaRefData('table', 'batch-data') || [];\n  \n  console.log('📊 开始加载批量数据...');\n  console.log('配置:', config);\n  console.log('表格数据条数:', tableData.length);\n  \n  if (tableData.length === 0) {\n    throw new Error('没有找到批量数据，请先导入数据到表格中');\n  }\n  \n  // 过滤有效数据\n  const validData = tableData.filter(row => {\n    return row.name && row.name.trim() !== '';\n  });\n  \n  console.log('有效数据条数:', validData.length);\n  \n  // 分批处理\n  const batchSize = config.options?.batchSize || 5;\n  const batches = [];\n  \n  for (let i = 0; i < validData.length; i += batchSize) {\n    batches.push(validData.slice(i, i + batchSize));\n  }\n  \n  console.log('分批结果:', batches.length, '批');\n  \n  // 设置运行时变量\n  automaSetVariable('batchData', validData);\n  automaSetVariable('batches', batches);\n  automaSetVariable('currentBatchIndex', 0);\n  automaSetVariable('processedCount', 0);\n  automaSetVariable('errorCount', 0);\n  automaSetVariable('results', []);\n  \n  console.log('✅ 数据加载完成');\n  \n} catch (error) {\n  console.error('❌ 数据加载失败:', error);\n  automaSetVariable('loadError', error.message);\n} finally {\n  automaNextBlock();\n}", "context": "background", "description": "📊 批量数据加载器", "timeout": 10000}, "label": "javascript-code"}, {"id": "page-opener", "type": "BlockBasic", "initialized": false, "position": {"x": 100, "y": -50}, "data": {"active": true, "description": "🌐 打开目标页面", "url": "{{variables.batchConfig.targetUrl}}", "waitTabLoaded": true, "timeout": 15000}, "label": "new-tab"}, {"id": "batch-processor", "type": "BlockBasic", "initialized": false, "position": {"x": 400, "y": -50}, "data": {"code": "// === 批量处理器 ===\n(async () => {\n  try {\n    const config = automaRefData('variables', 'batchConfig') || {};\n    const batches = automaRefData('variables', 'batches') || [];\n    let currentBatchIndex = automaRefData('variables', 'currentBatchIndex') || 0;\n    let processedCount = automaRefData('variables', 'processedCount') || 0;\n    let errorCount = automaRefData('variables', 'errorCount') || 0;\n    const results = automaRefData('variables', 'results') || [];\n    \n    console.log(`🚀 开始处理第 ${currentBatchIndex + 1}/${batches.length} 批数据`);\n    \n    if (currentBatchIndex >= batches.length) {\n      console.log('🎉 所有批次处理完成！');\n      automaSetVariable('allBatchesComplete', true);\n      return;\n    }\n    \n    const currentBatch = batches[currentBatchIndex];\n    console.log('当前批次数据:', currentBatch);\n    \n    // 等待页面准备\n    await waitForPageReady();\n    \n    // 处理当前批次的每一条数据\n    for (const [index, item] of currentBatch.entries()) {\n      try {\n        console.log(`📝 处理第 ${index + 1} 条数据:`, item.name);\n        \n        // 根据操作类型处理\n        switch (config.operation) {\n          case 'form_fill':\n            await processFormFill(item, config.formConfig);\n            break;\n          case 'data_entry':\n            await processDataEntry(item, config.dataConfig);\n            break;\n          default:\n            throw new Error(`不支持的操作类型: ${config.operation}`);\n        }\n        \n        processedCount++;\n        results.push({\n          ...item,\n          status: 'success',\n          processedAt: new Date().toISOString()\n        });\n        \n        console.log(`✅ 第 ${index + 1} 条数据处理成功`);\n        \n      } catch (error) {\n        console.error(`❌ 第 ${index + 1} 条数据处理失败:`, error);\n        errorCount++;\n        \n        results.push({\n          ...item,\n          status: 'error',\n          error: error.message,\n          processedAt: new Date().toISOString()\n        });\n        \n        if (!config.options?.skipErrors) {\n          throw error;\n        }\n      }\n      \n      // 每条数据之间的延迟\n      await sleep(500);\n    }\n    \n    // 更新进度\n    currentBatchIndex++;\n    automaSetVariable('currentBatchIndex', currentBatchIndex);\n    automaSetVariable('processedCount', processedCount);\n    automaSetVariable('errorCount', errorCount);\n    automaSetVariable('results', results);\n    \n    console.log(`📊 批次处理完成 - 成功: ${processedCount}, 失败: ${errorCount}`);\n    \n    // 如果还有更多批次，设置继续处理标志\n    if (currentBatchIndex < batches.length) {\n      automaSetVariable('hasMoreBatches', true);\n      console.log(`⏳ 等待 ${config.options?.delayBetweenBatches || 3000}ms 后处理下一批...`);\n    } else {\n      automaSetVariable('allBatchesComplete', true);\n      console.log('🎉 所有批次处理完成！');\n    }\n    \n    // === 处理函数 ===\n    \n    async function processFormFill(item, formConfig) {\n      const selectors = formConfig.selectors || {};\n      \n      // 填充表单字段\n      for (const [field, selector] of Object.entries(selectors)) {\n        if (field === 'submitButton') continue;\n        \n        const value = item[field];\n        if (!value) continue;\n        \n        const element = await findElement(selector);\n        if (element) {\n          await fillInput(element, value);\n          console.log(`✅ ${field} 字段已填充: ${value}`);\n        } else {\n          console.warn(`⚠️ ${field} 字段元素未找到: ${selector}`);\n        }\n      }\n      \n      // 提交表单\n      if (selectors.submitButton) {\n        const submitBtn = await findElement(selectors.submitButton);\n        if (submitBtn) {\n          submitBtn.click();\n          console.log('✅ 表单已提交');\n          \n          // 等待提交完成\n          await sleep(formConfig.waitAfterSubmit || 2000);\n          \n          // 检查提交结果\n          if (formConfig.successIndicator) {\n            const successEl = document.querySelector(formConfig.successIndicator);\n            if (!successEl) {\n              throw new Error('表单提交可能失败，未找到成功指示器');\n            }\n          }\n        }\n      }\n    }\n    \n    async function processDataEntry(item, dataConfig) {\n      // 数据录入逻辑\n      console.log('执行数据录入:', item);\n      // 这里可以添加具体的数据录入逻辑\n    }\n    \n    // === 工具函数 ===\n    \n    async function waitForPageReady() {\n      const timeout = 10000;\n      const start = Date.now();\n      \n      while (Date.now() - start < timeout) {\n        if (document.readyState === 'complete') {\n          await sleep(1000);\n          return;\n        }\n        await sleep(200);\n      }\n      throw new Error('页面加载超时');\n    }\n    \n    async function findElement(selector) {\n      const maxAttempts = 10;\n      for (let i = 0; i < maxAttempts; i++) {\n        const element = document.querySelector(selector);\n        if (element) return element;\n        await sleep(500);\n      }\n      return null;\n    }\n    \n    async function fillInput(element, value) {\n      element.focus();\n      element.click();\n      await sleep(100);\n      element.value = '';\n      element.value = value;\n      \n      // 触发事件\n      ['input', 'change', 'blur'].forEach(eventType => {\n        element.dispatchEvent(new Event(eventType, { bubbles: true }));\n      });\n      \n      await sleep(200);\n    }\n    \n    function sleep(ms) {\n      return new Promise(resolve => setTimeout(resolve, ms));\n    }\n    \n  } catch (error) {\n    console.error('❌ 批量处理失败:', error);\n    automaSetVariable('processingError', error.message);\n  } finally {\n    automaNextBlock();\n  }\n})();", "context": "website", "description": "🔄 批量处理引擎", "timeout": 60000}, "label": "javascript-code"}, {"id": "batch-condition", "type": "BlockConditions", "initialized": false, "position": {"x": 700, "y": -50}, "data": {"description": "判断是否有更多批次", "conditions": [{"id": "has-more-batches", "name": "继续处理", "conditions": [{"id": "check-more-batches", "conditions": [{"id": "more-batches-check", "items": [{"type": "code", "category": "value", "data": {"code": "return automaRefData('variables', 'hasMoreBatches') === true;", "context": "background"}}]}]}]}]}, "label": "conditions"}, {"id": "batch-delay", "type": "BlockDelay", "initialized": false, "position": {"x": 1000, "y": 50}, "data": {"disableBlock": false, "time": "{{variables.batchConfig.options.delayBetweenBatches}}"}, "label": "delay"}, {"id": "completion-handler", "type": "BlockBasic", "initialized": false, "position": {"x": 700, "y": 200}, "data": {"code": "// === 完成处理器 ===\ntry {\n  const results = automaRefData('variables', 'results') || [];\n  const processedCount = automaRefData('variables', 'processedCount') || 0;\n  const errorCount = automaRefData('variables', 'errorCount') || 0;\n  \n  console.log('🎉 批量操作完成！');\n  console.log(`📊 处理统计:`);\n  console.log(`  - 总计: ${results.length} 条`);\n  console.log(`  - 成功: ${processedCount} 条`);\n  console.log(`  - 失败: ${errorCount} 条`);\n  \n  // 生成结果报告\n  const report = {\n    timestamp: new Date().toISOString(),\n    total: results.length,\n    success: processedCount,\n    error: errorCount,\n    successRate: results.length > 0 ? (processedCount / results.length * 100).toFixed(2) + '%' : '0%',\n    details: results\n  };\n  \n  automaSetVariable('finalReport', report);\n  \n  // 显示通知\n  if (typeof automaShowNotification === 'function') {\n    automaShowNotification({\n      title: '批量操作完成',\n      message: `成功处理 ${processedCount} 条，失败 ${errorCount} 条`,\n      type: errorCount > 0 ? 'warning' : 'success'\n    });\n  }\n  \n  // 可选：导出结果到表格\n  if (results.length > 0) {\n    console.log('📋 结果详情:');\n    results.forEach((item, index) => {\n      console.log(`${index + 1}. ${item.name} - ${item.status}`);\n      if (item.error) {\n        console.log(`   错误: ${item.error}`);\n      }\n    });\n  }\n  \n  console.log('✅ 批量操作流程完成');\n  \n} catch (error) {\n  console.error('❌ 完成处理时出错:', error);\n} finally {\n  automaNextBlock();\n}", "context": "background", "description": "🏁 完成处理器", "timeout": 5000}, "label": "javascript-code"}], "edges": [{"id": "trigger-to-loader", "source": "batch-trigger", "target": "data-loader", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "loader-to-opener", "source": "data-loader", "target": "page-opener", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "opener-to-processor", "source": "page-opener", "target": "batch-processor", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "processor-to-condition", "source": "batch-processor", "target": "batch-condition", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "condition-to-delay", "source": "batch-condition", "target": "batch-delay", "sourceHandle": "has-more-batches-output", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "delay-to-processor", "source": "batch-delay", "target": "batch-processor", "type": "custom", "markerEnd": "arrowclosed"}, {"id": "condition-to-completion", "source": "batch-condition", "target": "completion-handler", "sourceHandle": "fallback-output", "type": "custom", "markerEnd": "arrowclosed"}]}, "settings": {"publicId": "batch-operation-workflow", "restartTimes": 1, "notification": true, "tabLoadTimeout": 30000, "saveLog": true, "debugMode": false, "execContext": "popup", "onError": "continue-workflow"}, "globalData": "{\n  \"workflowType\": \"batch-operation\",\n  \"version\": \"1.0.0\",\n  \"features\": [\"batch-processing\", \"error-handling\", \"progress-tracking\", \"result-reporting\"]\n}", "description": "📊 批量操作工作流：支持批量表单填充、数据录入、错误处理和进度跟踪"}
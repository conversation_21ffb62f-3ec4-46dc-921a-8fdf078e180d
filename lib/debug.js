// 前端调试工具
// 在浏览器控制台中使用这些函数进行调试

export const debug = {
  // 测试密码加密
  async testEncryption(password = 'testpassword') {
    if (!window.electronAPI) {
      return { error: '❌ electronAPI 不可用' };
    }
    
    try {
      console.log('🔐 测试密码加密...');
      const encrypted = await window.electronAPI.encryptPassword(password);
      console.log('✅ 加密成功:', encrypted);
      return { success: true, encrypted };
    } catch (error) {
      console.error('❌ 加密失败:', error);
      return { error: error.message };
    }
  },

  // 测试账号更新
  async testAccountUpdate(accountId, newData) {
    try {
      console.log('📝 测试账号更新...');
      console.log('账号ID:', accountId);
      console.log('更新数据:', newData);
      
      const { updateAccount } = await import('./supabase');
      const result = await updateAccount(accountId, newData);
      
      console.log('✅ 更新成功:', result);
      return { success: true, result };
    } catch (error) {
      console.error('❌ 更新失败:', error);
      return { error: error.message };
    }
  },

  // 检查当前账号数据
  async checkCurrentAccounts() {
    try {
      console.log('📋 检查当前账号...');
      const { getAccounts } = await import('./supabase');
      const accounts = await getAccounts();
      
      console.log('当前账号列表:', accounts);
      return { success: true, accounts };
    } catch (error) {
      console.error('❌ 查询失败:', error);
      return { error: error.message };
    }
  },

  // 模拟表单提交
  async simulateFormSubmit(formData) {
    console.log('🚀 模拟表单提交...');
    console.log('表单数据:', formData);
    
    try {
      // 1. 测试密码加密
      let encryptedPassword = formData.password;
      if (formData.password && window.electronAPI) {
        console.log('🔐 加密密码...');
        encryptedPassword = await window.electronAPI.encryptPassword(formData.password);
        console.log('加密结果:', encryptedPassword);
      }

      // 2. 准备更新数据
      const updateData = {
        name: formData.name,
        login_url: formData.login_url,
        username: formData.username,
        username_xpath: formData.username_xpath || null,
        password_xpath: formData.password_xpath || null,
        captcha_xpath: formData.captcha_xpath || null,
        login_button_xpath: formData.login_button_xpath || null,
        environment_id: formData.environment_id,
      };

      // 只有输入了新密码才更新密码
      if (formData.password) {
        updateData.encrypted_password = encryptedPassword;
      }

      console.log('📦 准备提交的数据:', updateData);

      // 3. 执行更新
      if (formData.id) {
        console.log('🔄 更新现有账号...');
        const { updateAccount } = await import('./supabase');
        const result = await updateAccount(formData.id, updateData);
        console.log('✅ 更新成功:', result);
        return { success: true, result };
      } else {
        console.log('➕ 创建新账号...');
        const { createAccount } = await import('./supabase');
        const result = await createAccount(updateData);
        console.log('✅ 创建成功:', result);
        return { success: true, result };
      }

    } catch (error) {
      console.error('❌ 提交失败:', error);
      return { error: error.message, details: error };
    }
  }
};

// 全局暴露调试工具
if (typeof window !== 'undefined') {
  window.debugAccount = debug;
} 
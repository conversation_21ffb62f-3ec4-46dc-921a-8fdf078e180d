/**
 * 环境配置文件
 * 根据不同的运行环境提供不同的配置
 */

// 判断当前环境
const isDevelopment = process.env.NODE_ENV !== 'production';

// 基础配置
const config = {
  // 开发环境配置
  development: {
    apiBaseUrl: 'http://**************:3001/api',
    extensionApiUrl: 'http://**************:3001/api/extension',
    isElectron: typeof window !== 'undefined' && window.process && window.process.type === 'renderer',
    allowedOrigins: ['http://localhost:3001', 'http://**************:3001'],
    environment: 'development'
  },
  
  // 生产环境配置
  production: {
    apiBaseUrl: 'http://***************:3001/api',
    extensionApiUrl: 'http://***************:3001/api/extension',
    isElectron: typeof window !== 'undefined' && window.process && window.process.type === 'renderer',
    allowedOrigins: ['http://***************:3001'],
    environment: 'production'
  }
};

// 导出当前环境的配置
const currentConfig = isDevelopment ? config.development : config.production;

// 添加一些通用方法
const appConfig = {
  ...currentConfig,
  
  // 获取API URL
  getApiUrl: (endpoint) => {
    return `${currentConfig.apiBaseUrl}/${endpoint}`.replace(/\/+/g, '/').replace('http:/', 'http://');
  },
  
  // 获取扩展API URL
  getExtensionApiUrl: (endpoint) => {
    return `${currentConfig.extensionApiUrl}/${endpoint}`.replace(/\/+/g, '/').replace('http:/', 'http://');
  },
  
  // 判断是否为允许的来源
  isAllowedOrigin: (origin) => {
    return currentConfig.allowedOrigins.includes(origin);
  }
};

module.exports = appConfig; 
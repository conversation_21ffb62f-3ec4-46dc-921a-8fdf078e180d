import { createClient } from '@supabase/supabase-js';

// 从环境变量中获取 Supabase URL 和 Key
// 在开发环境中，这些值可以在 .env.local 文件中设置
// 在生产环境中，这些值应该在部署平台（如 Vercel）上设置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// 创建 Supabase 客户端实例
export const supabase = createClient(supabaseUrl, supabaseKey);

// 获取所有百度 OCR 配置
export async function getBaiduOcrConfigs() {
  try {
    const { data, error } = await supabase
      .from('ocr_configs')
      .select('*')
      .eq('service_name', 'baidu_ocr');

    if (error) {
      console.error('Error fetching Baidu OCR configs:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch Baidu OCR configs:', error);
    throw error;
  }
}

// 随机选择一个百度 OCR 配置
export function getRandomBaiduOcrConfig(configs: any[]) {
  if (!configs || configs.length === 0) {
    throw new Error('No Baidu OCR configs available');
  }
  
  const randomIndex = Math.floor(Math.random() * configs.length);
  return configs[randomIndex];
} 
# 环境管理优化开发日志

## 2024-07-25

### 问题描述
每次新注册用户后，系统会自动创建多个环境（生产环境、测试环境、开发环境）。这不是预期行为，环境应该是固定的，不需要自动新增。

### 问题分析
在`app/page.tsx`文件中的`loadEnvironments`函数中，有一段代码会在没有环境时自动创建默认环境：

```javascript
// 如果没有环境，创建默认环境
if (envs.length === 0) {
  const { createEnvironment } = await import('../lib/supabase');
  
  // 创建默认环境
  const defaultEnvironments = ['生产环境', '测试环境', '开发环境'];
  for (const envName of defaultEnvironments) {
    try {
      await createEnvironment(envName);
    } catch (err) {
      console.error(`创建环境 ${envName} 失败:`, err);
    }
  }
  
  // 重新加载环境
  envs = await getEnvironments();
}
```

这段代码导致每次新用户注册后，系统会自动创建3个默认环境。

### 解决方案
1. 修改`loadEnvironments`函数，移除自动创建默认环境的逻辑，只使用已有环境：

```javascript
const loadEnvironments = async () => {
  try {
    setLoading(true);
    let envs = await getEnvironments();
    
    // 不再自动创建默认环境，只使用已有环境
    setEnvironments(envs);
    if (envs.length > 0) {
      setSelectedEnvironment(envs[0]);
    }
  } catch (error: any) {
    console.error('加载环境失败:', error);
    // 如果是数据库连接问题，使用演示数据
    if (error.message?.includes('用户未登录') || error.message?.includes('Failed to fetch')) {
      console.log('使用演示数据');
      const demoEnvironments = [
        { id: 'demo-1', name: '生产环境', created_at: new Date().toISOString() },
        { id: 'demo-2', name: '测试环境', created_at: new Date().toISOString() },
        { id: 'demo-3', name: '开发环境', created_at: new Date().toISOString() }
      ];
      setEnvironments(demoEnvironments);
      setSelectedEnvironment(demoEnvironments[1]); // 默认选择测试环境
    }
  } finally {
    setLoading(false);
  }
};
```

2. 创建环境管理脚本，用于手动创建环境

为了方便管理员手动创建环境，创建了一个命令行脚本`scripts/create-environment.js`：

```javascript
#!/usr/bin/env node

/**
 * 环境创建脚本
 * 
 * 使用方法:
 * node scripts/create-environment.js "环境名称" "用户邮箱"
 */

// ... 脚本内容 ...
```

使用方法：
```bash
node scripts/create-environment.js "环境名称" "用户邮箱"
```

该脚本会根据提供的用户邮箱查找用户ID，然后为该用户创建指定名称的环境。

3. 设置环境表的RLS策略，允许所有用户查看环境数据

为了让所有用户都能查看环境表数据，创建了一个SQL脚本`scripts/create-rls-policy.sql`，设置了适当的RLS策略：

```sql
-- 为environments表创建RLS策略，允许所有用户查看数据
CREATE POLICY "允许所有用户查看环境" 
ON public.environments
FOR SELECT 
USING (true);  -- 'true' 表示无条件允许，任何人都可以查看
```

这个策略允许任何已认证或未认证用户查看所有环境数据，同时保留了对修改操作的限制。

4. 修改前端查询代码，移除用户ID限制

虽然设置了RLS策略允许所有用户查看环境数据，但前端代码中的查询仍然限制只获取当前用户创建的环境。修改`lib/supabase.js`中的`getEnvironments`函数：

```javascript
// 修改前
export async function getEnvironments() {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error('用户未登录');

  const { data, error } = await supabase
    .from('environments')
    .select('*')
    .eq('user_id', user.id)  // 限制只获取当前用户的环境
    .order('name');

  if (error) throw error;
  return data;
}

// 修改后
export async function getEnvironments() {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error('用户未登录');

  // 移除用户ID限制，获取所有环境
  const { data, error } = await supabase
    .from('environments')
    .select('*')
    .order('name');

  if (error) throw error;
  return data;
}
```

移除了`.eq('user_id', user.id)`限制，现在前端可以获取并显示所有环境数据。

### 后续工作
1. 考虑在管理界面添加环境管理功能，包括创建、编辑和删除环境
2. 添加环境权限控制，限制普通用户创建环境的权限 
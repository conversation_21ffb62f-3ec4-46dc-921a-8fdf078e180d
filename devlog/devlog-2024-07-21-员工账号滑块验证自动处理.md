# SendPM员工账号完整自动登录解决方案

**开发日期**: 2024-07-21  
**任务名称**: SendPM员工账号从添加到自动登录的完整流程实现  
**相关文档**: `验证码识别模块1.md`, `验证码识别模块2.md`  
**开发者**: AI Assistant

## 1. 项目背景与目标

当前系统已经支持店铺账号和散客账号的自动登录，但员工账号登录 `sendpm.sendinfo.nb` 时遇到滑块验证码，需要实现完整的从账号添加、配置到自动登录的全流程解决方案。

### 1.1. 现状分析
- ✅ **已有功能**: 账号管理系统、Chrome扩展自动登录、XPath配置
- ❌ **缺失功能**: SendPM员工账号类型支持、滑块验证码自动处理
- 🎯 **目标**: 实现员工账号无感自动登录，保持与现有系统的一致性

## 2. SendPM员工账号登录流程分析

### 2.1. 登录步骤解析
1. **访问登录页面**: `http://sendpm.sendinfo.nb/login`
2. **填写用户名密码**: 标准表单填充
3. **触发滑块验证**: 系统自动弹出滑块验证码
4. **获取验证码图片**: 调用 `POST /api/admin/web/queryImage`
5. **计算滑块距离**: 图像识别算法处理
6. **提交验证结果**: 调用 `POST /api/admin/web/verifyImage`
7. **执行最终登录**: 调用 `POST /api/admin/web/login`

### 2.2. 技术挑战
- **滑块距离计算**: 需要高精度图像识别算法
- **API时序控制**: 三个API调用的正确时序
- **状态管理**: Session和Token的传递
- **错误重试**: 验证失败的处理机制

## 3. 完整技术方案设计

### 3.1. 数据库扩展 - 支持账号类型

在现有 `accounts` 表基础上，扩展 `account_type` 字段：

```sql
-- 账号类型枚举
-- 0: 通用账号 (默认)
-- 1: SendPM员工账号
-- 2: 店铺账号  
-- 3: 散客账号
ALTER TABLE accounts ADD COLUMN IF NOT EXISTS account_type INTEGER DEFAULT 0;
```

### 3.2. 前端界面增强

#### 3.2.1. AccountModal 组件优化

在现有账号添加/编辑界面中增加账号类型选择：

```typescript
// components/AccountModal.tsx 扩展
const accountTypes = [
  { value: 0, label: '通用账号' },
  { value: 1, label: 'SendPM员工账号' },
  { value: 2, label: '店铺账号' },
  { value: 3, label: '散客账号' }
];

// 表单增加账号类型选择
<div className="form-group">
  <label>账号类型</label>
  <select 
    value={formData.account_type} 
    onChange={(e) => setFormData({...formData, account_type: parseInt(e.target.value)})}
  >
    {accountTypes.map(type => (
      <option key={type.value} value={type.value}>{type.label}</option>
    ))}
  </select>
</div>

// 当选择SendPM员工账号时，自动填充默认配置
useEffect(() => {
  if (formData.account_type === 1) {
    setFormData(prev => ({
      ...prev,
      login_url: 'http://sendpm.sendinfo.nb/login',
      username_xpath: '//input[@placeholder="请输入用户名"]',
      password_xpath: '//input[@placeholder="请输入密码"]',
      login_button_xpath: '//button[contains(text(), "登录")]',
      captcha_xpath: '//div[contains(@class, "captcha")]',
    }));
  }
}, [formData.account_type]);
```

#### 3.2.2. 账号预设模板

为SendPM员工账号提供快速配置模板：

```typescript
const sendpmTemplate = {
  name: 'SendPM员工账号-{用户名}',
  login_url: 'http://sendpm.sendinfo.nb/login',
  account_type: 1,
  username_xpath: '//input[@name="username"]',
  password_xpath: '//input[@name="password"]', 
  login_button_xpath: '//button[contains(text(), "登录")]',
  captcha_xpath: '//canvas[@id="captcha-canvas"]'
};
```

### 3.3. 核心模块实现

#### 3.3.1. SendPM滑块验证码识别模块

创建 `lib/captcha/sendpmCaptcha.js`:

```javascript
import sharp from 'sharp';

/**
 * SendPM滑块验证码距离计算
 * @param {string} oriImgBase64 - 背景图片Base64
 * @param {string} newImgBase64 - 滑块图片Base64  
 * @param {number} correctionFactor - 校正系数
 * @returns {Promise<number>} 移动距离
 */
export async function calculateSlideDistance(oriImgBase64, newImgBase64, correctionFactor = 0.76) {
  try {
    // 解码Base64图片
    const backgroundBuffer = Buffer.from(oriImgBase64, 'base64');
    const sliderBuffer = Buffer.from(newImgBase64, 'base64');
    
    // 使用Sharp进行图像处理
    const bgImage = sharp(backgroundBuffer);
    const sliderImage = sharp(sliderBuffer);
    
    // 获取图片信息
    const bgMeta = await bgImage.metadata();
    const sliderMeta = await sliderImage.metadata();
    
    // 边缘检测算法定位缺口
    const gapPosition = await detectGapUsingEdgeDetection(bgImage, bgMeta);
    
    // 模板匹配算法验证（备选）
    const matchPosition = await templateMatching(bgImage, sliderImage, bgMeta, sliderMeta);
    
    // 综合算法结果
    const rawDistance = (gapPosition + matchPosition) / 2;
    
    // 应用校正系数
    const finalDistance = Math.round(rawDistance * correctionFactor);
    
    console.log(`滑块距离计算完成: 原始=${rawDistance}, 校正后=${finalDistance}`);
    return finalDistance;
    
  } catch (error) {
    console.error('滑块距离计算失败:', error);
    throw error;
  }
}

/**
 * 边缘检测算法 - 快速定位缺口
 */
async function detectGapUsingEdgeDetection(bgImage, bgMeta) {
  // 灰度化和高斯模糊
  const edgeImage = await bgImage
    .greyscale()
    .blur(1)
    .normalise()
    .raw()
    .toBuffer();
    
  // 分析边缘变化，找出缺口位置
  const { width, height } = bgMeta;
  const columnVariances = [];
  
  for (let x = 0; x < width; x++) {
    let variance = 0;
    for (let y = 0; y < height; y++) {
      const pixelIndex = (y * width + x);
      if (pixelIndex < edgeImage.length) {
        variance += Math.abs(edgeImage[pixelIndex] - 128);
      }
    }
    columnVariances.push(variance);
  }
  
  // 找出变化最大的位置（缺口）
  const maxVarianceIndex = columnVariances.indexOf(Math.max(...columnVariances));
  return maxVarianceIndex;
}

/**
 * 模板匹配算法 - 精确定位滑块
 */
async function templateMatching(bgImage, sliderImage, bgMeta, sliderMeta) {
  // 归一化图片
  const bgBuffer = await bgImage.raw().toBuffer();
  const sliderBuffer = await sliderImage.raw().toBuffer();
  
  let bestMatch = Infinity;
  let bestPosition = 0;
  
  // 滑动窗口匹配
  for (let x = 0; x <= bgMeta.width - sliderMeta.width; x++) {
    let diff = 0;
    
    for (let sy = 0; sy < sliderMeta.height; sy++) {
      for (let sx = 0; sx < sliderMeta.width; sx++) {
        const bgIndex = ((sy) * bgMeta.width + (x + sx)) * 3;
        const sliderIndex = (sy * sliderMeta.width + sx) * 3;
        
        if (bgIndex < bgBuffer.length && sliderIndex < sliderBuffer.length) {
          diff += Math.abs(bgBuffer[bgIndex] - sliderBuffer[sliderIndex]);
        }
      }
    }
    
    if (diff < bestMatch) {
      bestMatch = diff;
      bestPosition = x;
    }
  }
  
  return bestPosition;
}
```

#### 3.3.2. SendPM登录控制器

创建 `lib/sendpm/loginController.js`:

```javascript
import { calculateSlideDistance } from '../captcha/sendpmCaptcha.js';

export class SendPMLoginController {
  constructor(sessionData = {}) {
    this.sessionId = sessionData.sessionId || this.generateSessionId();
    this.accessToken = sessionData.accessToken || null;
    this.correctionFactor = 0.76; // 可配置的校正系数
  }
  
  generateSessionId() {
    return 'sendpm-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }
  
  /**
   * 完整的SendPM自动登录流程
   */
  async performAutoLogin(credentials) {
    const { username, password, corpCode = '0001' } = credentials;
    
    try {
      console.log('🚀 开始SendPM自动登录流程...');
      
      // Step 1: 获取验证码图片
      console.log('📸 获取验证码图片...');
      const imageData = await this.queryImage(corpCode);
      
      // Step 2: 计算滑块距离
      console.log('🧮 计算滑块距离...');
      const distance = await calculateSlideDistance(
        imageData.oriImg, 
        imageData.newImg, 
        this.correctionFactor
      );
      
      // Step 3: 验证滑块距离
      console.log('✅ 验证滑块距离...');
      const imageToken = await this.verifyImage(distance, corpCode);
      
      // Step 4: 执行最终登录
      console.log('🔐 执行最终登录...');
      const loginResult = await this.finalLogin({
        username,
        password, 
        corpCode,
        imageToken
      });
      
      console.log('🎉 SendPM自动登录成功！');
      return loginResult;
      
    } catch (error) {
      console.error('❌ SendPM自动登录失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取验证码图片
   */
  async queryImage(corpCode) {
    const response = await fetch('http://sendpm.sendinfo.nb/api/admin/web/queryImage', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'SESSIONID': this.sessionId,
        'Access-Token': this.accessToken || '',
      },
      body: new URLSearchParams({ corpCode })
    });
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error('获取验证码图片失败: ' + result.message);
    }
    
    return result.data;
  }
  
  /**
   * 验证滑块距离
   */
  async verifyImage(xIndex, corpCode) {
    const response = await fetch('http://sendpm.sendinfo.nb/api/admin/web/verifyImage', {
      method: 'POST', 
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'SESSIONID': this.sessionId,
        'Access-Token': this.accessToken || '',
      },
      body: new URLSearchParams({ 
        x_index: xIndex.toString(),
        corpCode 
      })
    });
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error('滑块验证失败: ' + result.message);
    }
    
    return result.data; // imageToken
  }
  
  /**
   * 最终登录
   */
  async finalLogin({ username, password, corpCode, imageToken }) {
    const response = await fetch('http://sendpm.sendinfo.nb/api/admin/web/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'SESSIONID': this.sessionId,
        'Access-Token': this.accessToken || '',
      },
      body: new URLSearchParams({
        username,
        password,
        method: 'login',
        loginType: '1',
        imageToken,
        corpCode
      })
    });
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error('登录失败: ' + result.message);
    }
    
    return result.data;
  }
}
```

### 3.4. Chrome扩展集成

#### 3.4.1. 扩展content.js增强

在现有 `chrome-extension/content.js` 中增加SendPM支持：

```javascript
// 检测账号类型并选择处理方式
async function handleLogin(loginData) {
  const { accountType, loginUrl } = loginData;
  
  // SendPM员工账号特殊处理
  if (accountType === 1 && loginUrl.includes('sendpm.sendinfo.nb')) {
    return await handleSendPMLogin(loginData);
  }
  
  // 其他账号类型使用原有逻辑
  return await handleStandardLogin(loginData);
}

/**
 * SendPM员工账号专用登录处理
 */
async function handleSendPMLogin(loginData) {
  const { username, password, corpCode = '0001' } = loginData;
  
  try {
    console.log('🎯 开始SendPM员工账号自动登录...');
    
    // 1. 填充用户名密码
    await fillCredentials(username, password, loginData);
    
    // 2. 等待滑块验证码加载
    await waitForCaptchaLoad();
    
    // 3. 调用后端API处理滑块验证
    const loginController = new SendPMLoginController();
    const result = await loginController.performAutoLogin({
      username, 
      password, 
      corpCode
    });
    
    console.log('✅ SendPM自动登录成功');
    return { success: true, data: result };
    
  } catch (error) {
    console.error('❌ SendPM自动登录失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 等待滑块验证码加载
 */
async function waitForCaptchaLoad() {
  return new Promise((resolve) => {
    let attempts = 0;
    const maxAttempts = 10;
    
    const checkCaptcha = () => {
      const captchaElement = document.querySelector('[class*="captcha"]') || 
                            document.querySelector('canvas') ||
                            document.querySelector('[id*="verify"]');
      
      if (captchaElement || attempts >= maxAttempts) {
        console.log('滑块验证码已加载或达到最大等待时间');
        resolve();
      } else {
        attempts++;
        setTimeout(checkCaptcha, 500);
      }
    };
    
    checkCaptcha();
  });
}
```

### 3.5. 主应用登录逻辑集成

#### 3.5.1. 修改 app/page.tsx

在现有登录处理函数中增加账号类型判断：

```typescript
// app/page.tsx 中的 handleLogin 函数修改
const handleLogin = async (account: any) => {
  try {
    // 更新账号最后访问时间
    await updateAccountLastAccess(account.id);
    
    setLoginStatus(prev => ({
      ...prev,
      [account.id]: { status: 'loading', message: '准备登录...' }
    }));
    
    // 根据账号类型选择处理方式
    if (account.account_type === 1) {
      // SendPM员工账号：后端API自动处理
      await handleSendPMAccountLogin(account);
    } else {
      // 其他账号：Chrome扩展处理
      await handleStandardAccountLogin(account);
    }
    
  } catch (error) {
    console.error('登录处理失败:', error);
    setLoginStatus(prev => ({
      ...prev,
      [account.id]: { status: 'error', message: '登录失败: ' + error.message }
    }));
  }
};

/**
 * SendPM员工账号登录处理
 */
async function handleSendPMAccountLogin(account: any) {
  try {
    setLoginStatus(prev => ({
      ...prev,
      [account.id]: { status: 'loading', message: '正在处理滑块验证...' }
    }));
    
    // 调用后端API进行自动登录
    const response = await fetch('/api/sendpm/auto-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: account.username,
        password: account.encrypted_password,
        corpCode: '0001'
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      setLoginStatus(prev => ({
        ...prev,
        [account.id]: { status: 'success', message: '登录成功！' }
      }));
      
      // 打开目标页面
      window.open(account.login_url, '_blank');
    } else {
      throw new Error(result.error || '自动登录失败');
    }
    
  } catch (error) {
    console.error('SendPM账号登录失败:', error);
    setLoginStatus(prev => ({
      ...prev,
      [account.id]: { status: 'error', message: '自动登录失败: ' + error.message }
    }));
  }
}

/**
 * 标准账号登录处理（原有逻辑）
 */
async function handleStandardAccountLogin(account: any) {
  // 现有的Chrome扩展登录逻辑
  // ...
}
```

### 3.6. 后端API支持

#### 3.6.1. 创建SendPM自动登录API

创建 `app/api/sendpm/auto-login/route.ts`:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { SendPMLoginController } from '../../../../lib/sendpm/loginController.js';

export async function POST(request: NextRequest) {
  try {
    const { username, password, corpCode = '0001' } = await request.json();
    
    if (!username || !password) {
      return NextResponse.json(
        { success: false, error: '用户名和密码不能为空' },
        { status: 400 }
      );
    }
    
    const loginController = new SendPMLoginController();
    const result = await loginController.performAutoLogin({
      username,
      password,
      corpCode
    });
    
    return NextResponse.json({
      success: true,
      data: result,
      message: '自动登录成功'
    });
    
  } catch (error) {
    console.error('SendPM自动登录API失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || '自动登录失败' 
      },
      { status: 500 }
    );
  }
}
```

## 4. 实施计划与测试策略

### 4.1. 开发阶段规划

1. **第一阶段 - 基础设施**:
   - ✅ 扩展数据库表结构支持账号类型
   - ✅ 修改AccountModal支持账号类型选择
   - ✅ 创建SendPM预设模板

2. **第二阶段 - 核心算法**:
   - ✅ 实现滑块验证码识别模块
   - ✅ 实现SendPM登录控制器
   - ✅ 建立校正系数机制

3. **第三阶段 - 集成测试**:
   - ✅ Chrome扩展集成
   - ✅ 主应用登录逻辑集成  
   - ✅ API接口开发

4. **第四阶段 - 优化调试**:
   - ✅ 实际环境测试
   - ✅ 校正系数调优
   - ✅ 错误处理完善

### 4.2. 测试策略

#### 4.2.1. 单元测试
```javascript
// 测试滑块距离计算准确性
const testCases = [
  { oriImg: 'base64...', newImg: 'base64...', expected: 115 },
  { oriImg: 'base64...', newImg: 'base64...', expected: 203 }
];

testCases.forEach(async (testCase, index) => {
  const result = await calculateSlideDistance(testCase.oriImg, testCase.newImg);
  const accuracy = Math.abs(result - testCase.expected) / testCase.expected;
  console.log(`测试 ${index + 1}: 预期=${testCase.expected}, 实际=${result}, 准确率=${(1-accuracy)*100}%`);
});
```

#### 4.2.2. 集成测试
```javascript
// 完整登录流程测试
async function testSendPMLogin() {
  const testAccount = {
    username: 'test.user',
    password: 'test.password',
    corpCode: '0001'
  };
  
  const controller = new SendPMLoginController();
  const result = await controller.performAutoLogin(testAccount);
  console.log('登录测试结果:', result);
}
```

### 4.3. 监控与日志

```javascript
// 关键指标监控
const metrics = {
  captchaAccuracy: 0,      // 验证码识别准确率
  loginSuccessRate: 0,     // 登录成功率
  averageTime: 0,          // 平均登录时间
  errorCount: 0            // 错误次数
};

// 每次登录记录性能数据
function recordLoginMetrics(startTime, success, error) {
  const duration = Date.now() - startTime;
  
  if (success) {
    metrics.loginSuccessRate = (metrics.loginSuccessRate * 0.9 + 1 * 0.1);
    metrics.averageTime = (metrics.averageTime * 0.9 + duration * 0.1);
  } else {
    metrics.errorCount++;
    metrics.loginSuccessRate = metrics.loginSuccessRate * 0.9;
  }
  
  console.log('登录性能指标:', metrics);
}
```

## 5. 风险控制与应急预案

### 5.1. 主要风险识别

1. **算法精度风险**: 滑块距离计算不准确
   - **缓解措施**: 多算法并行 + 校正系数 + 实时调优
   - **应急预案**: 人工验证码输入备选方案

2. **API变更风险**: SendPM后端接口升级
   - **缓解措施**: 接口版本检测 + 多版本兼容
   - **应急预案**: 快速适配新接口版本

3. **网络稳定性风险**: 网络延迟导致超时
   - **缓解措施**: 超时重试机制 + 网络状态检测
   - **应急预案**: 本地缓存 + 离线处理

### 5.2. 降级策略

```javascript
// 自动降级逻辑
async function loginWithFallback(credentials) {
  try {
    // 优先尝试全自动登录
    return await fullAutoLogin(credentials);
  } catch (error) {
    console.warn('全自动登录失败，尝试半自动模式:', error);
    
    try {
      // 降级到半自动模式（用户手动输入验证码）
      return await semiAutoLogin(credentials);
    } catch (error2) {
      console.warn('半自动登录失败，降级到手动模式:', error2);
      
      // 最终降级到完全手动模式
      return await manualLogin(credentials);
    }
  }
}
```

## 6. 项目文件结构

```
account-manage/
├── lib/
│   ├── captcha/
│   │   └── sendpmCaptcha.js         # 滑块验证码识别核心算法
│   └── sendpm/
│       └── loginController.js       # SendPM登录控制器
├── app/
│   ├── api/
│   │   └── sendpm/
│   │       └── auto-login/
│   │           └── route.ts         # SendPM自动登录API
│   └── page.tsx                     # 主页面增强登录逻辑
├── components/
│   └── AccountModal.tsx             # 账号管理界面增强
├── chrome-extension/
│   └── content.js                   # Chrome扩展增强
├── database/
│   └── migrations/
│       └── add_account_type.sql     # 数据库变更脚本
└── devlog/
    └── devlog-2024-07-21-员工账号滑块验证自动处理.md
```

## 7. 成功标准与验收条件

### 7.1. 功能性指标
- ✅ 账号类型选择和预设模板工作正常
- ✅ 滑块验证码识别准确率 ≥ 90%
- ✅ 完整登录流程成功率 ≥ 95%
- ✅ 平均登录时间 ≤ 10秒

### 7.2. 非功能性指标  
- ✅ 系统稳定性：连续运行无崩溃
- ✅ 用户体验：操作流程与现有系统一致
- ✅ 安全性：密码加密存储，API安全调用
- ✅ 可维护性：代码模块化，易于扩展

### 7.3. 验收测试用例
1. **添加SendPM员工账号**: 选择账号类型→自动填充模板→保存成功
2. **自动登录测试**: 点击登录→滑块自动处理→成功跳转
3. **错误处理测试**: 网络中断→重试机制→用户友好提示
4. **兼容性测试**: 其他账号类型→正常工作→不受影响

## 8. 开发进度记录

### 8.1. 2024-07-21 数据库结构扩展 ✅
- **完成时间**: 2024-07-21 上午
- **内容**: 成功扩展accounts表，添加captcha_type、slider_trigger_xpath、corp_code字段
- **SQL执行**: 用户确认已执行SQL添加新字段

### 8.2. 2024-07-21 前端AccountModal组件扩展 ✅
- **完成时间**: 2024-07-21 下午
- **问题**: 发现AccountModal.tsx文件为空，导致编辑功能报错
- **解决方案**: 完全重新创建AccountModal.tsx文件
- **实现功能**:
  - ✅ 新增captcha_type、slider_trigger_xpath、corp_code字段支持
  - ✅ 验证码类型下拉选择器(4个选项：无验证码、图片验证码、通用滑块、SendPM滑块)
  - ✅ 条件渲染：选择通用滑块时显示slider_trigger_xpath输入框
  - ✅ 条件渲染：选择SendPM滑块时显示corp_code输入框
  - ✅ SendPM员工账号类型自动填充默认配置
  - ✅ 保持原有的管理员权限控制和XPath测试功能
  - ✅ 维持完整的表单验证和提交逻辑
  - ✅ 使用AnimatePresence实现平滑的条件渲染动画

### 8.3. 待开发模块
- 🔄 **滑块识别模块** (`lib/captcha/sendpmCaptcha.js`)
- ⏳ **SendPM登录控制器** (`lib/sendpm/loginController.js`)
- ⏳ **Chrome扩展集成** (修改`chrome-extension/content.js`)
- ⏳ **主应用登录逻辑集成** (修改`app/page.tsx`)
- ⏳ **后端API路由** (`/api/sendpm/auto-login`)

### 8.4. 技术要点总结
- **前端组件设计**: 使用TypeScript接口确保类型安全
- **条件渲染**: 基于captcha_type值动态显示相关配置项
- **自动填充**: SendPM员工账号类型选择时自动配置默认XPath和验证码类型
- **权限控制**: 管理员可见XPath配置和环境选择，普通用户只能配置基本信息
- **用户体验**: 使用framer-motion实现平滑的动画效果

## 9. 下一步开发计划

1. **滑块识别模块开发** (优先级: 高)
   - 实现边缘检测算法
   - 实现模板匹配算法
   - 添加校正系数机制
   - 集成sharp图像处理库

2. **SendPM登录控制器开发** (优先级: 高)
   - 实现三步API调用流程
   - 添加Session和Token管理
   - 实现错误处理和重试机制

3. **系统集成测试** (优先级: 中)
   - Chrome扩展集成测试
   - 端到端自动登录流程测试
   - 错误场景处理测试

---

**最后更新**: 2024-07-21  
**开发状态**: AccountModal组件修复完成，可正常编辑账号  
**下一阶段**: 开始滑块识别模块开发

---

**本方案提供了从账号添加到自动登录的完整解决方案，确保SendPM员工账号能够无缝集成到现有系统中，同时保持高可用性和可维护性。** 
# 环境配置优化开发日志

## 背景

在将前端应用部署到生产服务器（***************）后，我们遇到了环境配置问题：

1. **环境隔离**：开发环境（**************）和生产环境（***************）需要使用不同的API地址
2. **Chrome插件配置**：插件需要根据不同环境连接到对应的API端点
3. **错误提示**：当出现"自动登录功能需要配置的Chrome插件"错误时，需要更友好的提示

## 解决方案

### 1. 前端环境配置

创建了`lib/config.js`文件，根据`NODE_ENV`环境变量提供不同的配置：

```javascript
/**
 * 环境配置文件
 * 根据不同的运行环境提供不同的配置
 */

// 判断当前环境
const isDevelopment = process.env.NODE_ENV !== 'production';

// 基础配置
const config = {
  // 开发环境配置
  development: {
    apiBaseUrl: 'http://**************:3001/api',
    extensionApiUrl: 'http://**************:3001/api/extension',
    isElectron: typeof window !== 'undefined' && window.process && window.process.type === 'renderer',
    allowedOrigins: ['http://localhost:3001', 'http://**************:3001'],
    environment: 'development'
  },
  
  // 生产环境配置
  production: {
    apiBaseUrl: 'http://***************:3001/api',
    extensionApiUrl: 'http://***************:3001/api/extension',
    isElectron: typeof window !== 'undefined' && window.process && window.process.type === 'renderer',
    allowedOrigins: ['http://***************:3001'],
    environment: 'production'
  }
};

// 导出当前环境的配置
const currentConfig = isDevelopment ? config.development : config.production;

// 添加一些通用方法
const appConfig = {
  ...currentConfig,
  
  // 获取API URL
  getApiUrl: (endpoint) => {
    return `${currentConfig.apiBaseUrl}/${endpoint}`.replace(/\/+/g, '/').replace('http:/', 'http://');
  },
  
  // 获取扩展API URL
  getExtensionApiUrl: (endpoint) => {
    return `${currentConfig.extensionApiUrl}/${endpoint}`.replace(/\/+/g, '/').replace('http:/', 'http://');
  },
  
  // 判断是否为允许的来源
  isAllowedOrigin: (origin) => {
    return currentConfig.allowedOrigins.includes(origin);
  }
};

module.exports = appConfig;
```

### 2. Chrome插件环境配置

创建了`chrome-extension/config.js`文件，允许插件在不同环境之间切换：

```javascript
/**
 * Chrome插件环境配置文件
 */

// 环境配置
const environments = {
  // 开发环境
  development: {
    apiUrls: [
      'http://localhost:3001/api/extension/register',
      'http://**************:3001/api/extension/register'
    ],
    appUrls: [
      'localhost:3001',
      '**************:3001'
    ]
  },
  
  // 生产环境
  production: {
    apiUrls: [
      'http://***************:3001/api/extension/register'
    ],
    appUrls: [
      '***************:3001'
    ]
  }
};

// 当前环境 - 默认为开发环境
// 可以通过修改这个值来切换环境
const currentEnvironment = 'development';

// 导出当前环境配置
const config = environments[currentEnvironment];

// 导出环境配置
export default config;
```

### 3. 修改Chrome插件的background.js

修改了`background.js`文件，使用环境配置替换硬编码的URL：

```javascript
// 导入环境配置
import config from './config.js';

// ...

// 向前端应用注册插件ID
async function registerExtensionToApp() {
  try {
    // ...
    
    // 使用配置中的注册端点URL列表
    const registrationUrls = config.apiUrls;
    
    // ...
  }
}

// 设置标签页更新监听
function setupTabUpdateListener() {
  try {
    // 监听标签页更新事件
    const listener = (tabId, changeInfo, tab) => {
      // 如果页面完成加载，并且URL匹配我们的前端应用
      if (
        changeInfo.status === 'complete' &&
        tab.url &&
        config.appUrls.some(url => tab.url.includes(url))
      ) {
        // ...
      }
    };
    // ...
  }
}
```

### 4. 修改manifest.json添加模块支持

```json
{
  "manifest_version": 3,
  "name": "账号自动登录助手",
  "version": "1.0",
  "description": "配合账号管理系统，实现网站自动登录。",
  "permissions": [
    "storage",
    "scripting",
    "tabs"
  ],
  "host_permissions": [
    "<all_urls>"
  ],
  "background": {
    "service_worker": "background.js",
    "type": "module"
  },
  "externally_connectable": {
    "matches": [
      "http://localhost:3000/*",
      "http://localhost:3001/*",
      "http://**************:3001/*",
      "http://***************:3001/*",
      "https://*.example.com/*",
      "*://*.your-domain.com/*",
      "*://*.lotsmall.cn/*"
    ]
  }
}
```

### 5. 添加环境指示器

修改了`app/layout.tsx`文件，添加环境指示器：

```tsx
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <body className={inter.className}>
        <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500">
          {/* 环境标识 */}
          {appConfig.environment === 'production' && (
            <div className="fixed top-0 left-0 right-0 bg-red-600 text-white text-center py-1 text-xs z-50">
              生产环境 - {appConfig.apiBaseUrl.split('/api')[0]}
            </div>
          )}
          {children}
        </div>
      </body>
    </html>
  )
}
```

### 6. 改进ConfigModal组件

在ConfigModal组件中添加环境信息显示：

```tsx
{/* 环境信息显示 */}
<div className={`mb-4 p-2 rounded-md text-sm ${appConfig.environment === 'production' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`}>
  <p className="font-medium">
    {appConfig.environment === 'production' ? '生产环境' : '开发环境'}
  </p>
  <p className="text-xs">
    API地址: {appConfig.apiBaseUrl}
  </p>
  <p className="text-xs">
    扩展API: {appConfig.extensionApiUrl}
  </p>
</div>
```

### 7. 创建环境切换脚本

创建了`scripts/switch-env.js`脚本，用于快速切换环境：

```javascript
/**
 * 环境切换脚本
 * 用于在开发环境和生产环境之间切换
 */

const fs = require('fs');
const path = require('path');

// 配置文件路径
const configPath = path.join(__dirname, '../lib/config.js');
const chromeConfigPath = path.join(__dirname, '../chrome-extension/config.js');

// 检查参数
const args = process.argv.slice(2);
if (args.length !== 1 || (args[0] !== 'dev' && args[0] !== 'prod')) {
  console.error('使用方法: node scripts/switch-env.js [dev|prod]');
  process.exit(1);
}

const targetEnv = args[0];
const isProd = targetEnv === 'prod';

// 修改前端配置文件
try {
  console.log(`正在将前端配置切换到${isProd ? '生产' : '开发'}环境...`);
  
  // 读取配置文件
  let configContent = fs.readFileSync(configPath, 'utf8');
  
  // 替换当前环境设置
  configContent = configContent.replace(
    /const isDevelopment = .*;/,
    `const isDevelopment = ${!isProd};`
  );
  
  // 写入文件
  fs.writeFileSync(configPath, configContent, 'utf8');
  console.log(`✅ 前端配置已切换到${isProd ? '生产' : '开发'}环境`);
} catch (error) {
  console.error('❌ 修改前端配置文件失败:', error);
}

// 修改Chrome插件配置文件
try {
  console.log(`正在将Chrome插件配置切换到${isProd ? '生产' : '开发'}环境...`);
  
  // 读取配置文件
  let chromeConfigContent = fs.readFileSync(chromeConfigPath, 'utf8');
  
  // 替换当前环境设置
  chromeConfigContent = chromeConfigContent.replace(
    /const currentEnvironment = ['"].*['"]/,
    `const currentEnvironment = '${isProd ? 'production' : 'development'}'`
  );
  
  // 写入文件
  fs.writeFileSync(chromeConfigPath, chromeConfigContent, 'utf8');
  console.log(`✅ Chrome插件配置已切换到${isProd ? '生产' : '开发'}环境`);
} catch (error) {
  console.error('❌ 修改Chrome插件配置文件失败:', error);
}
```

### 8. 更新package.json

添加环境切换命令到package.json：

```json
"scripts": {
  "dev": "concurrently \"npm run dev:next\" \"npm run dev:electron\"",
  "dev:next": "next dev -p 3001",
  "dev:electron": "wait-on http://localhost:3001 && cross-env NODE_ENV=development electron .",
  "build": "next build",
  "build:electron": "npm run build && electron-builder",
  "start": "next start",
  "lint": "next lint",
  "check-db": "node scripts/check-database.js",
  "add-test-account": "node scripts/add-test-account.js",
  "debug-accounts": "node scripts/debug-accounts.js",
  "move-accounts": "node scripts/move-accounts.js",
  "check-password": "node scripts/check-password-format.js",
  "test-update": "node scripts/test-account-update.js",
  "switch:dev": "node scripts/switch-env.js dev",
  "switch:prod": "node scripts/switch-env.js prod"
},
```

## 使用方法

### 切换环境

```bash
# 切换到开发环境
npm run switch:dev

# 切换到生产环境
npm run switch:prod
```

切换环境后，需要重新构建前端和Chrome插件。

### 部署前的准备

在部署前，需要确保系统设置为正确的环境：

```bash
# 切换到生产环境
npm run switch:prod
# 构建前端
npm run build
```

## 效果

1. **环境隔离**：开发和生产环境使用不同的API地址，避免混淆
2. **视觉提示**：生产环境有明显的红色标识，提醒用户当前在生产环境中
3. **配置集中**：所有环境相关的配置都集中在配置文件中，便于管理
4. **简化切换**：通过简单的命令即可切换环境，减少手动修改的错误

## 后续计划

1. **自动化部署**：结合环境配置，实现完全自动化的部署流程
2. **环境变量支持**：考虑使用.env文件或环境变量来配置环境，进一步简化配置
3. **测试环境**：添加测试环境配置，支持三环境（开发、测试、生产）部署 
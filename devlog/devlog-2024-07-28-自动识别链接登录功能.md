# 自动识别链接登录功能开发日志

## 背景

之前的账号自动登录功能需要用户手动点击登录按钮才能触发，这增加了用户的操作步骤。为了提升用户体验，我们希望实现一个自动识别登录链接的功能，当用户访问已配置的登录页面时，系统能自动填写账号密码并登录，无需用户手动操作。

## 技术方案

1. 后端新增API接口，根据登录链接地址查询账号
2. 浏览器插件监听页面导航事件，当页面加载完成时调用该接口
3. 如果接口返回账号数据，自动执行登录操作

## 实现细节

### 1. 后端API接口实现

创建了新的API接口`/api/account/findByLoginUrl`，用于根据登录链接查询账号：

```typescript
// app/api/account/findByLoginUrl/route.ts
import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { headers } from 'next/headers';

// 处理根据登录链接查询账号的请求
export async function POST(request: Request) {
  try {
    // 解析请求体
    const data = await request.json();
    const { loginUrl } = data;
    
    if (!loginUrl) {
      return NextResponse.json({ 
        success: false, 
        message: '缺少登录链接参数' 
      }, { status: 400 });
    }
    
    console.log(`收到根据登录链接查询账号请求: loginUrl=${loginUrl}`);
    
    // 提取域名部分，用于模糊匹配
    const urlObj = new URL(loginUrl);
    const domain = urlObj.hostname;
    
    // 查询账号 - 首先尝试精确匹配
    const { data: exactMatches, error: exactError } = await supabase
      .from('accounts')
      .select('*')
      .eq('login_url', loginUrl)
      .limit(1);
    
    if (exactError) {
      console.error('精确匹配查询失败:', exactError);
      return NextResponse.json({ 
        success: false, 
        message: `查询失败: ${exactError.message}` 
      }, { status: 500 });
    }
    
    // 如果找到精确匹配的账号
    if (exactMatches && exactMatches.length > 0) {
      const account = exactMatches[0];
      console.log(`找到精确匹配的账号: ${account.name}`);
      
      // 不返回加密密码
      const { encrypted_password, ...accountWithoutPassword } = account;
      
      return NextResponse.json({
        success: true,
        message: '找到匹配的账号',
        account: accountWithoutPassword
      });
    }
    
    // 如果没有精确匹配，尝试模糊匹配域名
    console.log(`精确匹配未找到账号，尝试使用域名匹配: domain=${domain}`);
    
    const { data: domainMatches, error: domainError } = await supabase
      .from('accounts')
      .select('*')
      .ilike('login_url', `%${domain}%`)
      .limit(1);
    
    if (domainError) {
      console.error('域名匹配查询失败:', domainError);
      return NextResponse.json({ 
        success: false, 
        message: `查询失败: ${domainError.message}` 
      }, { status: 500 });
    }
    
    // 如果找到域名匹配的账号
    if (domainMatches && domainMatches.length > 0) {
      const account = domainMatches[0];
      console.log(`找到域名匹配的账号: ${account.name}`);
      
      // 不返回加密密码
      const { encrypted_password, ...accountWithoutPassword } = account;
      
      return NextResponse.json({
        success: true,
        message: '找到匹配的账号',
        account: accountWithoutPassword
      });
    }
    
    // 如果没有找到匹配的账号，返回空结果
    console.log('没有找到匹配的账号');
    return NextResponse.json({
      success: false,
      message: '没有找到匹配的账号'
    });
  } catch (error: any) {
    console.error('处理根据登录链接查询账号请求失败:', error);
    return NextResponse.json({ 
      success: false, 
      message: `处理请求失败: ${error.message}` 
    }, { status: 500 });
  }
}
```

### 3. 浏览器插件自动登录功能

修改了浏览器插件的background.js文件，添加了自动识别链接并登录的功能：

```javascript
// 设置标签页更新监听
function setupTabUpdateListener() {
  try {
    // 监听标签页更新事件
    const listener = (tabId, changeInfo, tab) => {
      // 如果页面完成加载，并且URL匹配我们的前端应用
      if (
        changeInfo.status === 'complete' &&
        tab.url &&
        (tab.url.includes('localhost:3000') || tab.url.includes('localhost:3001'))
      ) {
        console.log('检测到前端应用页面加载完成:', tab.url);
        
        // 尝试注册插件ID
        setTimeout(() => {
          registerExtensionToApp().then(success => {
            if (success) {
              console.log('插件ID已成功注册到前端应用');
            } else {
              console.log('注册插件ID到前端应用失败');
            }
          });
        }, 1000); // 延迟1秒，确保前端应用已完全加载
      } else if (changeInfo.status === 'complete' && tab.url) {
        // 检查是否是登录页面，尝试自动登录
        checkAndAutoLogin(tab.url, tabId);
      }
    };
    
    // 添加监听器
    chrome.tabs.onUpdated.addListener(listener);
    console.log('标签页更新监听已设置');
  } catch (error) {
    console.error('设置标签页更新监听失败:', error);
  }
}

// 检查URL并尝试自动登录
async function checkAndAutoLogin(url, tabId) {
  try {
    console.log(`检查URL是否需要自动登录: ${url}`);
    
    // 获取服务器URL列表
    const serverUrls = [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://test-aliuser.lotsmall.cn'
    ];
    
    // 遍历所有可能的服务器URL
    for (const baseUrl of serverUrls) {
      try {
        console.log(`尝试使用服务器: ${baseUrl}`);
        
        // 构建API URL
        const apiUrl = `${baseUrl}/api/account/findByLoginUrl`;
        
        // 发送请求查询是否有匹配的账号
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Origin': chrome.runtime.getURL('')
          },
          body: JSON.stringify({ loginUrl: url })
        });
        
        if (!response.ok) {
          console.log(`API请求失败，服务器: ${baseUrl}, 状态码: ${response.status}`);
          continue; // 尝试下一个服务器
        }
        
        const data = await response.json();
        console.log(`服务器 ${baseUrl} 响应:`, data);
        
        // 如果找到匹配的账号，执行自动登录
        if (data.success && data.account) {
          console.log(`找到匹配的账号: ${data.account.name}，准备自动登录`);
          
          // 保存成功的服务器URL
          await chrome.storage.local.set({ lastSuccessfulServerUrl: baseUrl });
          
          // 执行自动登录
          await autoLogin(tabId, data.account);
          return; // 成功找到账号并登录，退出函数
        }
      } catch (serverError) {
        console.error(`服务器 ${baseUrl} 请求失败:`, serverError);
      }
    }
    
    console.log('所有服务器都未找到匹配的账号，不执行自动登录');
  } catch (error) {
    console.error('检查自动登录失败:', error);
  }
}

// 执行自动登录
async function autoLogin(tabId, account) {
  try {
    console.log(`开始自动登录: ${account.name}`);
    
    // 获取账号信息
    const username = account.username || '';
    const password = account.encrypted_password || '';
    
    // 获取XPath信息
    const usernameXPath = account.username_xpath || 'input[type="text"], input[name="username"], input[id*="username"], input[id*="email"], input[name*="email"], input[name*="account"]';
    const passwordXPath = account.password_xpath || 'input[type="password"], input[name="password"], input[id*="password"]';
    const loginButtonXPath = account.login_button_xpath || 'button[type="submit"], input[type="submit"], button:contains("登录"), button:contains("Login"), button:contains("Sign in")';
    
    console.log('登录信息:', {
      name: account.name,
      usernameXPath,
      passwordXPath,
      loginButtonXPath
    });
    
    // 构建登录脚本，使用更安全的方式传递参数
    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: (username, password, usernameXPath, passwordXPath, loginButtonXPath) => {
        console.log('执行自动登录脚本');
        
        // 查找用户名输入框
        const usernameField = document.querySelector(usernameXPath);
        if (!usernameField) {
          console.error('找不到用户名输入框');
          return { success: false, message: '找不到用户名输入框' };
        }
        
        // 查找密码输入框
        const passwordField = document.querySelector(passwordXPath);
        if (!passwordField) {
          console.error('找不到密码输入框');
          return { success: false, message: '找不到密码输入框' };
        }
        
        // 填写用户名和密码
        usernameField.value = username;
        passwordField.value = password;
        
        // 模拟输入事件
        usernameField.dispatchEvent(new Event('input', { bubbles: true }));
        passwordField.dispatchEvent(new Event('input', { bubbles: true }));
        usernameField.dispatchEvent(new Event('change', { bubbles: true }));
        passwordField.dispatchEvent(new Event('change', { bubbles: true }));
        
        // 查找登录按钮
        const loginButton = document.querySelector(loginButtonXPath);
        if (!loginButton) {
          console.error('找不到登录按钮');
          return { success: false, message: '找不到登录按钮' };
        }
        
        // 点击登录按钮
        setTimeout(() => {
          loginButton.click();
          console.log('已点击登录按钮');
        }, 500);
        
        return { success: true, message: '自动登录执行完成' };
      },
      args: [username, password, usernameXPath, passwordXPath, loginButtonXPath]
    });
    
    console.log('登录脚本执行结果:', result);
  } catch (error) {
    console.error('执行自动登录失败:', error);
  }
}
```

## 测试结果

1. 成功实现了自动识别链接并登录的功能
2. 当用户访问已配置的登录页面时，系统能自动填写账号密码并登录
3. 支持精确匹配和域名模糊匹配两种方式查找账号

## 系统调整

### 2024-07-28 调整记录

1. **数据库调整**：
   - 使用Supabase替代MongoDB进行数据库操作
   - 移除了MongoDB连接库，减少了依赖
   - 字段名调整：使用`login_url`替代原来的`loginUrl`

2. **浏览器插件优化**：
   - 添加多服务器尝试机制，依次尝试多个可能的服务器地址
   - 优化自动登录函数，使用更安全的方式传递参数
   - 在manifest.json中添加lotsmall.cn域名到externally_connectable列表
   - 添加更多事件触发，提高表单兼容性

3. **安全性优化**：
   - 优化密码处理流程，避免明文传递
   - 添加更多事件触发，提高表单兼容性

## 后续优化计划

1. **增强匹配算法**：
   - 支持更复杂的URL匹配规则，如路径匹配、查询参数匹配等
   - 添加优先级机制，当多个账号匹配同一个域名时，能够智能选择最合适的账号

2. **用户控制**：
   - 添加开关，允许用户控制是否启用自动登录功能
   - 添加白名单/黑名单机制，让用户可以指定哪些网站启用自动登录

3. **安全增强**：
   - 添加密码解密机制，确保密码在传输和存储过程中的安全性
   - 添加用户确认机制，在执行自动登录前可选择是否需要用户确认

4. **日志与分析**：
   - 记录自动登录的成功率和失败原因
   - 提供统计数据，帮助优化自动登录算法 
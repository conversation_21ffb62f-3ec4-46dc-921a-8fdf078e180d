# 数据迁移工具开发日志

日期：2024-07-29
任务：开发数据迁移工具，方便将数据从开发环境复制到生产环境(192.168.202.230)

## 背景

在部署应用到生产环境(192.168.202.230)后，需要频繁将开发环境的数据迁移到生产环境。手动复制文件容易出错且效率低下，因此开发了专门的数据迁移工具。

## 实现方案

开发两个迁移脚本：
1. 基础版：`prepare-migration.js` - 简单迁移数据库文件
2. 高级版：`prepare-migration-advanced.js` - 支持选择性迁移不同类型的文件

### 功能特点

1. **自动准备迁移文件**
   - 将需要迁移的文件复制到临时目录
   - 保持原始目录结构
   - 自动打开目标文件夹方便查看

2. **支持多种文件类型**
   - 数据库文件
   - 账号相关文件
   - 配置文件

3. **提供多种迁移方式**
   - 手动复制（准备好文件后手动操作）
   - 一键复制脚本（提供shell脚本和批处理文件）

4. **详细文档**
   - 自动生成README文件，包含迁移步骤和文件清单
   - 显示每个文件的大小

5. **命令行选项**
   - 支持通过命令行参数选择要迁移的文件类型
   - 默认迁移所有文件

## 使用方法

### 基础版本

```bash
# 使用npm脚本
npm run prepare-migration

# 或直接运行
node scripts/prepare-migration.js
```

### 高级版本

```bash
# 使用npm脚本
npm run prepare-migration:advanced  # 迁移所有文件
npm run migrate:db                  # 只迁移数据库文件
npm run migrate:accounts            # 只迁移账号相关文件
npm run migrate:config              # 只迁移配置文件

# 或直接运行
node scripts/prepare-migration-advanced.js [--all] [--db] [--accounts] [--config]
```

## 迁移过程

1. 运行迁移脚本，生成临时迁移目录
2. 复制所需文件到临时目录
3. 使用提供的脚本一键复制到远程服务器，或手动复制
4. 重启远程服务器上的Docker容器以应用更改

## 注意事项

1. 确保已配置SSH免密登录到目标服务器
2. 迁移前确认目标服务器上的目录结构
3. 迁移后检查文件是否正确复制
4. 根据需要调整配置文件中的环境设置

## 后续优化方向

1. 添加迁移历史记录功能
2. 支持差异化迁移（只迁移变更的文件）
3. 添加迁移前后的数据备份功能
4. 添加迁移完成后的自动验证功能 
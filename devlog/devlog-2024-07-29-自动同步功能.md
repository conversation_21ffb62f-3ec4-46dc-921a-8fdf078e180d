# 自动同步功能开发日志

日期：2024-07-29
任务：开发自动同步功能，实现本地文件变更自动同步到生产服务器(192.168.202.230)

## 背景

在部署应用到生产环境(192.168.202.230)后，需要频繁修改数据文件和配置。每次修改后都需要手动上传文件非常繁琐，因此开发了自动同步功能，通过Docker volumes和文件监控实现文件变更的自动同步。

## 实现方案

1. **Docker Volumes挂载**：
   - 修改Docker配置，使用volumes将本地文件挂载到容器中
   - 支持多种类型文件的挂载，包括数据库文件、配置文件、API文件等

2. **文件变更监控**：
   - 使用chokidar库监控本地文件变化
   - 检测到变化后，自动将文件同步到远程服务器
   - 针对关键文件变更，自动重启Docker容器

3. **远程配置更新**：
   - 开发脚本自动更新远程服务器的Docker配置
   - 确保远程目录结构与本地一致

## 功能特点

1. **全面的文件监控**：
   - 监控多种类型的文件和目录
   - 支持文件的添加、修改、删除操作
   - 支持目录的创建和删除

2. **智能容器重启**：
   - 根据文件类型判断是否需要重启容器
   - 只有关键文件变更才会触发重启，减少不必要的中断

3. **完整的错误处理**：
   - 捕获并显示同步过程中的错误
   - 提供友好的错误提示

4. **灵活的配置**：
   - 可自定义监控的文件和目录
   - 可配置忽略的文件和目录模式

## 监控的文件和目录

1. **数据库和数据文件**：
   - database/**/*
   - data/**/*

2. **配置文件**：
   - lib/config.js
   - chrome-extension/config.js
   - .env*
   - config/**/*

3. **API和后端文件**：
   - api/**/*
   - server/**/*
   - routes/**/*
   - controllers/**/*
   - models/**/*

4. **资源文件**：
   - public/**/*.json
   - public/data/**/*

5. **其他文件**：
   - middleware/**/*
   - utils/**/*
   - hooks/**/*
   - services/**/*
   - lib/**/*.js
   - components/server/**/*

## 使用方法

### 1. 更新远程Docker配置

首先需要更新远程服务器的Docker配置，使其支持volumes挂载：

```bash
npm run update-docker-config
```

这个命令会：
- 将本地的docker-compose-dev.yml上传到远程服务器
- 确保远程服务器上的目录结构正确
- 同步初始文件
- 重启Docker容器

### 2. 启动自动同步

配置更新完成后，启动自动同步功能：

```bash
npm run sync-to-prod
```

此时，脚本会监控本地文件变化，当检测到文件变更时：
- 自动将变更同步到远程服务器
- 对于关键文件（如配置文件、API文件等），自动重启容器
- 在控制台显示同步状态和结果

### 3. 停止同步

按 Ctrl+C 可以停止同步监控。

## 注意事项

1. 确保已配置SSH免密登录到目标服务器
2. 确保远程服务器上的Docker已正确配置
3. 首次使用前，建议先运行`update-docker-config`命令更新远程配置
4. 监控大量文件可能会占用一定系统资源，如果不需要监控某些文件，可以在脚本中调整WATCH_PATHS和IGNORE_PATTERNS

## 后续优化方向

1. 添加增量同步功能，只同步变更的部分
2. 支持多服务器同步
3. 添加Web界面，可视化监控同步状态
4. 添加同步历史记录和回滚功能
5. 支持按需启动和停止监控特定目录 
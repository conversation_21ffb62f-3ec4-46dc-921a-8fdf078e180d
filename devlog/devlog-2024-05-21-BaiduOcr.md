# 百度OCR集成开发日志

**任务**: 将百度OCR识别功能集成到 `aiplatform` 项目中。

**需求**:
1.  OCR配置（API Key, Secret Key等）存储在Nacos中，格式为JSON。
2.  提供一个Controller接口，用于接收图片进行文字识别。
3.  代码实现要参考百度官方文档，并放在`com.sendinfo.aiplatform.boot.chatbot.controller`同级目录下。

**实现方案**:
1.  **创建配置模型**: 定义 `com.sendinfo.aiplatform.boot.chatbot.config.OcrConfigItem.java` 用于映射Nacos中的JSON配置。
2.  **创建请求DTO**: 定义 `com.sendinfo.aiplatform.boot.chatbot.request.OcrRequest.java` 用于API请求。
3.  **创建Service层**: 创建 `OcrManage` 接口和 `OcrManageImpl` 实现。该服务将负责：
    -   通过 `@NacosValue` 从Nacos中读取`ai-platform-baidu-ocr.config`配置。
    -   解析Nacos配置。
    -   管理和缓存百度API的`access_token`，避免重复获取。
    -   调用百度OCR的`general_basic`接口进行文字识别，支持URL和Base64两种方式。
4.  **创建Controller层**: 创建 `OcrController.java`，提供一个`/api/ocr/recognize`的RESTful接口，接收图片数据和`appId`，调用 `OcrManage` 完成识别并返回结果。

**已完成**:
1. ✅ **配置模型**: `OcrConfigItem.java` 已创建，包含 `appId`, `apiKey`, `secretKey` 字段。
2. ✅ **请求DTO**: 
   - `OcrRequest.java` - 通用OCR请求，包含 `appId`, `imageBase64`, `imageUrl`
   - `CaptchaRequest.java` - 插件兼容请求，包含 `imageData`
3. ✅ **响应VO对象**: 
   - `OcrResultVO.java` - OCR识别结果VO，包含 `text`, `confidence` 字段
   - `CaptchaResponseVO.java` - 插件兼容响应VO，包含 `success`, `data`, `message`, `error` 字段
4. ✅ **配置加载器**: 
   - `OcrConfigLoader.java` - 专门的配置加载器，参考 `CacheConfigLoader` 实现
   - 支持从Nacos读取 `ai-platform-baidu-ocr.config` 配置并实时监听变更
   - 提供 `getConfigByAppId()` 和 `getRandomConfig()` 方法
   - 使用线程安全的 `CopyOnWriteArrayList` 存储配置
   - **错误处理**: 支持配置为空或不存在的情况，避免启动失败
   - **容错机制**: 配置解析失败时保持原有配置，不影响服务运行
5. ✅ **Service层**: 
   - `OcrManage` 接口定义了 `recognize()` 和 `recognizeCaptcha()` 方法，返回类型为VO对象
   - `OcrManageImpl` 实现类完成，使用 `OcrConfigLoader` 替代 `@NacosValue` 注解
   - 实现了access_token缓存机制
   - 支持随机选择配置进行负载均衡
   - 添加了 `parseOcrResult()` 工具方法统一解析百度API响应
6. ✅ **Controller层**: 
   - `/api/ocr/recognize` - 插件兼容接口，随机使用配置，返回 `CaptchaResponseVO` 格式与原插件接口一致
   - 移除了通用OCR接口，统一使用插件兼容格式

**Nacos配置格式**:
配置名: `ai-platform-baidu-ocr.config`
内容: 
```json
[
  {
    "appId": "黄山旅游官方平台",
    "apiKey": "sFlQ9hhAyG6wLUCEjyqxbVUg",
    "secretKey": "pauZXDvOkyiVLXljWpbuMks8SjNYGo09"
  },
  {
    "appId": "蜈支洲岛",
    "apiKey": "QmovrRYcGfmU9Ngq1Sg5QFxt",
    "secretKey": "LeWZ9YEjvXGaPD8fEZyLEAT6aXA9p6tB"
  }
]
```

**插件迁移指南**:
插件只需将OCR服务URL从原来的 `http://192.168.202.230/api/ocr/recognize` 修改为新的 `http://新域名/api/ocr/captcha` 即可无缝切换，接口格式完全兼容。 
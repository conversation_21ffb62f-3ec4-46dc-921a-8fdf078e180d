# Nginx静态部署方案开发日志

## 需求背景
需要将React项目打包后部署到Docker Nginx容器中，通过192.168.202.230服务器对外提供服务。

## 技术方案
1. 使用Next.js的静态导出功能生成静态文件
2. 创建Nginx配置文件处理SPA路由和静态资源
3. 使用Docker多阶段构建，将构建过程和部署过程分离
4. 创建部署脚本，自动化部署流程

## 实施步骤

### 1. 创建Nginx配置文件
创建了`nginx.conf`文件，配置了静态文件服务、SPA路由支持和静态资源缓存策略。

### 2. 部署方案演进

#### 初始方案：Docker容器部署
最初计划使用Docker容器部署，包括：
- 创建Dockerfile.nginx：使用多阶段构建
- 创建docker-compose.nginx.yml：配置Docker容器运行参数
- 创建多种部署脚本：支持不同环境

#### 中间方案：直接部署到Nginx
考虑到服务器网络环境不佳，改为本地构建并直接部署到服务器上已安装的Nginx：
- 创建`deploy-nginx-simple.sh`：一个简化的部署脚本
- 本地构建应用，生成静态文件
- 通过SCP上传静态文件和Nginx配置到服务器
- 配置服务器上的Nginx并重启服务
- 增加自动检测和安装Nginx的功能
- 增加多种Nginx配置路径的适配

#### Docker Nginx部署方案
结合前两种方案的优点，采用本地构建 + Docker Nginx部署的方式：
- 创建`deploy-nginx-docker.sh`和`deploy-nginx-docker.ps1`：Docker Nginx部署脚本
- 本地构建应用，生成静态文件
- 创建专用的Nginx配置和Dockerfile
- 通过SCP上传到服务器
- 在服务器上构建Docker镜像并运行容器
- 自动检测和安装Docker（如果需要）
- 提供更好的隔离性和可移植性

#### 解决Windows与Linux行尾符问题
在Windows环境下开发部署脚本时，遇到了行尾符问题：
- Windows使用CRLF（\r\n）作为行尾符
- Linux使用LF（\n）作为行尾符
- 当Windows创建的脚本在Linux上执行时，会出现`$'\r': command not found`错误

#### 最终解决方案：纯服务器端脚本
为了彻底解决行尾符问题和跨平台部署问题，采用了纯服务器端脚本方案：
- 创建`server-setup.sh`：在服务器上直接创建和执行的脚本
- 创建`deploy-react-app.sh`：完整的React应用部署脚本
- 直接在服务器上创建HTML文件、Nginx配置和Docker容器
- 将静态文件复制到Docker容器内部并设置正确的权限
- 解决了403 Forbidden错误问题

## 优势
1. 部署简单，只需静态文件和Nginx配置
2. 资源占用少，不需要Node.js运行时
3. 可扩展性好，易于配置负载均衡和CDN
4. 性能优越，Nginx高效处理静态资源
5. 适应网络环境不佳的情况，本地构建减少服务器负担
6. 部署过程简化，减少中间环节和出错可能
7. 使用Docker提供更好的隔离性和一致性
8. 容器化部署便于横向扩展和迁移
9. 解决了Windows与Linux行尾符问题，实现了跨平台部署

### 3. 解决403 Forbidden错误
部署过程中遇到了403 Forbidden错误，原因是容器内文件权限问题：
- 解决方法1：在容器内设置正确的文件权限
  ```bash
  docker exec account-manage-nginx sh -c "chown -R nginx:nginx /usr/share/nginx/html && chmod -R 755 /usr/share/nginx/html"
  ```
- 解决方法2：不使用卷挂载，而是直接将文件复制到容器内部
  ```bash
  docker cp /tmp/static-files.tar account-manage-nginx:/tmp/
  docker exec account-manage-nginx sh -c "rm -rf /usr/share/nginx/html/* && tar -xf /tmp/static-files.tar -C /usr/share/nginx/html/"
  ```

## 注意事项
1. 如果有API请求，需要在Nginx中配置代理
2. 如果应用需要服务端渲染(SSR)，此方案不适用
3. 环境变量需要在构建时注入，无法在运行时修改
4. Windows环境下部署需要注意行尾符问题，避免使用包含换行符的PowerShell命令
5. 确保SSH密钥配置正确，以便实现无密码部署
6. 在本地构建应用，而不是在Docker中构建，避免构建环境问题
7. 使用`npm install`代替`npm ci`可以提高构建成功率
8. 使用Docker部署时，确保服务器有足够权限安装和运行Docker
9. 如果服务器防火墙开启，需要确保开放相应端口
10. 如果遇到403 Forbidden错误，检查容器内文件权限

## 后续优化方向
1. 添加HTTPS支持
2. 配置自动化CI/CD流程
3. 添加健康检查和监控
4. 优化静态资源加载策略
5. 添加多环境支持（开发、测试、生产）
6. 实现蓝绿部署或金丝雀发布
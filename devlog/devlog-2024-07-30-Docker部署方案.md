# Docker部署方案

## 部署目标

将账号管理系统部署到内网服务器 ***************，使用Docker容器化技术实现快速部署和环境隔离。

## 部署方案

1. 创建Docker配置文件
   - Dockerfile：用于构建Node.js应用镜像
   - docker-compose.yml：用于编排容器服务
   - .dockerignore：排除不需要的文件

2. 部署流程
   - 将项目文件传输到目标服务器
   - 在服务器上构建Docker镜像
   - 使用docker-compose启动服务

## 配置文件详情

### Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

EXPOSE 3001

CMD ["npm", "run", "start"]
```

### docker-compose.yml

```yaml
version: '3'

services:
  account-manage:
    build:
      context: .
    image: account-manage:latest
    container_name: account-manage
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
    volumes:
      - ./database:/app/database
```

### .dockerignore

```
node_modules
.next
out
```

## 部署步骤

### 方案一：使用PowerShell脚本部署（遇到问题）

1. 在本地创建部署脚本 `deploy-to-server.ps1`
2. 执行部署脚本，将项目文件传输到目标服务器
3. 在服务器上构建Docker镜像并启动容器
4. 验证应用是否正常运行

### 方案二：使用批处理文件部署

1. 创建批处理文件 `deploy.bat`
2. 执行批处理文件，打包项目文件
3. 手动将文件上传到服务器并启动Docker容器
4. 验证应用是否正常运行

### 方案三：使用Shell脚本部署（最终方案）

1. 创建Shell脚本 `deploy.sh` 和 `upload-to-server.sh`
2. 通过WSL执行 `upload-to-server.sh` 脚本
3. 脚本自动将文件上传到服务器并执行部署
4. 验证应用是否正常运行

## 注意事项

1. 数据持久化：通过挂载卷保存数据库文件
2. 环境变量：设置NODE_ENV为production
3. 自动重启：配置容器在崩溃时自动重启
4. 端口映射：将容器内的3001端口映射到主机的3001端口

## 部署后验证

1. 访问 http://***************:3001 确认应用是否正常运行
2. 检查容器日志确认没有错误信息
3. 测试账号管理和自动登录功能是否正常工作

## 后续维护

1. 更新应用：重新构建镜像并重启容器
2. 监控容器状态：定期检查容器运行状况
3. 数据备份：定期备份数据库文件

## 部署脚本内容

### deploy.sh

```bash
#!/bin/bash

# 设置变量
APP_DIR="/app/account-manage"
APP_PORT=3001

echo "===== 开始部署账号管理系统 ====="

# 创建应用目录
echo "创建应用目录..."
mkdir -p $APP_DIR
cd $APP_DIR

# 创建Docker配置文件
echo "创建Docker配置文件..."

# 创建Dockerfile
cat > Dockerfile << 'EOF'
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

EXPOSE 3001

CMD ["npm", "run", "start"]
EOF

# 创建docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3'

services:
  account-manage:
    build:
      context: .
    image: account-manage:latest
    container_name: account-manage
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
    volumes:
      - ./database:/app/database
EOF

# 创建.dockerignore
cat > .dockerignore << 'EOF'
node_modules
.next
out
dist
.git
EOF

# 构建并启动Docker容器
echo "构建并启动Docker容器..."
docker-compose up -d --build

# 检查容器是否正常运行
echo "检查容器状态..."
sleep 5
if [ "$(docker ps -q -f name=account-manage)" ]; then
    echo "✅ 容器已成功启动"
    echo "应用现在可以通过 http://$(hostname -I | awk '{print $1}'):$APP_PORT 访问"
else
    echo "❌ 容器启动失败，请检查日志"
    docker logs account-manage
fi

echo "===== 部署完成 ====="
```

### upload-to-server.sh

```bash
#!/bin/bash

# 设置变量
SERVER_IP="***************"
SERVER_USER="root"
REMOTE_DIR="/app/account-manage"
LOCAL_DIR="."

echo "===== 开始准备部署文件 ====="

# 创建临时目录
echo "创建临时目录..."
mkdir -p temp-deploy

# 复制必要的文件到临时目录
echo "复制项目文件..."
cp -r app components lib types database temp-deploy/
cp package.json package-lock.json next.config.js temp-deploy/
cp deploy.sh temp-deploy/

# 创建压缩文件
echo "创建压缩文件..."
cd temp-deploy
tar -czf ../project.tar.gz *
cd ..

# 上传到服务器
echo "上传文件到服务器 $SERVER_IP..."
ssh $SERVER_USER@$SERVER_IP "mkdir -p $REMOTE_DIR"
scp project.tar.gz $SERVER_USER@$SERVER_IP:$REMOTE_DIR/

# 在服务器上解压并执行部署脚本
echo "在服务器上执行部署..."
ssh $SERVER_USER@$SERVER_IP "cd $REMOTE_DIR && tar -xzf project.tar.gz && chmod +x deploy.sh && ./deploy.sh"

# 清理临时文件
echo "清理临时文件..."
rm -rf temp-deploy project.tar.gz

echo "===== 部署过程已启动 ====="
echo "请访问 http://$SERVER_IP:3001 检查应用是否正常运行"
``` 
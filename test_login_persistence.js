const { chromium } = require('playwright');

async function testLoginPersistence() {
    console.log('🚀 启动带持久化配置的浏览器...');
    
    // 使用持久化上下文，会保存cookies和登录状态
    const context = await chromium.launchPersistentContext('./browser_data', {
        headless: false,
        viewport: { width: 1280, height: 720 },
        // 可选：使用系统Chrome
        // channel: 'chrome'
    });
    
    const page = context.pages()[0] || await context.newPage();
    
    console.log('📄 访问工作台页面...');
    await page.goto('https://test-aliuser.lotsmall.cn/usercenter/personal/worktable');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    console.log(`当前URL: ${page.url()}`);
    console.log(`页面标题: ${await page.title()}`);
    
    if (page.url().includes('login')) {
        console.log('🔐 需要登录 - 这是预期的，因为这是第一次运行');
        console.log('请在打开的浏览器中完成登录...');
        console.log('登录后，再次运行此脚本将自动保持登录状态');
        
        // 等待用户手动登录（可选）
        console.log('按 Ctrl+C 停止脚本，或等待自动超时...');
        
        try {
            // 等待跳转到工作台（最多等5分钟）
            await page.waitForURL('**/worktable', { timeout: 300000 });
            console.log('✅ 登录成功！下次运行将自动保持登录状态');
            
            // 现在可以分析页面结构
            await analyzePage(page);
            
        } catch (error) {
            console.log('⏰ 等待超时或用户取消');
        }
    } else {
        console.log('✅ 已保持登录状态！');
        await analyzePage(page);
    }
    
    console.log('浏览器保持打开状态，你可以手动关闭');
}

async function analyzePage(page) {
    console.log('\n🔍 分析页面结构...');
    
    // 查找可能的卡片元素
    const analysis = await page.evaluate(() => {
        const selectors = [
            'div.my-app__item',
            '.my-app__item',
            'div[class*="my-app"]',
            'div[class*="item"]',
            'div[class*="card"]'
        ];
        
        const results = {};
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                results[selector] = {
                    count: elements.length,
                    texts: Array.from(elements).map((el, i) => ({
                        index: i + 1,
                        text: el.textContent.trim().substring(0, 100),
                        hasTarget: el.textContent.includes('分时预约系统')
                    }))
                };
            }
        });
        
        return results;
    });
    
    console.log('\n📋 页面分析结果:');
    Object.entries(analysis).forEach(([selector, data]) => {
        console.log(`\n选择器: ${selector}`);
        console.log(`数量: ${data.count}`);
        
        data.texts.forEach(item => {
            const marker = item.hasTarget ? '🎯' : '⚪';
            console.log(`  ${marker} ${item.index}. ${item.text}...`);
        });
    });
    
    // 查找目标卡片
    const targetCards = Object.entries(analysis).flatMap(([selector, data]) => 
        data.texts.filter(item => item.hasTarget).map(item => ({
            selector: `${selector}:nth-child(${item.index})`,
            position: item.index,
            text: item.text
        }))
    );
    
    if (targetCards.length > 0) {
        console.log('\n🎉 找到目标卡片:');
        targetCards.forEach(card => {
            console.log(`📍 选择器: ${card.selector}`);
            console.log(`📝 文本: ${card.text}...`);
        });
    } else {
        console.log('\n❌ 未找到包含"分时预约系统"的卡片');
    }
}

// 运行测试
testLoginPersistence().catch(console.error); 
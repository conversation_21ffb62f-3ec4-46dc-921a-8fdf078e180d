// 账号类型常量定义
// 分离归属权限和业务类型两个维度

// 归属类型：决定谁能看到和编辑账号
export const OWNERSHIP_TYPES = {
  PUBLIC: 0,   // 公共账号 - 所有人可见（受权限控制）
  PRIVATE: 1,  // 个人账号 - 仅创建者可见
} as const;

export const OWNERSHIP_TYPE_LABELS = {
  [OWNERSHIP_TYPES.PUBLIC]: '公共账号',
  [OWNERSHIP_TYPES.PRIVATE]: '个人账号',
} as const;

export const OWNERSHIP_TYPE_COLORS = {
  [OWNERSHIP_TYPES.PUBLIC]: 'text-blue-600 bg-blue-50',
  [OWNERSHIP_TYPES.PRIVATE]: 'text-green-600 bg-green-50',
} as const;

// 业务类型：账号的业务用途分类
export const BUSINESS_TYPES = {
  UNCATEGORIZED: 0,  // 其他账号
  SHOP: 1,          // 店铺账号
  CUSTOMER: 2,      // 散客账号
  TEST: 3,          // 测试账号 (保留但不在主标签显示)
  SERVICE: 4,       // 客服账号 (保留但不在主标签显示)
  DEPLOY: 5,        // 部署账号
  LOG: 6,           // log账号
  SQL: 7,           // sql账号
} as const;

export const BUSINESS_TYPE_LABELS = {
  [BUSINESS_TYPES.UNCATEGORIZED]: '其他账号',
  [BUSINESS_TYPES.SHOP]: '店铺账号',
  [BUSINESS_TYPES.CUSTOMER]: '散客账号', 
  [BUSINESS_TYPES.TEST]: '测试账号',
  [BUSINESS_TYPES.SERVICE]: '客服账号',
  [BUSINESS_TYPES.DEPLOY]: '部署账号',
  [BUSINESS_TYPES.LOG]: 'log账号',
  [BUSINESS_TYPES.SQL]: 'sql账号',
} as const;

export const BUSINESS_TYPE_COLORS = {
  [BUSINESS_TYPES.UNCATEGORIZED]: 'text-gray-600 bg-gray-50',
  [BUSINESS_TYPES.SHOP]: 'text-emerald-600 bg-emerald-50',
  [BUSINESS_TYPES.CUSTOMER]: 'text-blue-600 bg-blue-50',
  [BUSINESS_TYPES.TEST]: 'text-purple-600 bg-purple-50',
  [BUSINESS_TYPES.SERVICE]: 'text-red-600 bg-red-50',
  [BUSINESS_TYPES.DEPLOY]: 'text-yellow-600 bg-yellow-50',
  [BUSINESS_TYPES.LOG]: 'text-orange-600 bg-orange-50',
  [BUSINESS_TYPES.SQL]: 'text-indigo-600 bg-indigo-50',
} as const;

// Tab配置：主要切换维度是归属类型
export const OWNERSHIP_TABS = [
  {
    key: 'private',
    label: '个人账号',
    value: OWNERSHIP_TYPES.PRIVATE,
    icon: '👤',
    color: 'text-green-600 border-green-300',
    activeColor: 'text-green-600 border-green-600 bg-green-50',
  },
  {
    key: 'public',
    label: '公共账号',
    value: OWNERSHIP_TYPES.PUBLIC,
    icon: '🌐',
    color: 'text-blue-600 border-blue-300',
    activeColor: 'text-blue-600 border-blue-600 bg-blue-50',
  },
  {
    key: 'all',
    label: '全部账号',
    value: null,
    icon: '📋',
    color: 'text-gray-700 border-gray-300',
    activeColor: 'text-blue-600 border-blue-600 bg-blue-50',
  },
] as const;

export type OwnershipType = typeof OWNERSHIP_TYPES[keyof typeof OWNERSHIP_TYPES];
export type BusinessType = typeof BUSINESS_TYPES[keyof typeof BUSINESS_TYPES]; 
@echo off
chcp 65001 >nul

echo 🔧 开始部署 Account Manager (服务模式)...
echo 解决API接口在静态构建后无法使用的问题

REM 1. 构建Docker镜像
echo 📦 构建Docker镜像...
docker build -t account-manage:latest .

REM 2. 停止现有容器
echo 🛑 停止现有容器...
docker-compose down

REM 3. 启动服务
echo 🚀 启动服务...
docker-compose up -d

REM 4. 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

REM 5. 检查服务状态
echo 🔍 检查服务状态...
docker-compose ps

REM 6. 测试API接口
echo 🧪 测试API接口...
timeout /t 5 /nobreak >nul

REM 测试健康检查接口
curl -f http://localhost:3001/api/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Next.js服务运行正常
) else (
    echo ❌ Next.js服务启动失败
    docker-compose logs account-manage
    pause
    exit /b 1
)

REM 测试强制登录接口
curl -f http://localhost/api/account/update-force-login -X POST -H "Content-Type: application/json" -d "{\"accountId\":1,\"forceLogin\":true}" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ API代理工作正常
) else (
    echo ❌ API代理配置有问题
    docker-compose logs nginx
    pause
    exit /b 1
)

echo 🎉 部署完成！
echo 📱 访问地址: http://localhost
echo 🔧 API地址: http://localhost/api
echo 📊 服务状态: docker-compose ps
echo 📋 查看日志: docker-compose logs -f
pause 
# 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制依赖文件
COPY package*.json ./
# 使用npm install代替npm ci，并添加额外的参数以提高可靠性
RUN npm install --no-audit --no-fund --loglevel=error

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 部署阶段
FROM nginx:alpine

# 复制Nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 从构建阶段复制构建产物到Nginx服务目录
COPY --from=builder /app/out /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"] 
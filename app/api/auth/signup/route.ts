import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// 创建Supabase Admin客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://192.168.202.230:8000';
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json({ 
        success: false, 
        message: '邮箱和密码不能为空' 
      }, { status: 400 });
    }

    // 使用Admin API创建用户，跳过邮件确认
    const { data, error } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // 自动确认邮箱
      user_metadata: {
        created_via: 'custom_signup'
      }
    });

    if (error) {
      console.error('注册失败:', error);
      return NextResponse.json({ 
        success: false, 
        message: error.message 
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: '注册成功！',
      user: {
        id: data.user.id,
        email: data.user.email
      }
    });

  } catch (error: any) {
    console.error('注册API错误:', error);
    return NextResponse.json({ 
      success: false, 
      message: '注册失败，请稍后重试' 
    }, { status: 500 });
  }
} 
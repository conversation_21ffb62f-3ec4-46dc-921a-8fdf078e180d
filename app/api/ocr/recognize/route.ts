import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import axios from 'axios';

// 创建 Supabase 客户端实例
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://192.168.202.230:8000';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseKey);

// 定义错误响应函数，添加 CORS 头
function errorResponse(message: string, code: string, status: number = 400) {
  return NextResponse.json(
    {
      success: false,
      error: {
        code,
        message
      }
    },
    { 
      status,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    }
  );
}

// 处理 OPTIONS 请求（预检请求）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}

// 获取百度 OCR 的 access_token
async function getBaiduAccessToken(config: any) {
  try {
    const url = `https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${config.api_key}&client_secret=${config.secret_key}`;
    
    const response = await axios.post(url);
    
    if (!response.data || !response.data.access_token) {
      throw new Error('Failed to get Baidu access token');
    }
    
    return response.data.access_token;
  } catch (error) {
    console.error('Error getting Baidu access token:', error);
    throw error;
  }
}

// 使用百度 OCR API 识别验证码图片
async function recognizeCaptcha(imageBase64: string, config: any) {
  try {
    // 1. 获取 access_token
    const accessToken = await getBaiduAccessToken(config);
    
    // 2. 准备图片数据
    // 移除 Base64 数据的前缀（如 "data:image/png;base64,"）
    let base64Data = imageBase64;
    console.log('原始图片数据类型:', imageBase64.substring(0, 50) + '...');
    
    try {
      if (base64Data.includes(',')) {
        base64Data = base64Data.split(',')[1];
      }
      
      // 检查base64数据是否有效
      if (!base64Data || base64Data.trim() === '') {
        throw new Error('Base64数据为空');
      }
      
      // 尝试解码Base64以验证其有效性
      try {
        atob(base64Data);
      } catch (e) {
        console.error('Base64解码失败:', e);
        throw new Error('无效的Base64数据');
      }
    } catch (error) {
      console.error('处理Base64数据时出错:', error);
      throw error;
    }
    
    // 3. 调用通用文字识别 API
    const url = `https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic?access_token=${accessToken}`;
    
    console.log('准备调用百度OCR API...');
    
    let response;
    try {
      // 准备请求参数
      const params = new URLSearchParams();
      params.append('image', base64Data);
      
      // 添加可选参数以提高识别率
      params.append('detect_direction', 'true'); // 检测图像方向
      params.append('probability', 'true'); // 返回识别结果中每一行的置信度
      params.append('language_type', 'CHN_ENG'); // 中英文混合
      
      response = await axios.post(url, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });
      
      console.log('百度OCR API响应状态:', response.status);
      console.log('百度OCR API响应数据:', JSON.stringify(response.data).substring(0, 200) + '...');
    } catch (error) {
      console.error('调用百度OCR API失败:', error);
      throw error;
    }
    
    // 4. 解析识别结果
    if (!response.data || !response.data.words_result) {
      throw new Error('Invalid OCR response format');
    }
    
    // 提取识别到的文本
    const results = response.data.words_result;
    if (results.length === 0) {
      return { text: '', confidence: 0 };
    }
    
    // 拼接所有识别到的文本（通常验证码只有一行）
    const text = results.map((item: any) => item.words).join('');
    
    // 返回识别结果
    return {
      text,
      confidence: response.data.words_result_num > 0 ? 0.9 : 0 // 简单设置一个置信度
    };
  } catch (error) {
    console.error('Error recognizing captcha:', error);
    throw error;
  }
}

// 处理 POST 请求
export async function POST(request: Request) {
  try {
    // 1. 解析请求体
    const body = await request.json();
    
    // 2. 验证请求参数
    if (!body.imageData) {
      return errorResponse(
        '缺少必要的参数: imageData', 
        'MISSING_PARAMETER'
      );
    }
    
    // 3. 从 Supabase 获取百度 OCR 配置
    let configs;
    try {
      // 直接从数据库获取配置
      const { data, error } = await supabase
        .from('ocr_configs')
        .select('*')
        .eq('service_name', 'baidu_ocr');
      
      if (error) {
        console.error('Error fetching Baidu OCR configs:', error);
        throw error;
      }
      
      configs = data || [];
      
      if (!configs || configs.length === 0) {
        return errorResponse(
          '未找到有效的 OCR 配置', 
          'NO_OCR_CONFIG_FOUND',
          500
        );
      }
    } catch (error) {
      console.error('获取 OCR 配置失败:', error);
      return errorResponse(
        'OCR 配置获取失败，请稍后重试', 
        'SUPABASE_CONFIG_ERROR',
        500
      );
    }
    
    // 4. 随机选择一个配置
    const randomIndex = Math.floor(Math.random() * configs.length);
    const config = configs[randomIndex];
    console.log(`使用配置 ID: ${config.id}`); // 记录使用的配置 ID，方便排查问题
    
    // 5. 调用百度 OCR API 进行识别
    try {
      const result = await recognizeCaptcha(body.imageData, config);
      
      // 6. 返回识别结果，添加 CORS 头
      return NextResponse.json({
        success: true,
        data: result,
        message: 'OCR 识别成功。'
      }, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      });
    } catch (error: any) {
      console.error('OCR 识别失败:', error);
      return errorResponse(
        `验证码识别失败: ${error.message || '未知错误'}`, 
        'OCR_FAILED',
        500
      );
    }
  } catch (error: any) {
    console.error('处理请求时发生错误:', error);
    return errorResponse(
      `服务器内部错误: ${error.message || '未知错误'}`, 
      'SERVER_ERROR',
      500
    );
  }
} 
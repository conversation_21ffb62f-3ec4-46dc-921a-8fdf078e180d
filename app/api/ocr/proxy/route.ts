import { NextResponse, NextRequest } from 'next/server';

// 代理获取图片API
export async function GET(request: NextRequest) {
  try {
    // 从URL参数中获取目标URL
    const targetUrl = request.nextUrl.searchParams.get('url');

    if (!targetUrl) {
      return NextResponse.json(
        { error: '缺少url参数' },
        { status: 400 }
      );
    }

    console.log('代理请求图片:', targetUrl);

    // 发送请求获取图片
    const response = await fetch(targetUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });

    if (!response.ok) {
      console.error(`代理请求失败: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `代理请求失败: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    // 获取图片内容
    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/jpeg';

    // 返回图片内容
    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400',
      },
    });
  } catch (error: any) {
    console.error('代理请求出错:', error);
    return NextResponse.json(
      { error: `代理请求出错: ${error.message}` },
      { status: 500 }
    );
  }
} 
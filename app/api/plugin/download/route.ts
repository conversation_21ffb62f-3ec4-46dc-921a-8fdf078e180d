import { NextResponse, NextRequest } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// 获取 Supabase 客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://192.168.202.230:8000';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseKey);

// 处理插件下载请求
export async function GET(request: NextRequest) {
  try {
    const pluginId = request.nextUrl.searchParams.get('id');
    
    if (!pluginId) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数: id' },
        { status: 400 }
      );
    }
    
    // 从数据库获取插件信息
    const { data: pluginData, error: pluginError } = await supabase
      .from('plugins')
      .select('*')
      .eq('id', pluginId)
      .single();
    
    if (pluginError || !pluginData) {
      console.error('获取插件信息失败:', pluginError);
      return NextResponse.json(
        { success: false, error: '插件不存在或已被删除' },
        { status: 404 }
      );
    }
    
    // 使用公共URL而不是签名URL
    let downloadUrl = '';
    
    // 如果插件数据中已经有public_url字段，直接使用
    if (pluginData.public_url) {
      downloadUrl = pluginData.public_url;
    } else {
      // 否则构造公共URL
      const { data: publicUrlData } = supabase
        .storage
        .from('account-manage-bucket')
        .getPublicUrl(pluginData.file_path);
      
      downloadUrl = publicUrlData?.publicUrl || '';
    }
    
    if (!downloadUrl) {
      console.error('获取下载链接失败');
      return NextResponse.json(
        { success: false, error: '获取下载链接失败' },
        { status: 500 }
      );
    }
    
    // 返回下载链接
    return NextResponse.json({
      success: true,
      data: {
        download_url: downloadUrl,
        plugin: pluginData
      }
    });
    
  } catch (error: any) {
    console.error('处理插件下载请求时出错:', error);
    return NextResponse.json(
      { success: false, error: `服务器错误: ${error.message}` },
      { status: 500 }
    );
  }
} 
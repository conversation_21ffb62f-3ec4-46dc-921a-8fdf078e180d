import { NextResponse, NextRequest } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// 获取 Supabase 客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://192.168.202.230:8000';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseKey);

// 处理插件删除请求
export async function DELETE(request: NextRequest) {
  try {
    const pluginId = request.nextUrl.searchParams.get('id');
    
    if (!pluginId) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数: id' },
        { status: 400 }
      );
    }
    
    // 从数据库获取插件信息
    const { data: pluginData, error: pluginError } = await supabase
      .from('plugins')
      .select('file_path')
      .eq('id', pluginId)
      .single();
    
    if (pluginError || !pluginData) {
      console.error('获取插件信息失败:', pluginError);
      return NextResponse.json(
        { success: false, error: '插件不存在或已被删除' },
        { status: 404 }
      );
    }
    
    // 从存储中删除文件
    const { error: storageError } = await supabase
      .storage
      .from('account-manage-bucket')
      .remove([pluginData.file_path]);
    
    if (storageError) {
      console.error('删除存储文件失败:', storageError);
      // 即使文件删除失败，我们也继续删除数据库记录
      console.warn('将继续删除数据库记录...');
    }
    
    // 从数据库中删除记录
    const { error: dbError } = await supabase
      .from('plugins')
      .delete()
      .eq('id', pluginId);
    
    if (dbError) {
      console.error('删除数据库记录失败:', dbError);
      return NextResponse.json(
        { success: false, error: `删除插件记录失败: ${dbError.message}` },
        { status: 500 }
      );
    }
    
    // 返回成功响应
    return NextResponse.json({
      success: true,
      data: { message: '插件已成功删除' }
    });
    
  } catch (error: any) {
    console.error('处理插件删除请求时出错:', error);
    return NextResponse.json(
      { success: false, error: `服务器错误: ${error.message}` },
      { status: 500 }
    );
  }
} 
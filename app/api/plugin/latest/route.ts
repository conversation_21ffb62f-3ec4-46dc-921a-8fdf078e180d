import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// 获取 Supabase 客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://192.168.202.230:8000';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseKey);

// 获取最新上传的插件
export async function GET(request: Request) {
  try {
    // 从数据库获取最新插件
    const { data, error } = await supabase
      .from('plugins')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
    
    console.log('查询结果:', { data, error });
    
    if (error) {
      console.error('获取最新插件失败:', error);
      return NextResponse.json(
        { success: false, error: `获取最新插件失败: ${error.message}` },
        { status: 500 }
      );
    }
    
    if (!data || data.length === 0) {
      return NextResponse.json(
        { success: false, error: '没有找到任何插件' },
        { status: 404 }
      );
    }
    
    // 确保有公共URL
    let plugin = data[0];
    if (!plugin.public_url) {
      const { data: publicUrlData } = supabase
        .storage
        .from('account-manage-bucket')
        .getPublicUrl(plugin.file_path);
      
      plugin.public_url = publicUrlData?.publicUrl || '';
    }
    
    // 返回最新插件信息
    return NextResponse.json({
      success: true,
      data: plugin
    });
    
  } catch (error: any) {
    console.error('处理获取最新插件请求时出错:', error);
    return NextResponse.json(
      { success: false, error: `服务器错误: ${error.message}` },
      { status: 500 }
    );
  }
} 
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// 获取 Supabase 客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://192.168.202.230:8000';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseKey);

// 获取插件列表
export async function GET(request: Request) {
  try {
    // 从数据库获取插件列表
    const { data, error } = await supabase
      .from('plugins')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('获取插件列表失败:', error);
      return NextResponse.json(
        { success: false, error: `获取插件列表失败: ${error.message}` },
        { status: 500 }
      );
    }

    // 返回插件列表
    return NextResponse.json({
      success: true,
      data
    });
  } catch (error: any) {
    console.error('处理获取插件列表请求时出错:', error);
    return NextResponse.json(
      { success: false, error: `服务器错误: ${error.message}` },
      { status: 500 }
    );
  }
} 
import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 处理根据登录链接查询账号的请求
export async function POST(request: Request) {
  try {
    // 解析请求体
    const data = await request.json();
    const { loginUrl } = data;
    
    if (!loginUrl) {
      return NextResponse.json({ 
        success: false, 
        message: '缺少登录链接参数' 
      }, { status: 400 });
    }
    
    console.log(`收到根据登录链接查询账号请求: loginUrl=${loginUrl}`);
    
    // 提取域名部分，用于模糊匹配
    const urlObj = new URL(loginUrl);
    const domain = urlObj.hostname;
    console.log(`提取的域名: ${domain}`);
    
    // 查询账号 - 首先尝试精确匹配
    console.log('开始精确匹配查询...');
    const { data: exactMatches, error: exactError } = await supabase
      .from('accounts')
      .select('*')
      .eq('login_url', loginUrl)
      .limit(1);
    
    console.log('精确匹配查询结果:', { 
      matchCount: exactMatches?.length || 0, 
      error: exactError 
    });
    
    if (exactError) {
      console.error('精确匹配查询失败:', exactError);
      return NextResponse.json({ 
        success: false, 
        message: `查询失败: ${exactError.message}` 
      }, { status: 500 });
    }
    
    // 如果找到精确匹配的账号
    if (exactMatches && exactMatches.length > 0) {
      const account = exactMatches[0];
      console.log(`找到精确匹配的账号: ${account.name}`);
      
      return NextResponse.json({
        success: true,
        message: '找到匹配的账号',
        account: account  // 返回完整账号数据，包括密码用于自动登录
      });
    }
    
    // 如果没有精确匹配，尝试模糊匹配域名
    console.log(`精确匹配未找到账号，尝试使用域名匹配: domain=${domain}`);
    
    const { data: domainMatches, error: domainError } = await supabase
      .from('accounts')
      .select('*')
      .ilike('login_url', `%${domain}%`)
      .limit(1);
    
    console.log('域名匹配查询结果:', { 
      matchCount: domainMatches?.length || 0, 
      error: domainError 
    });
    
    if (domainError) {
      console.error('域名匹配查询失败:', domainError);
      return NextResponse.json({ 
        success: false, 
        message: `查询失败: ${domainError.message}` 
      }, { status: 500 });
    }
    
    // 如果找到域名匹配的账号
    if (domainMatches && domainMatches.length > 0) {
      const account = domainMatches[0];
      console.log(`找到域名匹配的账号: ${account.name}`);
      
      return NextResponse.json({
        success: true,
        message: '找到匹配的账号',
        account: account  // 返回完整账号数据，包括密码用于自动登录
      });
    }
    
    // 如果没有找到匹配的账号，返回空结果
    console.log('没有找到匹配的账号');
    return NextResponse.json({
      success: false,
      message: '没有找到匹配的账号'
    });
  } catch (error: any) {
    console.error('处理根据登录链接查询账号请求失败:', error);
    return NextResponse.json({ 
      success: false, 
      message: `处理请求失败: ${error.message}` 
    }, { status: 500 });
  }
}

// 处理预检请求
export async function OPTIONS() {
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      }
    }
  );
} 
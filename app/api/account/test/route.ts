import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 测试数据库连接和查询账号数据
export async function GET() {
  try {
    console.log('开始测试数据库连接...');
    
    // 1. 测试基本连接
    const { data: connectionTest, error: connectionError } = await supabase
      .from('accounts')
      .select('count(*)', { count: 'exact' });
    
    if (connectionError) {
      console.error('数据库连接失败:', connectionError);
      return NextResponse.json({
        success: false,
        message: '数据库连接失败',
        error: connectionError.message
      });
    }
    
    console.log('数据库连接成功');
    
    // 2. 查询所有账号数据
    const { data: allAccounts, error: queryError } = await supabase
      .from('accounts')
      .select('*')
      .limit(10);
    
    if (queryError) {
      console.error('查询账号失败:', queryError);
      return NextResponse.json({
        success: false,
        message: '查询账号失败',
        error: queryError.message
      });
    }
    
    console.log(`查询到 ${allAccounts?.length || 0} 条账号记录`);
    
    // 3. 测试特定域名查询
    const testDomains = ['user.lotsmall.cn', 'wap.lotsmall.cn', 'lotsmall.cn'];
    const domainResults: Record<string, number> = {};
    
    for (const domain of testDomains) {
      const { data: domainAccounts, error: domainError } = await supabase
        .from('accounts')
        .select('*')
        .ilike('login_url', `%${domain}%`);
      
      if (!domainError) {
        domainResults[domain] = domainAccounts?.length || 0;
        console.log(`域名 ${domain} 匹配到 ${domainAccounts?.length || 0} 条记录`);
      }
    }
    
    return NextResponse.json({
      success: true,
      message: '数据库测试完成',
      data: {
        totalAccounts: allAccounts?.length || 0,
        accounts: allAccounts?.map(account => ({
          id: account.id,
          name: account.name,
          login_url: account.login_url,
          username: account.username
        })) || [],
        domainMatches: domainResults
      }
    });
    
  } catch (error: any) {
    console.error('测试API失败:', error);
    return NextResponse.json({
      success: false,
      message: '测试失败',
      error: error.message
    });
  }
}

// 处理预检请求
export async function OPTIONS() {
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      }
    }
  );
} 
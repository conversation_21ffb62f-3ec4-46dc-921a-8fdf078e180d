'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import TitleBar from '../components/TitleBar';
import Sidebar from '../components/Sidebar';
import AccountSection from '../components/AccountSection';
import SearchBar from '../components/SearchBar';
import AccountModal from '../components/AccountModal';
import ConfigModal from '../components/ConfigModal';
import AuthWrapper from '../components/AuthWrapper';
import AccountTabs from '../components/AccountTabs';
import { OWNERSHIP_TYPES, BUSINESS_TYPES, type OwnershipType, type BusinessType } from '../constants/accountTypes';
import '../lib/debug'; // 导入调试工具

// 为登录状态定义一个类型
type LoginStatus = {
  [accountId: string]: {
    status: 'loading' | 'success' | 'error';
    message: string;
  };
};

export default function Home() {
  const [environments, setEnvironments] = useState<any[]>([]);
  const [selectedEnvironment, setSelectedEnvironment] = useState<any | null>(null);
  const [accounts, setAccounts] = useState<any[]>([]);
  const [filteredAccounts, setFilteredAccounts] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<any | null>(null);
  const [loginStatus, setLoginStatus] = useState<LoginStatus>({});
  const [loading, setLoading] = useState(true);
  const [selectedOwnershipType, setSelectedOwnershipType] = useState<OwnershipType | null>(OWNERSHIP_TYPES.PRIVATE);
  const [selectedBusinessType, setSelectedBusinessType] = useState<BusinessType | null>(null);
  
  // 注册检查逻辑已移除

  // 加载环境数据
  useEffect(() => {
    loadEnvironments();
  }, []);

  // 加载账号数据
  useEffect(() => {
    if (selectedEnvironment) {
      loadAccounts();
    }
  }, [selectedEnvironment]);

  // 搜索和类型筛选
  useEffect(() => {
    let filtered = accounts;
    
    // 按归属类型筛选
    if (selectedOwnershipType !== null) {
      filtered = filtered.filter(account => 
        (account.ownership_type || 0) === selectedOwnershipType
      );
    }
    
    // 按业务类型筛选
    if (selectedBusinessType !== null) {
      filtered = filtered.filter(account => 
        (account.business_type || 0) === selectedBusinessType
      );
    }
    
    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(account =>
        account.name.toLowerCase().includes(query) ||
        account.login_url.toLowerCase().includes(query) ||
        account.username.toLowerCase().includes(query) ||
        (account.description && account.description.toLowerCase().includes(query))
      );
    }
    
    setFilteredAccounts(filtered);
  }, [searchQuery, accounts, selectedOwnershipType, selectedBusinessType]);

  const loadEnvironments = async () => {
    try {
      setLoading(true);
      const { getEnvironments } = await import('../lib/supabase.js');
      let envs = await getEnvironments();
      
      // 不再自动创建默认环境，只使用已有环境
      setEnvironments(envs);
      if (envs.length > 0) {
        setSelectedEnvironment(envs[0]);
      }
    } catch (error: any) {
      console.error('加载环境失败:', error);
      // 移除演示数据逻辑，如果加载失败就显示空列表
      setEnvironments([]);
    } finally {
      setLoading(false);
    }
  };

  const loadAccounts = async () => {
    try {
      const { getAccounts } = await import('../lib/supabase.js');
      const accs = await getAccounts(selectedEnvironment?.id);
      setAccounts(accs);
    } catch (error) {
      console.error('加载账号失败:', error);
    }
  };

  const performSearch = async () => {
    try {
      const { searchAccounts } = await import('../lib/supabase.js');
      const results = await searchAccounts(searchQuery, selectedEnvironment?.id);
      setFilteredAccounts(results);
    } catch (error) {
      console.error('搜索失败:', error);
    }
  };

  const handleLogin = async (account: any) => {
    // 更新账号最后访问时间
    try {
      const { updateAccountLastAccess } = await import('../lib/supabase.js');
      await updateAccountLastAccess(account.id);
    } catch (error) {
      console.warn('更新最后访问时间失败:', error);
    }
    
    // 根据登录方式选择不同的处理逻辑
    const loginType = account.login_type || 'custom';
    
    if (loginType === 'automa') {
      return handleAutomaLogin(account);
    } else {
      return handleCustomLogin(account);
    }
  };

  const handleAutomaLogin = async (account: any) => {
    console.log('=== Automa登录调试信息开始 ===');
    console.log('1. 账号信息:', account);
    console.log('2. 工作流ID:', account.automa_workflow_id);
    
    if (!account.automa_workflow_id) {
      console.log('❌ 未配置Automa工作流ID');
      alert('该账号未配置Automa工作流程ID，请联系管理员配置');
      return;
    }

    setLoginStatus(prev => ({
      ...prev,
      [account.id]: { status: 'loading', message: '准备启动Automa工作流...' }
    }));

    try {
      // 检查Automa插件是否可用
      if (typeof window.dispatchEvent !== 'function' || typeof window.CustomEvent !== 'function') {
        console.error('❌ 浏览器不支持或Automa插件未启用');
        setLoginStatus(prev => ({
          ...prev,
          [account.id]: { status: 'error', message: 'Automa插件未安装或未启用' }
        }));
        return;
      }

      console.log('3. 准备执行Automa工作流...');
      
      // 构建新的入参规范格式
      let inParams = {
        // === 核心登录信息 ===
        loginUrl: account.login_url,
        redirectUrl: account.redirect_url || '',
        credentials: {
          username: account.username,
          password: account.encrypted_password
        },
        
        // === 选择器配置 ===
        selectorMode: account.selector_mode || 'css',
        selectors: {
          username: account.username_selector || account.username_xpath || '',
          password: account.password_selector || account.password_xpath || '',
          captcha: account.captcha_selector || account.captcha_input_xpath || '',
          captchaImage: account.captcha_image_selector || account.captcha_xpath || '',
          captchaRefresh: account.captcha_refresh_selector || '',
          loginButton: account.login_button_selector || account.login_button_xpath || ''
        },
        
        // === 登录后操作 ===
        postLoginAction: {
          type: account.post_action_type || 'click_app_card',
          targetText: account.post_action_target || account.workspace_name || ''
        },
        
        // === 高级配置 ===
        options: {
          maxRetries: account.max_retries || 3,
          showProgress: account.show_progress !== false,
          skipPostAction: account.skip_post_action || false,
          forceLogin: account.force_login || false,
          ocrConfig: {
            url: account.ocr_config_url || 'https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize',
            headers: {
              Cookie: 'tr_de_id=ZHYtSEhOUlZRWkVXSFZI'
            }
          }
        },
        
        // === 元信息 ===
        accountName: account.name,
        accountId: account.id,
        businessType: account.business_type || 0,
        updatedAt: account.updated_at
      };

      // === 部署账号专用：添加oneTest配置 ===
      if (account.business_type === 5) { // BUSINESS_TYPES.DEPLOY
        inParams.oneTest = {
          task: {
            id: account.task_id || '',
            filter: account.platform_name || 'ai-platform'
          },
          urls: {
            login: "http://devops.sendinfo.nb/v1/login-by-ldap",
            search: "http://devops.sendinfo.nb/test-ops/v1/task/app/search",
            package: "http://devops.sendinfo.nb/test-ops/v1/task/package",
            redirectBase: "http://one-test.sendinfo.com/#/task-detail"
          },
          options: {
            withRelease: true,
            isIdcJenkins: false
          }
        };
        console.log('✅ 部署账号已添加oneTest配置:', inParams.oneTest);
      }

      // 创建自定义事件触发Automa工作流
      const event = new CustomEvent('automa:execute-workflow', {
        detail: {
          publicId: account.automa_workflow_id,

          data: {
            variables:{
              loginConfig:{
                ...inParams
              }
            }
    
          }
        }
      });
      
      window.dispatchEvent(event);
      console.log('✅ Automa工作流触发成功');
      
      setLoginStatus(prev => ({
        ...prev,
        [account.id]: { status: 'success', message: 'Automa工作流已启动' }
      }));
      
    } catch (error: any) {
      console.error('❌ Automa工作流触发失败:', error);
      setLoginStatus(prev => ({
        ...prev,
        [account.id]: { status: 'error', message: `Automa工作流启动失败: ${error.message}` }
      }));
    }
    
    console.log('=== Automa登录调试信息结束 ===');
    
    // 5秒后清除状态
    setTimeout(() => {
      setLoginStatus(prev => {
        const newStatus = { ...prev };
        delete newStatus[account.id];
        return newStatus;
      });
    }, 5000);
  };

  const handleCustomLogin = async (account: any) => {
    // 从localStorage获取插件ID
    const extensionId = localStorage.getItem('chromeExtensionId');
    
    console.log('=== 登录调试信息开始 ===');
    console.log('1. 获取到的插件ID:', extensionId);
    console.log('2. 当前页面URL:', window.location.href);
    console.log('3. User Agent:', navigator.userAgent);
    console.log('4. 是否为Chrome浏览器:', navigator.userAgent.includes('Chrome'));
    
    // 如果没有配置插件ID，不再强制要求配置，直接跳过插件登录
    if (!extensionId) {
      console.log('❌ 未获取到插件ID，跳转到登录页面');
      window.open(account.login_url, '_blank');
      setLoginStatus(prev => ({ 
        ...prev, 
        [account.id]: { 
          status: 'success', 
          message: '已打开登录页面，请手动登录' 
        } 
      }));
      return;
    }

    console.log('5. Chrome对象是否存在:', typeof chrome !== 'undefined');
    console.log('6. Chrome.runtime是否存在:', typeof chrome !== 'undefined' && !!chrome.runtime);
    
    setLoginStatus(prev => ({
      ...prev,
      [account.id]: { status: 'loading', message: '准备登录...' }
    }));

    // 直接尝试使用Chrome插件进行自动登录
    try {
      setLoginStatus(prev => ({ ...prev, [account.id]: { status: 'loading', message: '准备登录数据...' }}));
      
      // 准备登录数据 - 使用新字段，向后兼容旧字段
      let loginData = {
        loginUrl: account.login_url,
        username: account.username,
        password: account.encrypted_password,
        
        // 选择器字段 - 优先使用新字段，向后兼容旧字段
        usernameXpath: account.username_selector || account.username_xpath || '',
        passwordXpath: account.password_selector || account.password_xpath || '',
        captchaXpath: account.captcha_image_selector || account.captcha_xpath || '',
        loginButtonXpath: account.login_button_selector || account.login_button_xpath || '',
        captchaInputXpath: account.captcha_selector || account.captcha_input_xpath || '',
        
        // 新增字段
        selectorMode: account.selector_mode || 'css',
        captchaRefreshXpath: account.captcha_refresh_selector || '',
        
        // 登录后操作
        postActionType: account.post_action_type || '',
        postActionTarget: account.post_action_target || account.workspace_name || '',
        
        // 强制登录设置
        forceLogin: account.force_login || false,
        
        // 高级配置
        maxRetries: account.max_retries || 3,
        showProgress: account.show_progress !== false,
        skipPostAction: account.skip_post_action || false,
        ocrConfigUrl: account.ocr_config_url || ''
      };

      // one-test账号类型特殊参数组装
      if (account.login_type === 'one-test') {
        loginData = {
          ...loginData,
          oneTest: {
            task: {
              id: account.task_id,
              filter: account.platform_name
            },
            urls: {
              login: "http://devops.sendinfo.nb/v1/login-by-ldap",
              search: "http://devops.sendinfo.nb/test-ops/v1/task/app/search",
              package: "http://devops.sendinfo.nb/test-ops/v1/task/package",
              redirectBase: "http://one-test.sendinfo.com/#/task-detail"
            },
            options: {
              withRelease: true,
              isIdcJenkins: false
            }
          }
        };
      }

      console.log('7. 准备发送的登录数据:', loginData);
      setLoginStatus(prev => ({ ...prev, [account.id]: { status: 'loading', message: '正在调用插件...' }}));
      
      // 检查Chrome API是否存在，如果不存在则允许用户直接跳转
      if (typeof chrome === 'undefined' || !chrome.runtime) {
        console.log('❌ Chrome API不可用，但允许用户手动跳转到登录页面');
        console.log('   - chrome对象:', typeof chrome);
        console.log('   - chrome.runtime:', typeof chrome !== 'undefined' ? chrome.runtime : 'chrome未定义');
        
        // 详细诊断
        console.log('详细诊断信息:');
        console.log('   - 当前协议:', window.location.protocol);
        console.log('   - 是否HTTPS:', window.location.protocol === 'https:');
        console.log('   - 是否localhost:', window.location.hostname === 'localhost');
        console.log('   - 是否IP地址:', /^\d+\.\d+\.\d+\.\d+$/.test(window.location.hostname));
        
        // 允许用户跳转到登录页面
        console.log('🔗 打开登录页面:', account.login_url);
        window.open(account.login_url, '_blank');
        
        setLoginStatus(prev => ({ 
          ...prev, 
          [account.id]: { 
            status: 'success', 
            message: '已打开登录页面，请手动登录' 
          } 
        }));
        return;
      }
      
      console.log('✅ Chrome API可用，准备发送消息到插件');
      console.log('8. 目标插件ID:', extensionId);
      
      // 直接发送消息到插件
      chrome.runtime.sendMessage(extensionId, {
        type: 'AUTO_LOGIN',
        data: loginData
      }, (response: any) => {
        console.log('9. 发送消息完成，检查结果...');
        console.log('   - chrome.runtime.lastError:', chrome.runtime?.lastError);
        console.log('   - 插件响应:', response);
        
        if (chrome.runtime?.lastError) {
          const errorMessage = chrome.runtime?.lastError?.message || '未知错误';
          console.error('❌ 插件通信失败:', errorMessage);
          console.log('可能的原因:');
          console.log('   1. 插件ID不正确');
          console.log('   2. 插件未安装');
          console.log('   3. 插件已禁用');
          console.log('   4. 插件的manifest.json未配置允许此网站通信');
          setLoginStatus(prev => ({ ...prev, [account.id]: { status: 'error', message: `插件通信失败: ${errorMessage}。请确认插件ID正确且插件已安装启用。` }}));
        } else {
          console.log('✅ 插件通信成功:', response);
          setLoginStatus(prev => ({ ...prev, [account.id]: { status: 'success', message: response?.message || '登录指令已发送' }}));
        }
        console.log('=== 登录调试信息结束 ===');
      });

    } catch (error: any) {
      console.error('❌ 插件登录流程出错:', error);
      console.log('=== 登录调试信息结束 (异常) ===');
      setLoginStatus(prev => ({ ...prev, [account.id]: { status: 'error', message: `登录准备失败: ${error instanceof Error ? error.message : String(error)}` }}));
    }

    // 5秒后清除状态
    setTimeout(() => {
      setLoginStatus(prev => {
        const newStatus = { ...prev };
        delete newStatus[account.id];
        return newStatus;
      });
    }, 5000);
  };

  const handleEditAccount = (account: any) => {
    setSelectedAccount(account);
    setIsModalOpen(true);
  };

  const handleAddAccount = () => {
    setSelectedAccount(null);
    setIsModalOpen(true);
  };

  const handleDuplicateAccount = async (account: any) => {
    try {
      const { duplicateAccount } = await import('../lib/supabase.js');
      await duplicateAccount(account.id);
      await loadAccounts(); // 刷新账号列表
    } catch (error) {
      console.error('复制账号失败:', error);
      alert('复制账号失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  const handleDeleteAccount = async (account: any) => {
    const confirmed = window.confirm(`确定要删除账号 "${account.name}" 吗？`);
    if (!confirmed) return;

    try {
      const { deleteAccount } = await import('../lib/supabase.js');
      await deleteAccount(account.id);
      await loadAccounts(); // 刷新账号列表
    } catch (error) {
      console.error('删除账号失败:', error);
      alert('删除账号失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedAccount(null);
    // 重新加载账号列表
    loadAccounts();
  };
  
  const handleOpenConfig = () => {
    setIsConfigModalOpen(true);
  };
  
  const handleConfigModalClose = () => {
    setIsConfigModalOpen(false);
  };

  // 切换强制登录状态 - 直接操作Supabase，无需API接口
  const handleToggleForceLogin = async (account: any) => {
    try {
      const { toggleForceLogin } = await import('../lib/supabase.js');
      const updatedAccount = await toggleForceLogin(account.id);
      
      // 更新本地状态
      setAccounts(prevAccounts => 
        prevAccounts.map(acc => 
          acc.id === account.id 
            ? { ...acc, force_login: updatedAccount.force_login }
            : acc
        )
      );
      
      // 静默更新，不显示弹框
      console.log(`账号 ${account.name} 的强制登录已${updatedAccount.force_login ? '启用' : '禁用'}`);
    } catch (error) {
      console.error('切换强制登录失败:', error);
      alert('切换强制登录失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 监听登录状态更新
  useEffect(() => {
    if (window.electronAPI) {
      const cleanup = window.electronAPI.onLoginStatus((status) => {
        console.log('登录状态更新:', status);
      });

      return cleanup;
    }
  }, []);

  // 移除首次加载时的配置检查弹窗

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="glass-card p-8">
          <div className="loading-skeleton w-32 h-8 rounded mb-4"></div>
          <div className="loading-skeleton w-48 h-4 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <AuthWrapper>
      <div className="flex flex-col h-screen">
        <TitleBar />
        <div className="flex flex-1 overflow-hidden">
          <Sidebar
            environments={environments}
            selectedEnvironment={selectedEnvironment}
            onEnvironmentSelect={setSelectedEnvironment}
            onAddAccount={handleAddAccount}
            onOpenConfig={handleOpenConfig}
          />
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="mb-6 space-y-4">
              <div className="flex items-center gap-4">
                <SearchBar
                  query={searchQuery}
                  onQueryChange={setSearchQuery}
                  placeholder="搜索账号..."
                />
              </div>
              
              {/* 账号分类Tab */}
              <AccountTabs
                accounts={accounts}
                selectedOwnershipType={selectedOwnershipType}
                selectedBusinessType={selectedBusinessType}
                onOwnershipTypeChange={setSelectedOwnershipType}
                onBusinessTypeChange={setSelectedBusinessType}
              />
              
              {/* 显示筛选结果统计 */}
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {searchQuery && <span>搜索"{searchQuery}" • </span>}
                共 {filteredAccounts.length} 个账号
              </div>
            </div>
            <AccountSection
              accounts={filteredAccounts}
              onLogin={handleLogin}
              onEdit={handleEditAccount}
              onDuplicate={handleDuplicateAccount}
              onDelete={handleDeleteAccount}
              onToggleForceLogin={handleToggleForceLogin}
              loginStatus={loginStatus}
            />
          </div>
        </div>
        
        {isModalOpen && (
          <AccountModal
            isOpen={isModalOpen}
            onClose={handleModalClose}
            onSave={async (accountData) => {
              try {
                if (selectedAccount) {
                  // 更新账号
                  const supabase = await import('../lib/supabase.js');
                  await supabase.updateAccount(selectedAccount.id, accountData);
                } else {
                  // 创建账号
                  const supabase = await import('../lib/supabase.js');
                  await supabase.createAccount({
                    ...accountData,
                    environment_id: selectedEnvironment?.id
                  });
                }
                handleModalClose();
              } catch (error) {
                console.error('保存账号失败:', error);
                alert('保存账号失败: ' + (error instanceof Error ? error.message : String(error)));
              }
            }}
            account={selectedAccount}
            environments={environments}
          />
        )}
        
        <ConfigModal 
          isOpen={isConfigModalOpen} 
          onClose={handleConfigModalClose} 
        />
      </div>
    </AuthWrapper>
  );
} 
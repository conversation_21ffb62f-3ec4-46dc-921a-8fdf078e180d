@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

body {
  color: rgb(var(--foreground-rgb));
  background-color: #f8f9fa;
  height: 100vh;
}

a {
  color: inherit;
  text-decoration: none;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 毛玻璃效果基础类 */
@layer components {
  .glass-card {
    @apply bg-white/20 backdrop-blur-xl rounded-2xl border border-white/20 shadow-lg;
  }

  /* 增强版用户信息卡片 - 更高可见性 */
  .glass-card-enhanced {
    @apply backdrop-blur-xl rounded-2xl shadow-2xl;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2));
    border: 2px solid rgba(255, 255, 255, 0.6);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 0 20px rgba(255, 255, 255, 0.1);
  }

  .glass-card-hover {
    @apply glass-card transition-all duration-300 hover:bg-white/30 hover:border-white/40 hover:shadow-2xl;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .glass-card-hover-blue {
    @apply glass-card transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/40;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(37, 99, 235, 0.4));
    border: 3px solid rgba(59, 130, 246, 0.8);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .glass-card-hover-blue:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.7), rgba(37, 99, 235, 0.5));
    border-color: rgba(59, 130, 246, 0.9);
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }

  .glass-card-hover-green {
    @apply glass-card transition-all duration-300 hover:shadow-2xl hover:shadow-green-500/40;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.6), rgba(21, 128, 61, 0.4));
    border: 3px solid rgba(34, 197, 94, 0.8);
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }

  .glass-card-hover-green:hover {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.7), rgba(21, 128, 61, 0.5));
    border-color: rgba(34, 197, 94, 0.9);
    box-shadow: 0 0 30px rgba(34, 197, 94, 0.5);
  }

  /* 新增：业务类型对应的卡片颜色 */
  .glass-card-hover-emerald {
    @apply glass-card transition-all duration-300 hover:shadow-2xl hover:shadow-emerald-500/40;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.6), rgba(5, 150, 105, 0.4));
    border: 3px solid rgba(16, 185, 129, 0.8);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }

  .glass-card-hover-emerald:hover {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.7), rgba(5, 150, 105, 0.5));
    border-color: rgba(16, 185, 129, 0.9);
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.5);
  }

  .glass-card-hover-orange {
    @apply glass-card transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/40;
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.6), rgba(234, 88, 12, 0.4));
    border: 3px solid rgba(249, 115, 22, 0.8);
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.3);
  }

  .glass-card-hover-orange:hover {
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.7), rgba(234, 88, 12, 0.5));
    border-color: rgba(249, 115, 22, 0.9);
    box-shadow: 0 0 30px rgba(249, 115, 22, 0.5);
  }

  .glass-card-hover-purple {
    @apply glass-card transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/40;
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.6), rgba(147, 51, 234, 0.4));
    border: 3px solid rgba(168, 85, 247, 0.8);
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
  }

  .glass-card-hover-purple:hover {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.7), rgba(147, 51, 234, 0.5));
    border-color: rgba(168, 85, 247, 0.9);
    box-shadow: 0 0 30px rgba(168, 85, 247, 0.5);
  }

  .glass-card-hover-red {
    @apply glass-card transition-all duration-300 hover:shadow-2xl hover:shadow-red-500/40;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.6), rgba(220, 38, 38, 0.4));
    border: 3px solid rgba(239, 68, 68, 0.8);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }

  .glass-card-hover-red:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.7), rgba(220, 38, 38, 0.5));
    border-color: rgba(239, 68, 68, 0.9);
    box-shadow: 0 0 30px rgba(239, 68, 68, 0.5);
  }

  /* 公开账号浅色版本 */
  .glass-card-hover-light {
    @apply glass-card transition-all duration-300 hover:bg-white/30 hover:border-white/40 hover:shadow-2xl;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 255, 255, 0.2);
    opacity: 0.8;
  }

  .glass-card-hover-emerald-light {
    @apply glass-card transition-all duration-300 hover:shadow-2xl hover:shadow-emerald-500/20;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(5, 150, 105, 0.2));
    border: 2px solid rgba(16, 185, 129, 0.5);
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.2);
    opacity: 0.8;
  }

  .glass-card-hover-emerald-light:hover {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.4), rgba(5, 150, 105, 0.3));
    border-color: rgba(16, 185, 129, 0.6);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }

  .glass-card-hover-blue-light {
    @apply glass-card transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/20;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(37, 99, 235, 0.2));
    border: 2px solid rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.2);
    opacity: 0.8;
  }

  .glass-card-hover-blue-light:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(37, 99, 235, 0.3));
    border-color: rgba(59, 130, 246, 0.6);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .glass-card-hover-purple-light {
    @apply glass-card transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/20;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(124, 58, 237, 0.2));
    border: 2px solid rgba(139, 92, 246, 0.5);
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.2);
    opacity: 0.8;
  }

  .glass-card-hover-purple-light:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.4), rgba(124, 58, 237, 0.3));
    border-color: rgba(139, 92, 246, 0.6);
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }

  .glass-card-hover-red-light {
    @apply glass-card transition-all duration-300 hover:shadow-2xl hover:shadow-red-500/20;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.3), rgba(220, 38, 38, 0.2));
    border: 2px solid rgba(239, 68, 68, 0.5);
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.2);
    opacity: 0.8;
  }

  .glass-card-hover-red-light:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 38, 0.3));
    border-color: rgba(239, 68, 68, 0.6);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }

  /* 文本截断样式 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .glass-sidebar {
    @apply bg-white/10 backdrop-blur-2xl border-r border-white/20;
  }

  .glass-button {
    @apply bg-white/20 backdrop-blur-lg rounded-xl border border-white/30
           hover:bg-white/30 active:bg-white/40 transition-all duration-200
           text-white font-medium px-4 py-2;
  }

  .glass-input {
    @apply bg-white/10 backdrop-blur-lg rounded-lg border border-white/20
           focus:border-white/40 focus:bg-white/20 transition-all duration-200
           text-white placeholder-white/60 px-3 py-2;
  }

  .glass-modal {
    @apply bg-black/50 backdrop-blur-sm;
  }

  .glass-content {
    @apply bg-white/20 backdrop-blur-2xl rounded-3xl border border-white/20 shadow-2xl;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

.loading-skeleton {
  background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite linear;
}

/* Electron 窗口控制按钮样式 */
.window-controls {
  -webkit-app-region: no-drag;
}

.title-bar {
  -webkit-app-region: drag;
}

/* 状态指示器 */
.status-dot {
  @apply w-2 h-2 rounded-full animate-pulse;
}

.status-success {
  @apply bg-green-400;
}

.status-error {
  @apply bg-red-400;
}

.status-warning {
  @apply bg-yellow-400;
}

.status-loading {
  @apply bg-blue-400;
}

import React from 'react'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import appConfig from '../lib/config' // 导入环境配置

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: `账号管理系统 - ${appConfig.environment === 'production' ? '生产环境' : '开发环境'}`,
  description: '安全的账号管理和自动登录系统',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <body className={inter.className}>
        <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500">
          {/* 环境标识 */}
          {appConfig.environment === 'production' && (
            <div className="fixed top-0 left-0 right-0 bg-red-600 text-white text-center py-1 text-xs z-50">
              生产环境 - {appConfig.apiBaseUrl.split('/api')[0]}
            </div>
          )}
          {children}
        </div>
      </body>
    </html>
  )
} 
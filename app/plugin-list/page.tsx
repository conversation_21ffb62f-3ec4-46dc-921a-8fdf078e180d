'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface Plugin {
  id: string;
  name: string;
  version: string;
  description: string;
  file_path: string;
  public_url?: string;
  created_at: string;
}

export default function PluginListPage() {
  const [plugins, setPlugins] = useState<Plugin[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 获取插件列表
  useEffect(() => {
    // 只在客户端环境执行API调用
    if (typeof window === 'undefined') return;
    
    const fetchPlugins = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await fetch('/api/plugin/list');
        const result = await response.json();
        
        if (result.success && result.data) {
          setPlugins(result.data);
        } else {
          setError(result.error || '无法获取插件列表');
        }
      } catch (err: any) {
        console.error('获取插件列表失败:', err);
        setError('获取插件列表失败');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchPlugins();
  }, []);
  
  // 处理下载插件
  const handleDownload = async (plugin: Plugin) => {
    try {
      // 如果插件有公共URL，直接使用
      if (plugin.public_url) {
        const link = document.createElement('a');
        link.href = plugin.public_url;
        link.download = `${plugin.name}-v${plugin.version}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        return;
      }
      
      // 否则通过API获取下载链接
      const response = await fetch(`/api/plugin/download?id=${plugin.id}`);
      const result = await response.json();
      
      if (result.success && result.data.download_url) {
        // 创建一个隐藏的a标签并模拟点击来下载
        const link = document.createElement('a');
        link.href = result.data.download_url;
        link.download = `${plugin.name}-v${plugin.version}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        alert(result.error || '下载链接生成失败');
      }
    } catch (err: any) {
      console.error('下载插件失败:', err);
      alert('下载失败，请稍后再试');
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* 页面标题 */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-8"
      >
        <h1 className="text-4xl font-bold text-white mb-2">插件列表</h1>
        <p className="text-white/80 text-lg">管理和下载已上传的浏览器插件</p>
      </motion.div>
      
      {/* 主内容区 */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="glass-content p-0 overflow-hidden"
      >
        {/* 顶部导航 */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-white/10 title-bar">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <div>
              <h2 className="text-xl font-medium text-white">插件管理</h2>
              <p className="text-white/60 text-sm">查看和下载已上传的插件</p>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <Link href="/plugin-upload" className="glass-button flex items-center text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              上传新插件
            </Link>
            
            <Link href="/" className="glass-button flex items-center text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              返回主页
            </Link>
          </div>
        </div>
        
        {/* 内容区域 */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin h-8 w-8 border-4 border-white rounded-full border-t-transparent"></div>
              <span className="ml-3 text-white/80">加载插件列表中...</span>
            </div>
          ) : error ? (
            <div className="bg-red-500/20 border border-red-500/50 text-white rounded-md p-4 text-center">
              <p>{error}</p>
              <button 
                onClick={() => window.location.reload()}
                className="mt-2 px-4 py-1 bg-white/10 hover:bg-white/20 rounded-md transition-colors"
              >
                重试
              </button>
            </div>
          ) : plugins.length === 0 ? (
            <div className="text-center py-16">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-white/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              <p className="text-white/60 mt-4 text-lg">还没有上传任何插件</p>
              <Link href="/plugin-upload" className="mt-4 inline-block px-6 py-2 bg-blue-500 hover:bg-blue-600 rounded-md text-white transition-colors">
                上传第一个插件
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {plugins.map((plugin) => (
                <motion.div
                  key={plugin.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white/5 border border-white/10 rounded-lg p-4 flex justify-between items-center"
                >
                  <div>
                    <div className="flex items-center">
                      <h3 className="text-lg font-medium text-white">{plugin.name}</h3>
                      <span className="ml-2 px-2 py-0.5 bg-blue-500/20 text-blue-300 text-xs rounded-full">
                        v{plugin.version}
                      </span>
                    </div>
                    {plugin.description && (
                      <p className="text-white/70 text-sm mt-1">{plugin.description}</p>
                    )}
                    <p className="text-white/50 text-xs mt-2">
                      上传时间: {new Date(plugin.created_at).toLocaleString()}
                    </p>
                  </div>
                  
                  <button
                    onClick={() => handleDownload(plugin)}
                    className="flex items-center space-x-1 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-md transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    <span>下载</span>
                  </button>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
}
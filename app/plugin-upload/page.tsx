'use client';

import { useState, useRef } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';

export default function PluginUploadPage() {
  const [file, setFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string>('');
  const [pluginName, setPluginName] = useState<string>('');
  const [pluginDescription, setPluginDescription] = useState<string>('');
  const [pluginVersion, setPluginVersion] = useState<string>('1.0.0');
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadResult, setUploadResult] = useState<{success: boolean, message: string, warning?: string} | null>(null);
  const [error, setError] = useState<string>('');
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // 检查文件类型
      const validTypes = ['application/zip', 'application/x-zip-compressed', 'application/x-chrome-extension', 'application/octet-stream'];
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();
      
      if (!validTypes.includes(selectedFile.type) && !['zip', 'crx'].includes(fileExtension || '')) {
        setError('请上传 .zip 或 .crx 格式的插件文件');
        return;
      }
      
      // 检查文件大小 (10MB 限制)
      if (selectedFile.size > 10 * 1024 * 1024) {
        setError('文件大小不能超过 10MB');
        return;
      }
      
      setFile(selectedFile);
      setError('');
      
      // 如果文件名中包含版本信息，自动提取
      const nameMatch = selectedFile.name.match(/^(.+?)[-_]?v?([0-9]+\.[0-9]+\.[0-9]+)/i);
      if (nameMatch) {
        if (!pluginName) setPluginName(nameMatch[1].replace(/[-_]/g, ' '));
        if (pluginVersion === '1.0.0') setPluginVersion(nameMatch[2]);
      } else if (!pluginName) {
        // 否则使用文件名作为插件名称
        setPluginName(selectedFile.name.replace(/\.(zip|crx)$/i, ''));
      }
      
      // 创建文件图标预览
      setFilePreview(getFileIconByExtension(fileExtension || ''));
    }
  };
  
  // 根据文件扩展名获取图标
  const getFileIconByExtension = (extension: string): string => {
    if (extension === 'crx') {
      return '/chrome-extension-icon.svg'; // 假设有这个图标
    }
    return '/zip-file-icon.svg'; // 假设有这个图标
  };
  
  // 触发文件选择对话框
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };
  
  // 处理拖放
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };
  
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];
      // 模拟文件输入变化
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(droppedFile);
      
      if (fileInputRef.current) {
        fileInputRef.current.files = dataTransfer.files;
        const event = new Event('change', { bubbles: true });
        fileInputRef.current.dispatchEvent(event);
      }
    }
  };
  
  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!file) {
      setError('请选择一个插件文件');
      return;
    }
    
    if (!pluginName.trim()) {
      setError('请输入插件名称');
      return;
    }
    
    setError('');
    setIsUploading(true);
    setUploadProgress(0);
    setUploadResult(null);
    
    try {
      // 创建表单数据
      const formData = new FormData();
      formData.append('file', file);
      formData.append('name', pluginName);
      formData.append('description', pluginDescription);
      formData.append('version', pluginVersion);
      
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 15;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 300);
      
      // 发送上传请求
      const response = await fetch('/api/plugin/upload', {
        method: 'POST',
        body: formData,
      });
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      const result = await response.json();
      console.log('上传结果:', result);
      
      if (result.success) {
        // 上传成功
        setUploadResult({
          success: true,
          message: '插件上传成功！',
          warning: result.warning // 添加警告信息
        });
        
        // 重置表单
        setFile(null);
        setFilePreview('');
        setPluginName('');
        setPluginDescription('');
        setPluginVersion('1.0.0');
        if (fileInputRef.current) fileInputRef.current.value = '';
      } else {
        throw new Error(result.error || '上传失败');
      }
    } catch (err: any) {
      console.error('上传插件失败:', err);
      setUploadResult({
        success: false,
        message: `上传失败: ${err.message || '未知错误'}`
      });
    } finally {
      setIsUploading(false);
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* 页面标题 */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-8"
      >
        <h1 className="text-4xl font-bold text-white mb-2">插件上传</h1>
        <p className="text-white/80 text-lg">上传您的浏览器插件文件到系统中</p>
      </motion.div>
      
      {/* 主内容区 */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="glass-content p-0 overflow-hidden"
      >
        {/* 顶部导航 */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-white/10 title-bar">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
              </svg>
            </div>
            <div>
              <h2 className="text-xl font-medium text-white">插件上传工具</h2>
              <p className="text-white/60 text-sm">上传并管理浏览器插件</p>
            </div>
          </div>
          
          <Link href="/" className="glass-button flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            返回主页
          </Link>
        </div>
        
        {/* 内容区域 */}
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 文件上传区域 */}
            <div 
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all
                ${file ? 'border-green-400/50 bg-green-400/5' : 'border-white/20 hover:border-white/40 bg-white/5 hover:bg-white/10'}`}
              onClick={triggerFileInput}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <input 
                type="file" 
                ref={fileInputRef}
                onChange={handleFileChange}
                accept=".zip,.crx"
                className="hidden" 
              />
              
              {!file ? (
                <div className="space-y-4">
                  <div className="flex justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-white text-lg font-medium">拖放文件到此处或点击上传</p>
                    <p className="text-white/60 text-sm mt-1">支持 .zip 或 .crx 格式的插件文件（最大 10MB）</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-4">
                  <div className="w-16 h-16 flex items-center justify-center bg-white/10 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white/80" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <p className="text-white text-lg font-medium truncate max-w-xs">{file.name}</p>
                    <p className="text-white/60 text-sm">{(file.size / 1024).toFixed(1)} KB • {file.type || '未知类型'}</p>
                    <p className="text-white/60 text-xs mt-1">点击更换文件</p>
                  </div>
                </div>
              )}
            </div>
            
            {/* 错误提示 */}
            {error && (
              <div className="bg-red-500/20 border border-red-500/50 text-white rounded-md p-3 text-sm">
                {error}
              </div>
            )}
            
            {/* 插件信息表单 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-white/80 text-sm font-medium mb-2">
                  插件名称 *
                </label>
                <input
                  type="text"
                  value={pluginName}
                  onChange={(e) => setPluginName(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-md px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                  placeholder="输入插件名称"
                  required
                />
              </div>
              
              <div>
                <label className="block text-white/80 text-sm font-medium mb-2">
                  插件版本
                </label>
                <input
                  type="text"
                  value={pluginVersion}
                  onChange={(e) => setPluginVersion(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-md px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                  placeholder="1.0.0"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-white/80 text-sm font-medium mb-2">
                  插件描述
                </label>
                <textarea
                  value={pluginDescription}
                  onChange={(e) => setPluginDescription(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-md px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50 h-24 resize-none"
                  placeholder="输入插件功能描述（可选）"
                ></textarea>
              </div>
            </div>
            
            {/* 上传按钮 */}
            <div className="flex justify-center pt-4">
              <button
                type="submit"
                disabled={isUploading || !file}
                className={`px-8 py-3 rounded-md text-white font-medium flex items-center space-x-2
                  ${isUploading || !file 
                    ? 'bg-blue-500/50 cursor-not-allowed' 
                    : 'bg-blue-500 hover:bg-blue-600 transition-colors'}`}
              >
                {isUploading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>上传中...</span>
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                    </svg>
                    <span>上传插件</span>
                  </>
                )}
              </button>
            </div>
            
            {/* 上传进度 */}
            {isUploading && (
              <div className="mt-4">
                <div className="flex justify-between text-sm text-white/70 mb-1">
                  <span>上传进度</span>
                  <span>{Math.round(uploadProgress)}%</span>
                </div>
                <div className="w-full bg-white/10 rounded-full h-2.5">
                  <div 
                    className="bg-blue-500 h-2.5 rounded-full transition-all duration-300" 
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </div>
            )}
            
            {/* 上传结果 */}
            {uploadResult && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`p-4 rounded-md ${
                  uploadResult.success 
                    ? 'bg-green-500/20 border border-green-500/50' 
                    : 'bg-red-500/20 border border-red-500/50'
                }`}
              >
                <div className="flex items-start">
                  <div className={`rounded-full p-1 ${uploadResult.success ? 'bg-green-500' : 'bg-red-500'}`}>
                    {uploadResult.success ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                  <div className="ml-3">
                    <p className="text-white font-medium">{uploadResult.message}</p>
                    {uploadResult.success && (
                      <p className="text-white/70 text-sm mt-1">
                        您可以在插件列表中查看和管理已上传的插件
                      </p>
                    )}
                    {uploadResult.warning && (
                      <p className="text-yellow-300 text-sm mt-1 border-l-2 border-yellow-400 pl-2">
                        {uploadResult.warning}
                      </p>
                    )}
                  </div>
                </div>
              </motion.div>
            )}
          </form>
        </div>
      </motion.div>
    </div>
  );
} 
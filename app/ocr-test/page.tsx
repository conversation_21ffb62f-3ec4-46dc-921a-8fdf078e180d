'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';

export default function OcrTestPage() {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [ocrResult, setOcrResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  // 处理 URL 输入变化
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setImageUrl(e.target.value);
  };

  // 处理文件上传
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      // 创建预览 URL
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
      setImageUrl(''); // 清除 URL 输入
    }
  };

  // 处理 URL 提交
  const handleUrlSubmit = async () => {
    if (!imageUrl) {
      setError('请输入图片 URL');
      return;
    }

    setError('');
    setIsLoading(true);
    setOcrResult('');

    try {
      console.log('开始获取图片:', imageUrl);
      
      // 使用代理请求获取图片以避免CORS问题
      const proxyUrl = `/api/ocr/proxy?url=${encodeURIComponent(imageUrl)}`;
      
      // 先尝试使用代理获取图片
      try {
        const imageResponse = await fetch(proxyUrl);
        if (!imageResponse.ok) {
          throw new Error(`代理请求失败: ${imageResponse.status}`);
        }
        const blob = await imageResponse.blob();
        const reader = new FileReader();
        
        reader.onload = async (e) => {
          const base64 = e.target?.result as string;
          console.log('图片已转换为Base64:', base64.substring(0, 50) + '...');
          await processOcr(base64);
        };
        
        reader.onerror = (error) => {
          console.error('读取图片失败:', error);
          setError('读取图片失败');
          setIsLoading(false);
        };
        
        reader.readAsDataURL(blob);
      } catch (proxyErr) {
        console.error('通过代理获取图片失败，尝试直接请求:', proxyErr);
        
        // 如果代理失败，尝试直接请求（可能会有CORS问题）
        try {
          const directResponse = await fetch(imageUrl);
          if (!directResponse.ok) {
            throw new Error(`直接请求失败: ${directResponse.status}`);
          }
          const blob = await directResponse.blob();
          const reader = new FileReader();
          
          reader.onload = async (e) => {
            const base64 = e.target?.result as string;
            await processOcr(base64);
          };
          
          reader.readAsDataURL(blob);
        } catch (directErr) {
          console.error('直接获取图片也失败:', directErr);
          setError('无法获取图片，请尝试下载后上传');
          setIsLoading(false);
        }
      }
    } catch (err) {
      console.error('获取图片过程中出错:', err);
      setError('获取图片失败，请检查 URL 是否正确或尝试下载后上传');
      setIsLoading(false);
    }
  };

  // 处理文件提交
  const handleFileSubmit = async () => {
    if (!imageFile) {
      setError('请选择一个图片文件');
      return;
    }

    setError('');
    setIsLoading(true);
    setOcrResult('');

    try {
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        const base64 = e.target?.result as string;
        await processOcr(base64);
      };
      
      reader.readAsDataURL(imageFile);
    } catch (err) {
      console.error('处理图片失败:', err);
      setError('处理图片失败');
      setIsLoading(false);
    }
  };

  // 处理 OCR 识别
  const processOcr = async (base64Image: string) => {
    try {
      const response = await fetch('/api/ocr/recognize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData: base64Image
        })
      });

      const data = await response.json();

      if (data.success) {
        setOcrResult(data.data.text);
      } else {
        setError(`OCR 识别失败: ${data.error?.message || '未知错误'}`);
      }
    } catch (err) {
      console.error('OCR API 调用失败:', err);
      setError('OCR 服务调用失败');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* 页面标题 */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-8"
      >
        <h1 className="text-4xl font-bold text-white mb-2">验证码 OCR 识别</h1>
        <p className="text-white/80 text-lg">使用百度 OCR 服务识别验证码，提高自动登录成功率</p>
      </motion.div>
      
      {/* 主内容区 */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="glass-content p-0 overflow-hidden"
      >
        {/* 顶部导航 */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-white/10 title-bar">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
            <div>
              <h2 className="text-xl font-medium text-white">验证码识别工具</h2>
              <p className="text-white/60 text-sm">识别验证码文本内容</p>
            </div>
          </div>
          
          <Link href="/" className="glass-button flex items-center text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            返回主页
          </Link>
        </div>
        
        {/* 内容区域 */}
        <div className="p-6 space-y-6">
          {/* 错误提示 */}
          {error && (
            <motion.div 
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-red-500/20 backdrop-blur-sm border border-red-500/30 rounded-xl p-4 text-white"
            >
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 mt-0.5 text-red-300" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zm-1 9a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                </svg>
                <span>{error}</span>
              </div>
            </motion.div>
          )}
          
          {/* 两种识别方式 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 方法 1: URL 输入 */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="glass-card p-6"
            >
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 rounded-full bg-blue-500/30 flex items-center justify-center mr-3">
                  <span className="text-white font-medium">1</span>
                </div>
                <h3 className="text-xl font-medium text-white">输入验证码图片 URL</h3>
              </div>
              
              <div className="space-y-4">
                <div className="relative">
                  <input
                    type="text"
                    value={imageUrl}
                    onChange={handleUrlChange}
                    placeholder="https://example.com/captcha.jpg"
                    className="glass-input w-full pr-24"
                  />
                  <button
                    onClick={handleUrlSubmit}
                    disabled={isLoading || !imageUrl}
                    className="absolute right-1 top-1 glass-button py-1 px-3 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="flex items-center">
                        <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        处理中
                      </div>
                    ) : '识别图片'}
                  </button>
                </div>
                <p className="text-white/60 text-sm">输入验证码图片的完整URL地址</p>
              </div>
            </motion.div>
            
            {/* 方法 2: 文件上传 */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="glass-card p-6"
            >
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 rounded-full bg-purple-500/30 flex items-center justify-center mr-3">
                  <span className="text-white font-medium">2</span>
                </div>
                <h3 className="text-xl font-medium text-white">上传验证码图片</h3>
              </div>
              
              <div className="space-y-4">
                <label className="block border-2 border-dashed border-white/20 rounded-xl p-6 text-center cursor-pointer hover:border-white/40 transition-colors duration-200">
                  <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-white/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span className="block mt-2 text-white/80">点击选择图片或拖拽到此处</span>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </label>
                
                <button
                  onClick={handleFileSubmit}
                  disabled={isLoading || !imageFile}
                  className="glass-button w-full py-3 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      处理中...
                    </div>
                  ) : '识别图片'}
                </button>
              </div>
            </motion.div>
          </div>
          
          {/* 图片预览和结果区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 图片预览 */}
            {(imageUrl || imagePreview) && (
              <motion.div 
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="glass-card p-6"
              >
                <h3 className="text-xl font-medium text-white mb-4">图片预览</h3>
                <div className="bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-2 flex items-center justify-center">
                  {imageUrl ? (
                    <img src={imageUrl} alt="验证码预览" className="max-h-48 rounded" />
                  ) : imagePreview ? (
                    <img src={imagePreview} alt="验证码预览" className="max-h-48 rounded" />
                  ) : null}
                </div>
              </motion.div>
            )}
            
            {/* OCR 识别结果 */}
            {ocrResult && (
              <motion.div 
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="glass-card p-6"
              >
                <h3 className="text-xl font-medium text-white mb-4">识别结果</h3>
                <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6 text-center">
                  <p className="font-mono text-2xl text-white tracking-wider">{ocrResult}</p>
                </div>
                <div className="mt-4 flex justify-between items-center">
                  <p className="text-white/60 text-sm">识别完成，可复制使用</p>
                  <button 
                    onClick={() => navigator.clipboard.writeText(ocrResult)}
                    className="glass-button text-sm py-1"
                  >
                    复制结果
                  </button>
                </div>
              </motion.div>
            )}
          </div>
          
          {/* 提示信息 */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="bg-blue-500/10 backdrop-blur-sm border border-blue-500/20 rounded-xl p-4 text-white/80 text-sm"
          >
            <div className="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 mt-0.5 text-blue-300" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zm-1 9a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="mb-1">本工具使用百度 OCR 技术识别验证码，识别结果可用于自动登录。</p>
                <p>如识别失败，可尝试使用更清晰的图片或手动输入验证码。</p>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
} 
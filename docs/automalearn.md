从添加一个触发器开始（流程的起点）
流程触发器用于表示流程的起点，流程从触发器开始按序执行
automa 流程必须 有且只有一个触发器（没有触发器无法保存，也无法添加多于一个的触发器）。
一个完整可运行的Demo
可以尝试搭建一个简单的Demo，打开浏览器，并且弹出一句话

JS 代码块
如上的 Demo 中，我们使用到了 JS 块，JS代码块是 automa 内使用最多的能力，允许编写自定义的 JS 代码，完成各种功能：
1. 接口调用，爬取数据
2. 抓取页面内容
3. 自动化完成页面交互
4. 调用客户端能力
5. ....
JS 代码块的执行上下文

JS 块有两个执行上下文
● 当前标签页。在当前激活的标签页内执行，类似在调试工具内执行 ===> 在这个js块之前，必须有类似新建标签页、切换标签页这种已经设置了标签页为活动标签页的情况，执行才不会报错
● 背景。在插件内执行，类似后台执行，如果不需要操作页面元素的或者不需要window相关属性，能通过调接口的方式实现的，可将执行上下文选中为：背景



执行过程、和效果没有区别，具体看场景来选择上下文。
某些和当前页面内容强相关的功能需要放在当前标签页执行，如：
1. 需要使用到当前页面的变量
2. 需要抓取当前页面的DOM元素
3. ...
唯一要注意的点是在 JS 中发起请求会遇到的同源策略，在背景和当前标签页中有所不同，具体可以看下面的网络请求部分
查看背景中运行的代码日志
不同于标签页执行的代码，可以直接在浏览器内看到日志。
背景运行代码的日志，需要在 automa 的背景页，console.log 、请求等。


按照如下的步骤进入背景页的调试工具 



完成代码块执行
JS 块需要调用 automaNextBlock()，以继续向下执行流程，否则流程会停留在当前块
发起网络请求
目前来说 automa 内做的数据采集、任务创建等自动化能力，大多通过API请求，而非模拟用户点击。所以，发起网络请求是流程内必不可少的功能。
目前，automa内部有两个发起网络请求的方式：
● JS 块内发起
● Http 块内发起
通过 JavaScript 代码块发起请求
JS 块中，又有两种 API 可以使用
使用 fetch
可以直接使用 原生的 fetch 方法发起请求，但是容易出现跨域问题。
在服务端没有配置 cors 的前提下：
● fetch在背景页执行，一定有跨域问题
● fetch在当前标签页执行。是否存在跨域问题，看同源策略
使用 automaFetch
automaFetch是automa封装的请求方法，可以绕过浏览器的同源策略。
automaFetch 有几个特性：
1. 无论 JS 块在背景还是标签页执行，automaFetch都会在背景页发起
2. 会自动带上浏览器的Cookie、User-Agent
3. 不受同源策略限制



使用方式：
automaFetch('json', {
  url: 'http://localhost:21002/get-custom-domain',
  method: "GET"
})

目前已知的问题是，automaFetch 不支持使用formData，无法上传文件（目前可以使用 fetch 或者 Http 块来弥补）。
如何选择？
除了一些特殊的情况， 推荐使用，automaFetch ，其适用性更强



通过 Http 请求块发起请求
如果没有复杂的请求处理、出入参处理逻辑，使用 Http 块发起请求也是不错的选择。
Http块 有几个需要说明的点：
变量引用方式
请求 URL、Body等，都支持使用双花括号模板的方式引用变量


请求结果存储到变量


文件上传
目前，在 automaFetch 不支持formData上传文件的情况下，可以使用 http 请求块替代。
需要有两处配置：
1. 内容类型改为 multipart/form-data
2. body 需要使用特定的格式。
  a. 需要上传的文件，需要以 automa 能访问的连接的形式给出，automa会自动去下载并将其上传




数据流转与全局数据
通常一个稍复杂的流程，不会只有一个节点，会将一个任务拆成多个节点执行不同的动作。这时候，不同节点之间可能需要交换数据， 数据需要在这些块内流转。
automa 块与块之间的数据流转、共享一般通过全局数据(variables)来进行
automa 里的全局数据

automa 内部有一份贯穿整个运行时的全局数据(referenceData)，automa 自身的一些运行时数据也会往上挂载。而其上的 variables 变量专门用于挂载我们的自定义变量，作为业务的全局变量。

这样，任意的相邻不相邻的节点，都可以通过全局变量交换数据（先执行的节点 设置全局数据，后执行的节点获取全局数据）
设置全局数据
automaSetVariable('block1Data', {key1: 'value1'})

相邻节点间传递数据
如果只需要在相邻节点间传递数据，可以使用如下的方式:
automaNextBlock(data)
条件与分支
在流程开发的过程中，经常通过条件执行流程的不同分支，可以通过 条件块 来实现。


有几个点需要说明：
● 条件块可以有任意多的分支
● 条件判断可以使用 JSCode 也可以是 可视化+表达式
● 条件之间支持 AND OR
● 条件块支持 fallback
异常处理
automa中的异常
automa中的的 JS块、Http请求块，等模块都可能抛出错误。抛出异常后，会导致流程停止，流程状态变为运行失败
其他常用的模块
模块组
将多个块组合在模块组内，所有的内部块都执行完成，模块组才会执行完成

标签页
● 新建标签页。每次新建标签页
● 切换标签页。如果已存在对应URL的标签页，则切换，否则新建
包
automa中的包，主要用于拆分和复用流程。对于一个复杂的流程，可以将部分流程拆分为包，然后再组合、复用
1. 查看当前流程下所有的包
2. 引用包
3. 包的展现形式



注意⚠️： 每次修改了包之后，要在流程中刷新一下这个包才能拿到最新修改后的包，如下图所示：

利用automa的自定义事件，发起执行一个rpa流程

常用工具块类注意事项
● 条件/while循环中不能使用automaSetVariable方法设置变量
● 条件块中这种方法的判断只能判断字符串，非字符串的方式需要通过jscode的方式去判断。





● 设置js模块最大执行超时时间，默认为20s，如果超过最大超时时间，流程会继续走到下一个工作块中。
● 条件模块可以设置重试次数，适用于loading判断等




automa函数使用方法
● automaRefData('keyword', 'path')  读取变量
● automaSetVariable('name', value)  设置变量
● automaNextBlock() 在javascript代码块中 需要在方法结束后调用这个方法来执行下一个工作快
● automaFetch() 1.发起请求避免跨域问题 2.处理form data参数


// 使用说明
automaFetch('json',{
    url:'http://api.com',
    headers:{
        'content-type':'application/json',
    },
    method:'POST',
    body:JSON.stringify(params)
})
// GET方法需要自己拼接成url
// 使用浏览器自带的URLSearchParams方法
 
// eg
const data = new URLSearchParams({
    id:'111'
})
automaFetch('json',{
    url:`http://api.com${data.toString()}`,
    headers:{
        'content-type':'application/json',
    },
    method:'POST',
    body:JSON.stringify(params)
})
// 对象转formData参数
export const objToFormData = (data = {}) => {
 const formData = []
 for (let key in data) {
  formData.push([key, data[key]])
 }
 let str = formData.reduce((pre, next) => {
  pre += next.join('=') + '&'
  return pre
 }, '')
 str = str.slice(0, str.length - 1)
 return str
}
// automaFetch会将这样的参数处理为form data的格式
开发流程注意事项

1. 触发器之后连接的标签不能是当前标签页js块(javascript)，可以将这个js块的执行上下文选成背景

2. 填写页面表单涉及到dropdown的元素时，有时候会获取不到该元素，可以尝试将流程右上角设置中'在网页上显示已执行的模块'关闭，该设置会在流程运行时在页面右下角创建新元素，导致document的最后一个元素选择有误。
3. 每个流程的唯一标识是publicId，开发新流程时应注意publicId是否存在且唯一。
4. 在客户端等流程结束时候的时候async不能等待流程结束。需要使用额外的方法等待流程结束
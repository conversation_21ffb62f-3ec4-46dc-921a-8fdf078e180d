# 🤖 自动文档维护系统

## 🎯 功能特性

- ✅ **自动检测新需求** - AI识别关键词自动创建文档
- ✅ **实时记录开发过程** - 每次代码修改自动更新DevLog
- ✅ **智能功能分类** - 自动识别功能模块并分类
- ✅ **自动归档整理** - 完成后自动移动到双重索引结构
- ✅ **零手动操作** - 完全集成到编程工作流

## 🚀 快速开始

### 1. 复制Cursor Rules
将 `.cursor-rules-auto-docs` 的内容复制到你的Cursor Rules设置中

### 2. 开始使用
正常提出需求，AI会自动维护文档：

```
你: "我要开发一个用户权限管理功能"
AI: ✅ 检测到新需求，自动创建文档...
    📝 PRD已创建: docs/active/用户权限管理/prd.md
    🔧 TSD已创建: docs/active/用户权限管理/tsd.md
    📋 DevLog已创建: docs/active/用户权限管理/devlog.md
    🚀 开始开发...

[编程过程中AI自动记录每个步骤]

你: "功能完成了"
AI: ✨ 功能完成，正在归档...
    🗂️ 已移动到双重索引结构
```

## 📂 文档结构

```
docs/
├── active/                 # 开发中的文档
├── archive/               # 已完成的文档
│   ├── by-time/          # 按时间查找 
│   └── by-feature/       # 按功能查找
└── modules-config.json    # 功能模块配置
```

## 🛠 手动命令

虽然主要是自动化，但也支持手动操作：

```bash
# 创建新文档
node scripts/auto-docs.js create "功能名称" "描述"

# 更新开发日志
node scripts/auto-docs.js update-devlog "功能名称" "更新内容"

# 归档完成的功能
node scripts/auto-docs.js archive "功能名称"
```

## 🎯 核心优势

1. **无缝集成** - 不影响正常编程流程
2. **自动维护** - 无需记住复杂命令
3. **完整记录** - PRD→TSD→DevLog完整链路
4. **智能分类** - 自动识别功能模块
5. **双重索引** - 时间和功能两种查找方式

---

💡 **这就是真正的"自动文档维护"** - 你编程，AI记录！

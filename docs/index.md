# 账号管理项目文档总览

> 📚 项目文档管理系统 - 双重索引结构

## 🔍 快速导航

### 按时间查找
想查看项目发展历程？按时间顺序浏览开发记录？

👉 [时间线索引](./by-time/timeline.md)

```
📅 2023年 → 插件ID获取、插件管理功能
📅 2024年 → 验证码识别、滑块处理、部署优化  
📅 2025年 → 账号管理优化、静态部署、登录功能
```

### 按功能查找
想了解特定功能模块？查找相关技术文档？

👉 [功能模块索引](./by-feature/projects.md)

```
🔐 账号管理   → 登录、权限、用户管理
🤖 验证码识别 → OCR、滑块、图像处理
🧩 插件管理   → Chrome扩展、插件注册
🚀 部署运维   → Docker、Nginx、服务配置
```

## 📋 文档类型说明

| 类型 | 说明 | 包含内容 |
|------|------|----------|
| **PRD** | 产品需求文档 | 需求背景、功能要求、验收标准 |
| **TSD** | 技术方案文档 | 架构设计、技术实现、风险评估 |
| **DevLog** | 开发日志 | 实现过程、问题解决、测试验证 |

## 🛠 工具使用

### 创建新文档
```bash
node scripts/create-docs.js --name "功能名称" --type "账号管理" --date "2025-01-31"
```

### 更新索引
```bash
node scripts/update-index.js
```

### 迁移现有文档
```bash
node scripts/migrate-docs.js
```

## 📊 项目统计

- **总项目数**: 33+ 个开发项目
- **时间跨度**: 2023-2025年
- **功能模块**: 4个主要模块
- **文档类型**: PRD、TSD、DevLog

## 🔗 快速链接

### 最新项目
- [2025-06-账号管理优化](./by-time/2025/06-账号管理优化/)
- [2025-01-登录方式优化](./by-time/2025/01-登录方式优化/)

### 重要里程碑
- [滑块验证码解决方案](./by-feature/验证码识别/2025-07-滑块验证码/)
- [Docker部署方案](./by-feature/部署运维/2024-07-Docker部署/)

## 📝 使用说明

1. **查找文档**: 根据需要选择时间索引或功能索引
2. **阅读顺序**: PRD → TSD → DevLog
3. **关联查找**: 每个文档都有相关文档链接
4. **创建新文档**: 使用自动化脚本保持结构一致

## 🎯 最佳实践

- 新需求先创建PRD明确需求
- 技术实现前完善TSD方案
- 开发过程中持续更新DevLog
- 定期更新索引保持文档同步

---

💡 **提示**: 这是一个动态维护的文档系统，索引会自动更新。如果发现问题或有改进建议，请更新对应的自动化脚本。 
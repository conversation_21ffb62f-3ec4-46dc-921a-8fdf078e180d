@echo off
echo === 测试生产环境API ===
echo 服务器: http://*************** (80端口)
echo 时间: %date% %time%
echo.

echo === 1. 健康检查API ===
powershell -Command "try { $r = Invoke-RestMethod 'http://***************/api/health' -TimeoutSec 5; Write-Host 'Success: ' -NoNewline -ForegroundColor Green; Write-Host $r } catch { Write-Host 'Failed: ' -NoNewline -ForegroundColor Red; Write-Host $_.Exception.Message }"
echo.

echo === 2. 账号查询API - user.lotsmall.cn ===
powershell -Command "try { $h = @{'Content-Type'='application/json'; 'Origin'='chrome-extension://test'}; $b = '{\"loginUrl\": \"https://user.lotsmall.cn/login\"}'; $r = Invoke-RestMethod 'http://***************/api/account/findByLoginUrl' -Method POST -Headers $h -Body $b -TimeoutSec 5; Write-Host 'Success: ' -NoNewline -ForegroundColor Green; Write-Host ($r | ConvertTo-Json -Compress) } catch { Write-Host 'Failed: ' -NoNewline -ForegroundColor Red; Write-Host $_.Exception.Message }"
echo.

echo === 3. 账号查询API - wap.lotsmall.cn ===
powershell -Command "try { $h = @{'Content-Type'='application/json'; 'Origin'='chrome-extension://test'}; $b = '{\"loginUrl\": \"https://wap.lotsmall.cn/login\"}'; $r = Invoke-RestMethod 'http://***************/api/account/findByLoginUrl' -Method POST -Headers $h -Body $b -TimeoutSec 5; Write-Host 'Success: ' -NoNewline -ForegroundColor Green; Write-Host ($r | ConvertTo-Json -Compress) } catch { Write-Host 'Failed: ' -NoNewline -ForegroundColor Red; Write-Host $_.Exception.Message }"
echo.

echo === 测试完成 ===
echo 请检查生产服务器日志，看是否收到了这些请求
pause 
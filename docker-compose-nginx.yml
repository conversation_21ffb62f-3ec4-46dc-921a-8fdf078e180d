version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: account-manage-nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - /opt/account-manage/static:/usr/share/nginx/html:ro
      - ./nginx-config/default.conf:/etc/nginx/conf.d/default.conf:ro
    environment:
      - TZ=Asia/Shanghai
    networks:
      - account-manage-network

networks:
  account-manage-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
-- ===============================================
-- 手动执行SQL - 账号字段扩展 V4
-- 请在Supabase SQL编辑器中手动执行
-- 支持新的Automa工作流入参规范
-- ===============================================

-- 1. 添加选择器模式字段
ALTER TABLE public.accounts
ADD COLUMN IF NOT EXISTS selector_mode VARCHAR(10) DEFAULT 'css';

-- 2. 添加CSS选择器字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS username_css_selector TEXT,
ADD COLUMN IF NOT EXISTS password_css_selector TEXT,
ADD COLUMN IF NOT EXISTS captcha_css_selector TEXT,
ADD COLUMN IF NOT EXISTS captcha_image_css_selector TEXT,
ADD COLUMN IF NOT EXISTS captcha_refresh_css_selector TEXT,
ADD COLUMN IF NOT EXISTS login_button_css_selector TEXT;

-- 3. 添加登录后操作配置字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS post_login_action JSONB;

-- 4. 添加重试和进度配置字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS max_retries INTEGER DEFAULT 3,
ADD COLUMN IF NOT EXISTS show_progress BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS skip_post_action BOOLEAN DEFAULT false;

-- 5. 添加OCR配置字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS ocr_config JSONB;

-- 6. 添加字段注释
COMMENT ON COLUMN public.accounts.selector_mode IS '选择器模式: css 或 xpath';
COMMENT ON COLUMN public.accounts.username_css_selector IS 'CSS选择器：用户名输入框';
COMMENT ON COLUMN public.accounts.password_css_selector IS 'CSS选择器：密码输入框';
COMMENT ON COLUMN public.accounts.captcha_css_selector IS 'CSS选择器：验证码输入框';
COMMENT ON COLUMN public.accounts.captcha_image_css_selector IS 'CSS选择器：验证码图片';
COMMENT ON COLUMN public.accounts.captcha_refresh_css_selector IS 'CSS选择器：验证码刷新按钮';
COMMENT ON COLUMN public.accounts.login_button_css_selector IS 'CSS选择器：登录按钮';
COMMENT ON COLUMN public.accounts.post_login_action IS '登录后操作配置，JSON格式: {type: "click_app_card", targetText: "应用名称"}';
COMMENT ON COLUMN public.accounts.max_retries IS '最大重试次数';
COMMENT ON COLUMN public.accounts.show_progress IS '是否显示进度';
COMMENT ON COLUMN public.accounts.skip_post_action IS '是否跳过登录后操作';
COMMENT ON COLUMN public.accounts.ocr_config IS 'OCR识别配置，JSON格式: {url: "识别API地址", headers: {}}';

-- 7. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_accounts_selector_mode ON public.accounts(selector_mode);
CREATE INDEX IF NOT EXISTS idx_accounts_max_retries ON public.accounts(max_retries);
CREATE INDEX IF NOT EXISTS idx_accounts_show_progress ON public.accounts(show_progress);
CREATE INDEX IF NOT EXISTS idx_accounts_skip_post_action ON public.accounts(skip_post_action);

-- 8. 为现有记录设置默认值
UPDATE public.accounts 
SET 
    selector_mode = 'css',
    max_retries = 3,
    show_progress = true,
    skip_post_action = false
WHERE 
    selector_mode IS NULL OR 
    max_retries IS NULL OR 
    show_progress IS NULL OR 
    skip_post_action IS NULL;

-- 9. 添加约束检查
ALTER TABLE public.accounts 
ADD CONSTRAINT check_selector_mode CHECK (selector_mode IN ('css', 'xpath'));

-- 10. 验证字段添加成功
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'accounts' 
AND column_name IN (
    'selector_mode', 
    'username_css_selector', 
    'password_css_selector', 
    'captcha_css_selector',
    'captcha_image_css_selector',
    'captcha_refresh_css_selector',
    'login_button_css_selector',
    'post_login_action',
    'max_retries',
    'show_progress',
    'skip_post_action',
    'ocr_config'
)
ORDER BY ordinal_position; 
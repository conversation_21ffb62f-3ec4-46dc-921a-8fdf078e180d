-- =================================================================
-- 数据库结构更新 V4 - 账号字段扩展
-- 支持新的Automa工作流入参规范
-- 创建时间: 2025-01-30
-- =================================================================

-- 1. 添加选择器模式字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS selector_mode VARCHAR(10) DEFAULT 'css';

-- 2. 添加CSS选择器字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS username_css_selector TEXT,
ADD COLUMN IF NOT EXISTS password_css_selector TEXT,
ADD COLUMN IF NOT EXISTS captcha_css_selector TEXT,
ADD COLUMN IF NOT EXISTS captcha_image_css_selector TEXT,
ADD COLUMN IF NOT EXISTS captcha_refresh_css_selector TEXT,
ADD COLUMN IF NOT EXISTS login_button_css_selector TEXT;

-- 3. 添加登录后操作配置字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS post_login_action JSONB;

-- 4. 添加重试和进度配置字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS max_retries INTEGER DEFAULT 3,
ADD COLUMN IF NOT EXISTS show_progress BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS skip_post_action BOOLEAN DEFAULT false;

-- 5. 添加OCR配置字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS ocr_config JSONB;

-- 6. 添加字段注释
COMMENT ON COLUMN public.accounts.selector_mode IS '选择器模式: css 或 xpath';
COMMENT ON COLUMN public.accounts.username_css_selector IS 'CSS选择器：用户名输入框';
COMMENT ON COLUMN public.accounts.password_css_selector IS 'CSS选择器：密码输入框';
COMMENT ON COLUMN public.accounts.captcha_css_selector IS 'CSS选择器：验证码输入框';
COMMENT ON COLUMN public.accounts.captcha_image_css_selector IS 'CSS选择器：验证码图片';
COMMENT ON COLUMN public.accounts.captcha_refresh_css_selector IS 'CSS选择器：验证码刷新按钮';
COMMENT ON COLUMN public.accounts.login_button_css_selector IS 'CSS选择器：登录按钮';
COMMENT ON COLUMN public.accounts.post_login_action IS '登录后操作配置，JSON格式: {type: "click_app_card", targetText: "应用名称"}';
COMMENT ON COLUMN public.accounts.max_retries IS '最大重试次数';
COMMENT ON COLUMN public.accounts.show_progress IS '是否显示进度';
COMMENT ON COLUMN public.accounts.skip_post_action IS '是否跳过登录后操作';
COMMENT ON COLUMN public.accounts.ocr_config IS 'OCR识别配置，JSON格式: {url: "识别API地址", headers: {}}';

-- 7. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_accounts_selector_mode ON public.accounts(selector_mode);
CREATE INDEX IF NOT EXISTS idx_accounts_max_retries ON public.accounts(max_retries);
CREATE INDEX IF NOT EXISTS idx_accounts_show_progress ON public.accounts(show_progress);
CREATE INDEX IF NOT EXISTS idx_accounts_skip_post_action ON public.accounts(skip_post_action);

-- 8. 为现有记录设置默认值
UPDATE public.accounts 
SET 
    selector_mode = 'css',
    max_retries = 3,
    show_progress = true,
    skip_post_action = false
WHERE 
    selector_mode IS NULL OR 
    max_retries IS NULL OR 
    show_progress IS NULL OR 
    skip_post_action IS NULL;

-- 9. 添加约束检查
ALTER TABLE public.accounts 
ADD CONSTRAINT check_selector_mode CHECK (selector_mode IN ('css', 'xpath'));

-- 10. 创建示例数据（可选）
-- 为测试账号添加示例配置
UPDATE public.accounts 
SET 
    post_login_action = '{"type": "click_app_card", "targetText": "智游宝数智平台"}',
    ocr_config = '{"url": "https://test-aliwap.lotsmall.cn/lotsAi/api/ocr/recognize", "headers": {"Cookie": "tr_de_id=ZHYtSEhOUlZRWkVXSFZI"}}'
WHERE name LIKE '%测试%' OR name LIKE '%demo%';

-- 11. 验证字段添加成功
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'accounts' 
AND column_name IN (
    'selector_mode', 
    'username_css_selector', 
    'password_css_selector', 
    'captcha_css_selector',
    'captcha_image_css_selector',
    'captcha_refresh_css_selector',
    'login_button_css_selector',
    'post_login_action',
    'max_retries',
    'show_progress',
    'skip_post_action',
    'ocr_config'
)
ORDER BY ordinal_position;

-- 12. 显示执行结果
DO $$
BEGIN
    RAISE NOTICE '=== 数据库V4更新完成 ===';
    RAISE NOTICE '新增字段:';
    RAISE NOTICE '- selector_mode: 选择器模式 (css/xpath)';
    RAISE NOTICE '- *_css_selector: CSS选择器字段 (6个)';
    RAISE NOTICE '- post_login_action: 登录后操作配置 (JSON)';
    RAISE NOTICE '- max_retries: 最大重试次数';
    RAISE NOTICE '- show_progress: 显示进度';
    RAISE NOTICE '- skip_post_action: 跳过登录后操作';
    RAISE NOTICE '- ocr_config: OCR识别配置 (JSON)';
    RAISE NOTICE '总计新增: 12个字段';
END $$;

-- =================================================================
-- 字段映射说明
-- =================================================================
-- 
-- 新入参规范 -> 数据库字段映射:
-- 
-- 1. 基础信息:
--    loginUrl -> login_url (已存在)
--    redirectUrl -> redirect_url (已存在)
--    credentials.username -> username (已存在)
--    credentials.password -> encrypted_password (已存在)
--
-- 2. 选择器配置:
--    selectorMode -> selector_mode (新增)
--    selectors.username -> username_css_selector (新增)
--    selectors.password -> password_css_selector (新增)
--    selectors.captcha -> captcha_css_selector (新增)
--    selectors.captchaImage -> captcha_image_css_selector (新增)
--    selectors.captchaRefresh -> captcha_refresh_css_selector (新增)
--    selectors.loginButton -> login_button_css_selector (新增)
--
-- 3. 登录后操作:
--    postLoginAction -> post_login_action (新增, JSON)
--
-- 4. 选项配置:
--    options.maxRetries -> max_retries (新增)
--    options.showProgress -> show_progress (新增)
--    options.skipPostAction -> skip_post_action (新增)
--    options.ocrConfig -> ocr_config (新增, JSON)
--
-- ================================================================= 
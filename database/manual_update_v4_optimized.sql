-- ===============================================
-- 手动执行SQL - 账号字段扩展 V4 (优化版)
-- 请在Supabase SQL编辑器中手动执行
-- 支持新的Automa工作流入参规范 - 优化设计
-- ===============================================

-- 1. 添加选择器模式字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS selector_mode VARCHAR(10) DEFAULT 'css';

-- 2. 重命名现有选择器字段 (复用现有字段)
ALTER TABLE public.accounts 
RENAME COLUMN username_xpath TO username_selector;
ALTER TABLE public.accounts 
RENAME COLUMN password_xpath TO password_selector;
ALTER TABLE public.accounts 
RENAME COLUMN login_button_xpath TO login_button_selector;
ALTER TABLE public.accounts 
RENAME COLUMN captcha_xpath TO captcha_image_selector;
ALTER TABLE public.accounts 
RENAME COLUMN captcha_input_xpath TO captcha_selector;

-- 3. 新增验证码刷新按钮选择器
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS captcha_refresh_selector TEXT;

-- 4. 添加登录后操作字段 (不使用JSON)
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS post_action_type VARCHAR(20) DEFAULT 'click_app_card',
ADD COLUMN IF NOT EXISTS post_action_target TEXT;

-- 5. 添加重试和进度配置字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS max_retries INTEGER DEFAULT 3,
ADD COLUMN IF NOT EXISTS show_progress BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS skip_post_action BOOLEAN DEFAULT false;

-- 6. 添加OCR配置字段 (简化为URL)
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS ocr_config_url TEXT;

-- 7. 添加字段注释
COMMENT ON COLUMN public.accounts.selector_mode IS '选择器模式: css 或 xpath';
COMMENT ON COLUMN public.accounts.username_selector IS '用户名输入框选择器 (支持CSS/XPath)';
COMMENT ON COLUMN public.accounts.password_selector IS '密码输入框选择器 (支持CSS/XPath)';
COMMENT ON COLUMN public.accounts.captcha_selector IS '验证码输入框选择器 (支持CSS/XPath)';
COMMENT ON COLUMN public.accounts.captcha_image_selector IS '验证码图片选择器 (支持CSS/XPath)';
COMMENT ON COLUMN public.accounts.captcha_refresh_selector IS '验证码刷新按钮选择器 (支持CSS/XPath)';
COMMENT ON COLUMN public.accounts.login_button_selector IS '登录按钮选择器 (支持CSS/XPath)';
COMMENT ON COLUMN public.accounts.post_action_type IS '登录后操作类型: click_app_card, redirect, none';
COMMENT ON COLUMN public.accounts.post_action_target IS '登录后操作目标 (应用名称或URL)';
COMMENT ON COLUMN public.accounts.max_retries IS '最大重试次数';
COMMENT ON COLUMN public.accounts.show_progress IS '是否显示进度';
COMMENT ON COLUMN public.accounts.skip_post_action IS '是否跳过登录后操作';
COMMENT ON COLUMN public.accounts.ocr_config_url IS 'OCR识别API地址';

-- 8. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_accounts_selector_mode ON public.accounts(selector_mode);
CREATE INDEX IF NOT EXISTS idx_accounts_post_action_type ON public.accounts(post_action_type);
CREATE INDEX IF NOT EXISTS idx_accounts_max_retries ON public.accounts(max_retries);
CREATE INDEX IF NOT EXISTS idx_accounts_show_progress ON public.accounts(show_progress);
CREATE INDEX IF NOT EXISTS idx_accounts_skip_post_action ON public.accounts(skip_post_action);

-- 9. 为现有记录设置默认值
UPDATE public.accounts 
SET 
    selector_mode = 'xpath',  -- 现有数据使用xpath模式
    post_action_type = 'click_app_card',
    max_retries = 3,
    show_progress = true,
    skip_post_action = false
WHERE 
    selector_mode IS NULL OR 
    post_action_type IS NULL OR
    max_retries IS NULL OR 
    show_progress IS NULL OR 
    skip_post_action IS NULL;

-- 10. 添加约束检查
ALTER TABLE public.accounts 
ADD CONSTRAINT check_selector_mode CHECK (selector_mode IN ('css', 'xpath'));

ALTER TABLE public.accounts 
ADD CONSTRAINT check_post_action_type CHECK (post_action_type IN ('click_app_card', 'redirect', 'none'));

-- 11. 验证字段变更成功
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'accounts' 
AND column_name IN (
    'selector_mode', 
    'username_selector', 
    'password_selector', 
    'captcha_selector',
    'captcha_image_selector',
    'captcha_refresh_selector',
    'login_button_selector',
    'post_action_type',
    'post_action_target',
    'max_retries',
    'show_progress',
    'skip_post_action',
    'ocr_config_url'
)
ORDER BY ordinal_position; 
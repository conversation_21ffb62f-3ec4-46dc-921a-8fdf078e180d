# 数据库设置说明

## Supabase 项目设置步骤111

1. 前往 [Supabase](https://supabase.com) 创建新项目
2. 在项目设置中获取项目URL和匿名密钥
3. 在SQL编辑器中执行以下SQL语句：

```sql
-- 创建environments表
CREATE TABLE IF NOT EXISTS environments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建accounts表
CREATE TABLE IF NOT EXISTS accounts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    environment_id UUID REFERENCES environments(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    login_url TEXT NOT NULL,
    username TEXT NOT NULL,
    encrypted_password TEXT NOT NULL,
    username_xpath TEXT,
    password_xpath TEXT,
    captcha_xpath TEXT,
    login_button_xpath TEXT,
    merchant_id TEXT,
    task_id TEXT,
    platform_name TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 启用RLS (Row Level Security)
ALTER TABLE environments ENABLE ROW LEVEL SECURITY;
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;

-- 创建environments表的RLS策略
CREATE POLICY "Users can only see their own environments" ON environments
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own environments" ON environments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own environments" ON environments
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own environments" ON environments
    FOR DELETE USING (auth.uid() = user_id);

-- 创建accounts表的RLS策略
CREATE POLICY "Users can only see their own accounts" ON accounts
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own accounts" ON accounts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own accounts" ON accounts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own accounts" ON accounts
    FOR DELETE USING (auth.uid() = user_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为environments表添加更新时间触发器
CREATE TRIGGER update_environments_updated_at 
    BEFORE UPDATE ON environments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 为accounts表添加更新时间触发器
CREATE TRIGGER update_accounts_updated_at 
    BEFORE UPDATE ON accounts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_environments_user_id ON environments(user_id);
CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_accounts_environment_id ON accounts(environment_id);
CREATE INDEX IF NOT EXISTS idx_accounts_name ON accounts(name);
CREATE INDEX IF NOT EXISTS idx_accounts_login_url ON accounts(login_url);
```

## 环境变量配置

创建 `.env.local` 文件并添加以下配置：

```env
NEXT_PUBLIC_SUPABASE_URL=你的Supabase项目URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=你的Supabase匿名密钥
NODE_ENV=development
```

## 测试数据

可以插入一些测试数据：

```sql
-- 插入测试环境
INSERT INTO environments (user_id, name) VALUES 
((SELECT id FROM auth.users LIMIT 1), '生产环境'),
((SELECT id FROM auth.users LIMIT 1), '测试环境'),
((SELECT id FROM auth.users LIMIT 1), '开发环境');

-- 插入测试账号（使用提供的测试数据）
INSERT INTO accounts (
    user_id, 
    environment_id, 
    name, 
    login_url, 
    username, 
    encrypted_password, 
    captcha_xpath, 
    login_button_xpath
) VALUES (
    (SELECT id FROM auth.users LIMIT 1),
    (SELECT id FROM environments WHERE name = '测试环境' LIMIT 1),
    'LotSmall测试账号111',
    'https://test-aliuser.lotsmall.cn/usercenter/login',
    '***********',
    'Admin@123', -- 注意：这里应该是加密后的密码
    '//*[@id="app"]/div/div[2]/div[1]/div/div[3]/div/div/form/div[2]/div[2]/div/div[1]/div/input',
    '//*[@id="app"]/div/div[2]/div[1]/div/div[3]/div/div/form/div[2]/button'
);

```

-- 账号分类管理功能 - 数据库初始化脚本
-- 创建时间: 2025-01-25
-- 说明: 此脚本用于初始化账号分类管理功能的数据库结构和基础数据

-- 1. 创建账号类型表
CREATE TABLE IF NOT EXISTS account_types (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  color VARCHAR(7) DEFAULT '#6366f1',
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. 为accounts表添加account_type字段（如果不存在）
ALTER TABLE accounts ADD COLUMN IF NOT EXISTS account_type INTEGER DEFAULT 0;

-- 3. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_accounts_account_type ON accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_account_types_sort_order ON account_types(sort_order);

-- 4. 插入默认账号类型数据
INSERT INTO account_types (name, description, color, sort_order) VALUES
('默认账号', '未分类的账号', '#6b7280', 0),
('个人账号', '个人使用的账号', '#3b82f6', 1),
('店铺账号', '店铺相关账号', '#10b981', 2),
('散客账号', '散客相关账号', '#f59e0b', 3),
('测试账号', '用于测试的账号', '#8b5cf6', 4),
('客服账号', '客服相关账号', '#ef4444', 5)
ON CONFLICT (name) DO NOTHING;

-- 5. 查看结果
SELECT 
    at.id,
    at.name,
    at.description,
    at.color,
    at.sort_order,
    COUNT(a.id) as account_count
FROM account_types at
LEFT JOIN accounts a ON a.account_type = at.id AND a.is_deleted = false
GROUP BY at.id, at.name, at.description, at.color, at.sort_order
ORDER BY at.sort_order;

-- 6. 查看账号类型分布统计
SELECT 
    COALESCE(at.name, '未知类型') as type_name,
    a.account_type,
    COUNT(*) as count
FROM accounts a
LEFT JOIN account_types at ON a.account_type = at.id
WHERE a.is_deleted = false
GROUP BY a.account_type, at.name
ORDER BY a.account_type;

-- 执行完成后，你可以在前端看到以下功能：
-- ✅ 账号类型筛选器（带颜色指示）
-- ✅ 编辑账号时可以选择类型
-- ✅ 各类型账号数量统计
-- ✅ 筛选结果实时更新

COMMENT ON TABLE account_types IS '账号类型配置表';
COMMENT ON COLUMN account_types.name IS '类型名称';
COMMENT ON COLUMN account_types.description IS '类型描述';
COMMENT ON COLUMN account_types.color IS '类型颜色（16进制）';
COMMENT ON COLUMN account_types.sort_order IS '排序权重';
COMMENT ON COLUMN accounts.account_type IS '账号类型ID，关联account_types表'; 
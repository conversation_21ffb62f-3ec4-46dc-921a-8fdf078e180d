-- =================================================================
-- 数据库结构更新 V3 - 账号字段扩展
-- 为Automa工作流增加新字段支持
-- 创建时间: 2025-01-30
-- =================================================================

-- 1. 添加登录后跳转地址字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS redirect_url TEXT;

-- 2. 添加工作台名称字段（仅店铺账号使用）
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS workspace_name TEXT;

-- 3. 添加强制登录标识字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS force_login BOOLEAN DEFAULT FALSE;

-- 4. 添加最后访问时间字段（用于跟踪登录状态）
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS last_access_at TIMESTAMPTZ DEFAULT NOW();

-- 5. 添加字段注释
COMMENT ON COLUMN public.accounts.redirect_url IS '登录成功后的跳转地址URL';
COMMENT ON COLUMN public.accounts.workspace_name IS '工作台名称（仅店铺账号类型使用）';
COMMENT ON COLUMN public.accounts.force_login IS '是否强制登录：true-强制登录，false-正常登录';
COMMENT ON COLUMN public.accounts.last_access_at IS '最后访问时间，用于跟踪账号使用情况';

-- 6. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_accounts_force_login ON public.accounts(force_login);
CREATE INDEX IF NOT EXISTS idx_accounts_last_access_at ON public.accounts(last_access_at);
CREATE INDEX IF NOT EXISTS idx_accounts_business_type ON public.accounts(business_type);

-- 7. 为现有记录设置默认值
UPDATE public.accounts 
SET force_login = FALSE, last_access_at = NOW()
WHERE force_login IS NULL OR last_access_at IS NULL;

-- 8. 创建业务规则检查约束
-- 确保店铺账号可以有工作台名称，散客账号工作台名称应为空
-- 注意：这个约束是软性的，通过应用层控制，数据库层允许灵活性

-- 9. 更新触发器以自动更新last_access_at
CREATE OR REPLACE FUNCTION update_last_access_time()
RETURNS TRIGGER AS $$
BEGIN
    -- 仅在特定操作时更新last_access_at
    IF TG_OP = 'UPDATE' AND OLD.last_access_at IS DISTINCT FROM NEW.last_access_at THEN
        NEW.last_access_at = NOW();
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 提交更改
COMMIT; 
-- =================================================================
-- 数据库结构更新
-- 本文件记录了所有对数据库结构的修改，请按顺序执行。
-- =================================================================

-- 版本 1.1.0 更新
-- 添加自定义XPath字段
ALTER TABLE public.accounts
ADD COLUMN IF NOT EXISTS username_xpath TEXT,
ADD COLUMN IF NOT EXISTS password_xpath TEXT,
ADD COLUMN IF NOT EXISTS captcha_xpath TEXT,
ADD COLUMN IF NOT EXISTS login_button_xpath TEXT,
ADD COLUMN IF NOT EXISTS captcha_input_xpath TEXT;

-- 添加字段注释
COMMENT ON COLUMN public.accounts.username_xpath IS '自定义用户名输入框的XPath选择器';
COMMENT ON COLUMN public.accounts.password_xpath IS '自定义密码输入框的XPath选择器';
COMMENT ON COLUMN public.accounts.captcha_xpath IS '验证码图片的XPath选择器';
COMMENT ON COLUMN public.accounts.login_button_xpath IS '登录按钮的XPath选择器';
COMMENT ON COLUMN public.accounts.captcha_input_xpath IS '验证码输入框的XPath选择器';

-- 更新现有记录的注释（如果需要）
UPDATE environments
SET name = '默认环境'
WHERE name IS NULL;

ALTER TABLE environments
ALTER COLUMN name SET NOT NULL;

-- =================================================================
-- 版本 1.2.0 更新 - 2025-01-30
-- 账号管理功能优化
-- =================================================================

-- 添加账号类型字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS account_type INTEGER DEFAULT 0;

-- 添加字段注释
COMMENT ON COLUMN public.accounts.account_type IS '账号类型：0-默认账号（所有人可见），1-个人账号（仅创建者可见）';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_accounts_account_type ON accounts(account_type);

-- 为现有账号设置默认类型
UPDATE public.accounts 
SET account_type = 0 
WHERE account_type IS NULL;

-- 删除现有的账号查看策略
DROP POLICY IF EXISTS "Users can only see their own accounts" ON accounts;

-- 创建新的账号查看权限策略
CREATE POLICY "账号查看权限策略" ON accounts
FOR SELECT USING (
  account_type = 0 OR  -- 默认账号所有人可见
  auth.uid() = user_id -- 个人账号仅创建者可见
);

-- 保留其他操作的权限策略（插入、更新、删除仍限制为创建者）
CREATE POLICY IF NOT EXISTS "Users can insert their own accounts" ON accounts
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own accounts" ON accounts
FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own accounts" ON accounts
FOR DELETE USING (auth.uid() = user_id);

-- =================================================================
-- 版本 1.3.0 更新 - 2025-01-30
-- 添加账号描述字段和复制优化
-- =================================================================

-- 添加账号描述字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS description TEXT;

-- 添加字段注释
COMMENT ON COLUMN public.accounts.description IS '账号描述信息';

-- ================================================================= 
-- =================================================================
-- 版本 1.4.0 更新 - 2025-07-XX
-- one-test账号类型字段扩展
-- =================================================================

-- 添加one-test账号专用字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS task_id VARCHAR(64),
ADD COLUMN IF NOT EXISTS platform_name VARCHAR(64),
ADD COLUMN IF NOT EXISTS merchant_info_id VARCHAR(64);

-- 添加字段注释
COMMENT ON COLUMN public.accounts.task_id IS 'one-test账号专用：任务ID（taskId）';
COMMENT ON COLUMN public.accounts.platform_name IS 'one-test账号专用：平台名称（platformName）';
COMMENT ON COLUMN public.accounts.merchant_info_id IS 'one-test账号专用：商户信息ID（merchantInfoId）'; 
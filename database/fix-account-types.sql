-- 账号分类管理功能 - 修复设计方案 (修复版本)
-- 创建时间: 2025-01-25
-- 说明: 分离归属权限和业务类型两个维度
-- 修复: 处理RLS策略依赖问题

-- 1. 首先删除依赖于account_type字段的RLS策略
DO $$
BEGIN
    -- 删除可能存在的RLS策略
    DROP POLICY IF EXISTS "账号查看权限策略" ON accounts;
    DROP POLICY IF EXISTS "account_view_policy" ON accounts;
    DROP POLICY IF EXISTS "account_select_policy" ON accounts;
    DROP POLICY IF EXISTS "account_insert_policy" ON accounts;
    DROP POLICY IF EXISTS "account_update_policy" ON accounts;
    DROP POLICY IF EXISTS "account_delete_policy" ON accounts;
    
    -- 如果有其他依赖account_type的策略，在这里添加
    RAISE NOTICE '已删除依赖account_type字段的RLS策略';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '删除RLS策略时出现错误: %', SQLERRM;
END $$;

-- 2. 删除之前错误的account_types表（如果存在）
DROP TABLE IF EXISTS account_types CASCADE;

-- 3. 为accounts表添加正确的字段
-- ownership_type: 归属类型 (0=公共账号, 1=个人账号)
-- business_type: 业务类型 (0=未分类, 1=店铺账号, 2=散客账号, 3=测试账号, 4=客服账号)
ALTER TABLE accounts ADD COLUMN IF NOT EXISTS ownership_type INTEGER DEFAULT 0;
ALTER TABLE accounts ADD COLUMN IF NOT EXISTS business_type INTEGER DEFAULT 0;

-- 4. 迁移现有数据并删除旧字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'accounts' AND column_name = 'account_type') THEN
        -- 数据迁移映射
        -- 0=默认账号 -> ownership_type=0, business_type=0
        -- 1=个人账号 -> ownership_type=1, business_type=0  
        -- 2=店铺账号 -> ownership_type=0, business_type=1
        -- 3=散客账号 -> ownership_type=0, business_type=2
        -- 4=测试账号 -> ownership_type=0, business_type=3
        -- 5=客服账号 -> ownership_type=0, business_type=4
        
        UPDATE accounts SET 
            ownership_type = CASE 
                WHEN account_type = 1 THEN 1  -- 个人账号
                ELSE 0  -- 其他都是公共账号
            END,
            business_type = CASE 
                WHEN account_type = 2 THEN 1  -- 店铺账号
                WHEN account_type = 3 THEN 2  -- 散客账号
                WHEN account_type = 4 THEN 3  -- 测试账号
                WHEN account_type = 5 THEN 4  -- 客服账号
                ELSE 0  -- 未分类
            END
        WHERE account_type IS NOT NULL;
        
        RAISE NOTICE '数据迁移完成';
        
        -- 现在可以安全删除旧字段了
        ALTER TABLE accounts DROP COLUMN account_type;
        RAISE NOTICE '已删除account_type字段';
    ELSE
        RAISE NOTICE 'account_type字段不存在，跳过迁移';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '数据迁移过程中出现错误: %', SQLERRM;
END $$;

-- 5. 创建索引
CREATE INDEX IF NOT EXISTS idx_accounts_ownership_type ON accounts(ownership_type);
CREATE INDEX IF NOT EXISTS idx_accounts_business_type ON accounts(business_type);

-- 6. 添加字段注释
COMMENT ON COLUMN accounts.ownership_type IS '归属类型: 0=公共账号(所有人可见), 1=个人账号(仅创建者可见)';
COMMENT ON COLUMN accounts.business_type IS '业务类型: 0=未分类, 1=店铺账号, 2=散客账号, 3=测试账号, 4=客服账号';

-- 7. 重建基础的RLS策略（根据需要调整）
DO $$
BEGIN
    -- 重建账号查看策略（基于新的字段结构）
    CREATE POLICY "账号查看权限策略_新" ON accounts
        FOR SELECT
        USING (
            -- 公共账号：所有认证用户可见
            ownership_type = 0
            OR
            -- 个人账号：仅创建者可见
            (ownership_type = 1 AND auth.uid()::text = user_id)
        );
    
    RAISE NOTICE '已重建RLS策略';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '重建RLS策略时出现错误: %', SQLERRM;
END $$;

-- 8. 查看迁移结果
SELECT 
    ownership_type,
    CASE 
        WHEN ownership_type = 0 THEN '公共账号'
        WHEN ownership_type = 1 THEN '个人账号'
        ELSE '未知'
    END as ownership_name,
    business_type,
    CASE 
        WHEN business_type = 0 THEN '未分类'
        WHEN business_type = 1 THEN '店铺账号'
        WHEN business_type = 2 THEN '散客账号'
        WHEN business_type = 3 THEN '测试账号'
        WHEN business_type = 4 THEN '客服账号'
        ELSE '未知类型'
    END as business_name,
    COUNT(*) as count
FROM accounts 
WHERE is_deleted = false OR is_deleted IS NULL
GROUP BY ownership_type, business_type
ORDER BY ownership_type, business_type;

-- 9. 显示执行结果
DO $$
BEGIN
    RAISE NOTICE '=== 数据库迁移完成 ===';
    RAISE NOTICE '新字段结构:';
    RAISE NOTICE '- ownership_type (归属维度): 0=公共账号, 1=个人账号';
    RAISE NOTICE '- business_type (业务维度): 0=未分类, 1=店铺, 2=散客, 3=测试, 4=客服';
    RAISE NOTICE '请刷新前端页面查看新的Tab界面！';
END $$;

-- 新的字段设计说明:
-- ownership_type (归属维度):
--   0 = 公共账号 - 所有人都可以看到和使用（受权限控制）
--   1 = 个人账号 - 只有创建者可以看到和编辑
--
-- business_type (业务维度):  
--   0 = 未分类
--   1 = 店铺账号
--   2 = 散客账号  
--   3 = 测试账号
--   4 = 客服账号 
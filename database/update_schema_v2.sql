-- 账号管理系统数据库结构更新 V2
-- 添加创建时间、更新时间、删除标识字段

-- 1. 添加删除标识字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- 2. 添加创建时间字段（如果不存在）
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW();

-- 3. 添加更新时间字段（如果不存在）
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- 4. 为现有记录设置时间戳（如果字段为空）
UPDATE public.accounts 
SET created_at = NOW(), updated_at = NOW()
WHERE created_at IS NULL OR updated_at IS NULL;

-- 5. 添加字段注释
COMMENT ON COLUMN public.accounts.is_deleted IS '逻辑删除标识：false-正常，true-已删除';
COMMENT ON COLUMN public.accounts.created_at IS '账号创建时间';
COMMENT ON COLUMN public.accounts.updated_at IS '账号最后更新时间';

-- 6. 创建索引
CREATE INDEX IF NOT EXISTS idx_accounts_is_deleted ON public.accounts(is_deleted);
CREATE INDEX IF NOT EXISTS idx_accounts_updated_at ON public.accounts(updated_at);
CREATE INDEX IF NOT EXISTS idx_accounts_created_at ON public.accounts(created_at);

-- 7. 创建更新时间自动更新触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 8. 为accounts表添加更新时间触发器
DROP TRIGGER IF EXISTS update_accounts_updated_at ON public.accounts;
CREATE TRIGGER update_accounts_updated_at
    BEFORE UPDATE ON public.accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 9. 更新RLS策略，排除已删除的记录
DROP POLICY IF EXISTS "账号查看权限策略" ON public.accounts;

CREATE POLICY "账号查看权限策略" ON public.accounts
FOR SELECT USING (
  is_deleted = FALSE AND (
    account_type = 0 OR 
    account_type IS NULL OR
    auth.uid() = user_id
  )
);

-- 10. 创建删除权限策略
DROP POLICY IF EXISTS "账号删除权限策略" ON public.accounts;

CREATE POLICY "账号删除权限策略" ON public.accounts
FOR UPDATE USING (
  -- 超管可以删除任何账号，普通用户只能删除自己的账号
  auth.email() = '<EMAIL>' OR 
  auth.uid() = user_id
);

-- 11. 创建插入权限策略
DROP POLICY IF EXISTS "账号插入权限策略" ON public.accounts;

CREATE POLICY "账号插入权限策略" ON public.accounts
FOR INSERT WITH CHECK (
  auth.uid() = user_id
);

-- 12. 创建更新权限策略
DROP POLICY IF EXISTS "账号更新权限策略" ON public.accounts;

CREATE POLICY "账号更新权限策略" ON public.accounts
FOR UPDATE USING (
  -- 超管可以更新任何账号，普通用户只能更新自己的账号
  auth.email() = '<EMAIL>' OR 
  auth.uid() = user_id
);

-- 提交更改
COMMIT; 
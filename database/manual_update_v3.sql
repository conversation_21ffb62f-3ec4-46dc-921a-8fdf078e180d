-- ===============================================
-- 手动执行SQL - 账号字段扩展 V3
-- 请在Supabase SQL编辑器中手动执行
-- ===============================================

-- 1. 添加登录后跳转地址字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS redirect_url TEXT;

-- 2. 添加工作台名称字段（仅店铺账号使用）
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS workspace_name TEXT;

-- 3. 添加强制登录标识字段
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS force_login BOOLEAN DEFAULT FALSE;

-- 4. 添加最后访问时间字段（用于跟踪登录状态）
ALTER TABLE public.accounts 
ADD COLUMN IF NOT EXISTS last_access_at TIMESTAMPTZ DEFAULT NOW();

-- 5. 添加字段注释
COMMENT ON COLUMN public.accounts.redirect_url IS '登录成功后的跳转地址URL';
COMMENT ON COLUMN public.accounts.workspace_name IS '工作台名称（仅店铺账号类型使用）';
COMMENT ON COLUMN public.accounts.force_login IS '是否强制登录：true-强制登录，false-正常登录';
COMMENT ON COLUMN public.accounts.last_access_at IS '最后访问时间，用于跟踪账号使用情况';

-- 6. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_accounts_force_login ON public.accounts(force_login);
CREATE INDEX IF NOT EXISTS idx_accounts_last_access_at ON public.accounts(last_access_at);
CREATE INDEX IF NOT EXISTS idx_accounts_business_type ON public.accounts(business_type);

-- 7. 为现有记录设置默认值
UPDATE public.accounts 
SET force_login = FALSE, last_access_at = NOW()
WHERE force_login IS NULL OR last_access_at IS NULL;

-- 8. 验证字段添加成功
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default
FROM information_schema.columns 
WHERE table_name = 'accounts' 
AND column_name IN ('redirect_url', 'workspace_name', 'force_login', 'last_access_at')
ORDER BY ordinal_position; 
@echo off
echo Cleaning up processes and files...

echo Killing node processes...
taskkill /f /im node.exe 2>nul

echo Waiting for processes to close...
timeout /t 3 /nobreak >nul

echo Removing .next directory...
rmdir /s /q .next 2>nul

echo Removing out directory...
rmdir /s /q out 2>nul

echo Moving API directory...
if exist app\api (
    if exist api_backup rmdir /s /q api_backup
    move app\api api_backup
    echo API directory moved
)

echo Setting environment variables...
set NODE_ENV=production
set STATIC_EXPORT=true

echo Starting build...
npm run build

echo Checking results...
if exist out\index.html (
    echo SUCCESS: Build completed!
    echo Files in out directory:
    dir out
) else (
    echo ERROR: Build failed or no index.html found
)

echo Restoring API directory...
if exist api_backup (
    move api_backup app\api
    echo API directory restored
)

echo Done!
pause
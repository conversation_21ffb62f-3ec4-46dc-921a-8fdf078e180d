# Playwright 使用现有浏览器配置指南

## 🎯 目标
让 Playwright 使用你自己的浏览器配置，保留登录状态、cookies、扩展程序等。

## 🔧 方法一：使用现有Chrome用户数据目录

### 1. 找到Chrome用户数据目录
```bash
# Windows
C:\Users\<USER>\AppData\Local\Google\Chrome\User Data

# macOS  
~/Library/Application Support/Google/Chrome

# Linux
~/.config/google-chrome
```

### 2. 使用 launchPersistentContext
```javascript
const { chromium } = require('playwright');

const browser = await chromium.launchPersistentContext(
    'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data',
    {
        headless: false,
        channel: 'chrome', // 使用系统Chrome
        ignoreDefaultArgs: ['--disable-extensions'], // 保留扩展程序
    }
);
```

## 🔧 方法二：连接到正在运行的浏览器

### 1. 启动Chrome调试模式
```bash
# Windows
chrome.exe --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome-debug"

# macOS
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome-debug"

# Linux
google-chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome-debug"
```

### 2. 连接到运行中的浏览器
```javascript
const browser = await chromium.connectOverCDP('http://localhost:9222');
```

## 🔧 方法三：创建专用配置文件（推荐）

### 优点
- 不影响日常使用的浏览器
- 可以保持自动化专用的登录状态
- 更安全，不会意外操作个人数据

### 代码示例
```javascript
const context = await chromium.launchPersistentContext(
    './automation_profile', // 专用配置文件目录
    {
        headless: false,
        viewport: { width: 1280, height: 720 }
    }
);
```

## 🚀 完整实用脚本

```javascript
const { chromium } = require('playwright');
const path = require('path');
const os = require('os');

async function setupBrowserWithLogin() {
    // 获取Chrome用户数据目录
    let userDataDir;
    const platform = os.platform();
    
    if (platform === 'win32') {
        userDataDir = path.join(os.homedir(), 'AppData', 'Local', 'Google', 'Chrome', 'User Data');
    } else if (platform === 'darwin') {
        userDataDir = path.join(os.homedir(), 'Library', 'Application Support', 'Google', 'Chrome');
    } else {
        userDataDir = path.join(os.homedir(), '.config', 'google-chrome');
    }
    
    console.log('使用用户数据目录:', userDataDir);
    
    // 启动浏览器
    const context = await chromium.launchPersistentContext(userDataDir, {
        headless: false,
        channel: 'chrome',
        viewport: { width: 1280, height: 720 },
        ignoreDefaultArgs: ['--disable-extensions'],
    });
    
    const page = context.pages()[0] || await context.newPage();
    
    // 访问目标页面
    await page.goto('https://test-aliuser.lotsmall.cn/usercenter/personal/worktable');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    console.log('页面标题:', await page.title());
    console.log('当前URL:', page.url());
    
    // 检查登录状态
    if (page.url().includes('login')) {
        console.log('❌ 需要登录，请在浏览器中完成登录');
        console.log('登录后脚本将继续运行...');
        
        // 等待跳转到工作台
        await page.waitForURL('**/worktable', { timeout: 60000 });
    }
    
    console.log('✅ 已登录，开始查找卡片...');
    
    // 查找分时预约系统卡片
    await findTargetCard(page);
    
    // 保持浏览器打开以便操作
    console.log('浏览器将保持打开状态...');
    
    return { context, page };
}

async function findTargetCard(page) {
    try {
        // 等待卡片加载
        await page.waitForSelector('div', { timeout: 10000 });
        
        // 查找所有可能的卡片元素
        const selectors = [
            'div.my-app__item',
            '.my-app__item',
            'div[class*="item"]',
            'div[class*="card"]'
        ];
        
        for (const selector of selectors) {
            const elements = await page.$$(selector);
            if (elements.length > 0) {
                console.log(`找到 ${elements.length} 个 "${selector}" 元素`);
                
                // 检查每个元素的内容
                for (let i = 0; i < elements.length; i++) {
                    const text = await elements[i].textContent();
                    if (text.includes('分时预约系统')) {
                        console.log(`🎯 找到目标卡片在位置 ${i + 1}`);
                        console.log(`CSS选择器: ${selector}:nth-child(${i + 1})`);
                        
                        // 高亮显示找到的元素
                        await elements[i].evaluate(el => {
                            el.style.border = '3px solid red';
                            el.style.backgroundColor = 'yellow';
                        });
                        
                        return `${selector}:nth-child(${i + 1})`;
                    }
                }
            }
        }
        
        console.log('❌ 未找到包含"分时预约系统"的卡片');
        return null;
        
    } catch (error) {
        console.error('查找卡片时出错:', error);
        return null;
    }
}

// 运行脚本
setupBrowserWithLogin().catch(console.error);
```

## ⚠️ 注意事项

1. **关闭其他Chrome实例**: 使用用户数据目录时，确保没有其他Chrome实例在使用该目录
2. **权限问题**: 某些系统可能需要管理员权限
3. **扩展程序**: 某些扩展可能影响自动化，可以选择性禁用
4. **安全性**: 使用个人配置文件时要小心，建议使用专用配置文件

## 🔍 调试技巧

1. **检查用户数据目录是否正确**:
```javascript
const fs = require('fs');
console.log('目录存在:', fs.existsSync(userDataDir));
```

2. **查看浏览器启动参数**:
```javascript
console.log('启动参数:', context._options);
```

3. **监听页面事件**:
```javascript
page.on('console', msg => console.log('浏览器控制台:', msg.text()));
page.on('pageerror', err => console.log('页面错误:', err.message));
``` 
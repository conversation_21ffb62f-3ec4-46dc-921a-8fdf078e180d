# 本地构建Docker镜像并推送到服务器

## 1. 在本地创建Dockerfile

```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM node:18-alpine AS runner
WORKDIR /app

ENV NODE_ENV=production

# 复制必要文件
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/database ./database

# 暴露端口
EXPOSE 3001

# 启动应用
CMD ["npm", "run", "start"]
```

## 2. 在本地构建Docker镜像

```bash
# 在项目根目录执行
docker build -t account-manage:latest .
```

## 3. 保存镜像为tar文件

```bash
docker save -o account-manage.tar account-manage:latest
```

## 4. 将镜像传输到服务器

```bash
scp account-manage.tar root@192.168.202.230:/home/<USER>/
```

## 5. 在服务器上加载镜像并运行容器

```bash
# 登录到服务器
ssh root@192.168.202.230

# 加载镜像
cd /home/<USER>
docker load -i account-manage.tar

# 停止旧容器（如果存在）
docker rm -f account-manage || true

# 运行新容器
docker run -d \
  --name account-manage \
  --restart unless-stopped \
  -p 3001:3001 \
  -v /home/<USER>/database:/app/database \
  account-manage:latest
```

## 6. 验证部署

访问 http://192.168.202.230:3001 验证应用是否正常运行。 
{"accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "cmn-Hans-CN"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 864, "left": 10, "maximized": false, "right": 1306, "top": 10, "work_area_bottom": 1040, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17230, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "5df3655f-563f-4792-8556-79a56d7ca51c", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.7204.23", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "在这里，您可以找到适用于 Chromium 的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "应用商店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1179\\chrome-win\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chromium PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1179\\chrome-win\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gaia_cookie": {"changed_time": **********.064675, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "org.chromium.windows"}, "google": {"services": {"signin_scoped_device_id": "b6a4659d-b227-4671-8ef5-65180e4e9b0f"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "zh-CN,zh"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "pinned_tabs": [], "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]lotsmall.cn,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://test-aliuser.lotsmall.cn:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://test-aliuser.lotsmall.cn:443,*": {"last_modified": "13395390563188825", "setting": {"lastEngagementTime": 1.3395361763188718e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.7204.23", "creation_time": "1339*************", "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "*****************", "last_time_obsolete_http_credentials_removed": **********.505642, "last_time_password_store_metrics_reported": **********.494481, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "您的 Chromium", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "protection": {"macs": {"account_values": {"browser": {"show_home_button": "D353E586F1742BF0D56C7F73C422370FDD45E4556AD145D090F552BF65AC4D5E"}, "extensions": {"ui": {"developer_mode": "5B6C3A156AF61001D0A881112CB430C20235D9145F0CB6DE95100D28ABA22687"}}, "homepage": "363176B641A2E77C0FF7DB838F10B23C61C88AAADA7BC3E3087EB498232D95DB", "homepage_is_newtabpage": "71D560AA8F40FE0505F28C6C6901F26C474E40F27EC52C68D80A349DAEBF6CA6", "session": {"restore_on_startup": "00D7A7054CFF6BD34DD2AFB30B4330D44251A4AFF2278F102AF6A042091F5E24", "startup_urls": "48DF82FA3A378DF26537767A61337B651AF8B1E1FB6AD0218F604B3082543D1E"}}, "browser": {"show_home_button": "3FDEF651D8DD66EDABB3F562B3CAF37F1CD3C42C3815CA9980113AAE67E42904"}, "default_search_provider_data": {"template_url_data": "A1059ADAFCC2C67989B70EF6EC7A61B31209E4CD3908E608D47A28B83FC60899"}, "enterprise_signin": {"policy_recovery_token": "841763447F4EA330BC1C312DE9C1FFA76EBB1F8619A7DD8F7552061497E50D28"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "054115FE64D2AFF5499DBD82EA2EB807617A0966E4BCB6CEE2DFF26DF81337F6", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "B894413C2F563F276BFC0324B4A394D963B88510FEDBD4907084D043E76F94CF"}, "ui": {"developer_mode": "0453661BD15E4BB586DA856684DA06AAB7D61326590FF16D91EF43AE7BCB4FE1"}}, "google": {"services": {"account_id": "A413D06E842E475CC29003F15D2A145FEF97ADE8B3C26337BB5C47F9AED6383B", "last_signed_in_username": "49FFEC4EFD5F05ABFE14042BAAEE0A2290433D1A05F9D7517938AF2F1CF23A1D", "last_username": "CBFE4059EF7CD8BF74AE5D70E4883164D7742A388C8E397BFEF118E1B93D3A6A"}}, "homepage": "40C2FBF4F3E68E70714B000A62DA7BA64724DA2A9B4036ADCD4F51B6C8ADCB0D", "homepage_is_newtabpage": "1CC7AE391C665BA7268A0816D81EB7DFAAF6724E364E261D451BA286E0378324", "media": {"cdm": {"origin_data": "C72BD49BC79B1F3BA79114686C841C42ED8E7CB07605387E30C8FDA58772EE93"}, "storage_id_salt": "F7D9B8E735EF20B58664E85E29578ECDB0255C31158FD3FB8F547780114E81C3"}, "pinned_tabs": "705BB9849013F0D52D470CCF77A7BB09987510A227BDE677CF83105A918FC50A", "prefs": {"preference_reset_time": "F0A83C90676FE50F388014750FC499C8E508A70CF796BBCBC7542B74F373E317"}, "safebrowsing": {"incidents_sent": "B0E5CA17DBA82FF08F62E0051B92411D76B525B15FCAC995DE594868CAB0DF0B"}, "search_provider_overrides": "4B9E2CB45515A5503F9936865CF09E9D853C2A47638D8F7A13A6901379B846D6", "session": {"restore_on_startup": "84F927189B5C4F79E43508ACB1AB0DF2B500700068F3468CE34D95DA199AB233", "startup_urls": "AEDB4254046966D89CAA9C714875525F965F0B7E5F4CB26657C3AA026CB38BE2"}}}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13395323702", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ3LyCu53f5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADELu9grud3+UX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13395196799000000", "uma_in_sql_start_time": "13395323702496198"}, "sessions": {"event_log": [{"crashed": false, "time": "13395323702491858", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395390563130845", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["zh-CN", "en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138"}}
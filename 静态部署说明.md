# 静态文件部署到nginx说明

## 概述
这套脚本可以将Next.js应用构建为静态文件，并自动部署到服务器的Docker nginx容器中。

## 部署架构
```
本地开发环境 → 构建静态文件 → 上传到服务器 → Docker nginx容器
```

## 前置条件

### 本地环境
- Node.js和npm已安装
- 项目已配置为静态导出模式
- 具有SSH访问服务器的权限

### 服务器环境
- Linux服务器（支持Docker）
- SSH访问权限
- 网络连接正常

## 脚本说明

### 1. `deploy-static-to-server.sh`
**功能**: 将本地构建的静态文件打包并上传到服务器
**用法**: 
```bash
./deploy-static-to-server.sh [服务器IP] [用户名] [目标路径]
```
**默认参数**:
- 服务器IP: ***************
- 用户名: root
- 目标路径: /opt/account-manage/static

### 2. `setup-docker-nginx.sh`
**功能**: 在服务器上创建和启动Docker nginx容器
**用法**:
```bash
./setup-docker-nginx.sh [服务器IP] [用户名] [静态文件路径] [端口]
```
**默认参数**:
- 服务器IP: ***************
- 用户名: root
- 静态文件路径: /opt/account-manage/static
- 端口: 80

### 3. `deploy-all.sh`
**功能**: 一键完成构建、上传、部署的完整流程
**用法**:
```bash
./deploy-all.sh [服务器IP] [用户名] [端口]
```

## 使用步骤

### 快速部署（推荐）
```bash
# 使用Docker Compose一键部署到默认服务器
./deploy-all-compose.sh

# 部署到指定服务器
./deploy-all-compose.sh ************* ubuntu 8080

# 使用原版部署脚本
./deploy-all.sh
```

**注意：部署脚本假设out目录已包含构建好的静态文件，不会自动执行npm run build**

### 分步部署
```bash
# 1. 构建静态文件（手动执行）
npm run build

# 2. 上传到服务器
./deploy-static-to-server.sh

# 3. 设置nginx容器
./setup-docker-nginx.sh
# 或使用Docker Compose版本
./setup-docker-nginx-compose.sh
```

## 部署后管理

### 查看容器状态
```bash
ssh root@*************** 'docker ps | grep account-manage-nginx'
```

### 查看容器日志
```bash
ssh root@*************** 'docker logs account-manage-nginx'
```

### 重启服务
```bash
ssh root@*************** 'docker restart account-manage-nginx'
```

### 停止服务
```bash
ssh root@*************** 'docker stop account-manage-nginx'
```

### 更新部署
```bash
# 先构建最新版本
npm run build

# 重新部署最新版本（Docker Compose）
./deploy-all-compose.sh

# 或使用原版脚本
./deploy-all.sh
```

## 成功部署示例
- ✅ 已成功部署到: http://***************
- ✅ 容器状态: account-manage-nginx 运行中
- ✅ 部署时间: 2025-01-27 23:28
- ✅ 服务响应: HTTP 200 正常

## nginx配置特性

### SPA路由支持
- 所有路径都会回退到 `index.html`
- 支持前端路由

### 缓存策略
- 静态资源缓存1年
- HTML文件不缓存，确保更新及时生效

### CORS支持
- 添加了CORS头，支持跨域访问

### 安全设置
- 添加了基本的安全头
- 禁止访问隐藏文件

## 故障排除

### 1. 上传失败
- 检查SSH连接是否正常
- 确认服务器用户权限
- 验证网络连通性

### 2. 容器启动失败
- 检查Docker是否正常运行
- 确认端口是否被占用
- 查看容器日志排查问题

### 3. 访问404
- 确认防火墙设置
- 检查容器端口映射
- 验证静态文件是否正确上传

### 4. 样式或脚本加载失败
- 检查nginx配置中的路径设置
- 确认静态资源文件完整性
- 查看浏览器控制台错误信息

## 自定义配置

### 修改nginx配置
编辑服务器上的配置文件:
```bash
/opt/account-manage/nginx-config/default.conf
```

### 更改端口
```bash
./deploy-all.sh *************** root 8080
```

### 自定义静态文件路径
```bash
./deploy-static-to-server.sh *************** root /var/www/html
./setup-docker-nginx.sh *************** root /var/www/html 80
```

## 注意事项

1. **SSH密钥**: 建议配置SSH密钥认证，避免输入密码
2. **防火墙**: 确保服务器防火墙开放相应端口
3. **权限**: 确保用户有Docker操作权限
4. **备份**: 脚本会自动备份现有文件，但建议定期备份重要数据
5. **更新**: 每次代码更新后，只需运行 `./deploy-all.sh` 即可

## 目录结构

部署后的服务器目录结构:
```
/opt/account-manage/
├── static/                 # 静态文件目录
│   ├── index.html
│   ├── _next/
│   └── ...
├── nginx-config/           # nginx配置
│   └── default.conf
└── static_backup_*/        # 备份目录
``` 
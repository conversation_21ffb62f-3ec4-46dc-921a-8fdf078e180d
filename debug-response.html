<!DOCTYPE html>
<html>
<head>
    <title>API响应调试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>API响应调试</h1>
    
    <div>
        <h2>测试本地API (localhost:3001)</h2>
        <button onclick="testLocalApi()">测试本地API</button>
        <div id="local-result"></div>
    </div>
    
    <div>
        <h2>测试生产API (***************)</h2>
        <button onclick="testProdApi()">测试生产API</button>
        <div id="prod-result"></div>
    </div>
    
    <script>
        async function testLocalApi() {
            const resultDiv = document.getElementById('local-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch('http://localhost:3001/api/plugin/list');
                const contentType = response.headers.get('content-type');
                const text = await response.text();
                
                resultDiv.innerHTML = `
                    <h3>本地API结果:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>Content-Type:</strong> ${contentType}</p>
                    <p><strong>响应长度:</strong> ${text.length}</p>
                    <p><strong>响应前200字符:</strong></p>
                    <pre style="background:#f0f0f0;padding:10px;border-radius:5px;">${text.substring(0, 200)}</pre>
                    <p><strong>是否为JSON:</strong> ${isJson(text)}</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p style="color:red;">错误: ${error.message}</p>`;
            }
        }
        
        async function testProdApi() {
            const resultDiv = document.getElementById('prod-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch('http://***************/api/plugin/list');
                const contentType = response.headers.get('content-type');
                const text = await response.text();
                
                resultDiv.innerHTML = `
                    <h3>生产API结果:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>Content-Type:</strong> ${contentType}</p>
                    <p><strong>响应长度:</strong> ${text.length}</p>
                    <p><strong>响应前200字符:</strong></p>
                    <pre style="background:#f0f0f0;padding:10px;border-radius:5px;">${text.substring(0, 200)}</pre>
                    <p><strong>是否为JSON:</strong> ${isJson(text)}</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p style="color:red;">错误: ${error.message}</p>`;
            }
        }
        
        function isJson(str) {
            try {
                JSON.parse(str);
                return '是';
            } catch (e) {
                return '否';
            }
        }
    </script>
</body>
</html> 
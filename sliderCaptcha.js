/**
 * 检测页面中是否存在滑块验证码
 * @returns {Array} - 可能的滑块验证码元素数组
 */
function detectSliderCaptcha() {
  console.log('检测滑块验证码...');
  
  try {
    // 用户提供的特定选择器
    const specificSelectors = [
      '//*[@id="puzzle"]/div[2]/div[2]/img',
      '//*[@id="puzzle"]/div[2]/div[2]'
    ];
    
    // 尝试使用XPath查找特定元素
    for (const xpathSelector of specificSelectors) {
      try {
        console.log(`尝试使用XPath选择器: ${xpathSelector}`);
        const xpathResult = document.evaluate(
          xpathSelector, 
          document, 
          null, 
          XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, 
          null
        );
        
        if (xpathResult.snapshotLength > 0) {
          const element = xpathResult.snapshotItem(0);
          console.log(`通过XPath找到特定滑块元素:`, {
            tagName: element.tagName,
            className: element.className,
            id: element.id
          });
          return [element];
        }
      } catch (xpathError) {
        console.log(`XPath选择器错误: ${xpathError.message}`);
      }
    }
    
    // 如果特定选择器没有找到元素，继续使用通用选择器
    console.log('特定选择器未找到元素，使用通用选择器');
    
    // 常见滑块验证码的选择器
    const selectors = [
      // 通用滑块选择器
      '.slider-btn', '.slider_button', '.sliderBtn', '.slider-button',
      '.geetest_slider_button', '.gt_slider_knob', '.yidun_slider',
      '.slide-btn', '.slide-button', '.slide_button', '.slideBtn',
      '.drag-btn', '.drag-button', '.drag_button', '.dragBtn',
      // 特定验证码系统的选择器
      '.geetest_slider', '.yidun_slider', '.nc_iconfont.btn_slide',
      // 属性选择器
      '[role="slider"]', '[draggable="true"]',
      // 图片滑块
      '.slider-img', '.captcha-slider-img',
      // 容器选择器
      '.slider-container', '.captcha-slider', '.slider-captcha',
      '.geetest_container', '.yidun_panel', '.nc-container'
    ];
    
    // 查找所有可能的滑块元素
    let elements = [];
    
    // 使用选择器查找
    for (const selector of selectors) {
      const found = document.querySelectorAll(selector);
      if (found.length > 0) {
        console.log(`通过选择器 "${selector}" 找到 ${found.length} 个元素`);
        elements = [...elements, ...Array.from(found)];
      }
    }
    
    // 查找带有特定样式的元素
    const allElements = document.querySelectorAll('*');
    for (const el of allElements) {
      const style = window.getComputedStyle(el);
      
      // 检查是否有拖动相关的样式
      if (
        style.cursor === 'pointer' || 
        style.cursor === 'move' || 
        style.cursor === 'grab' ||
        el.getAttribute('draggable') === 'true'
      ) {
        // 检查元素大小，滑块通常较小
        const rect = el.getBoundingClientRect();
        if (rect.width > 0 && rect.width < 100 && rect.height > 0 && rect.height < 100) {
          console.log('找到可能的滑块元素(基于样式):', {
            tagName: el.tagName,
            className: el.className,
            id: el.id,
            cursor: style.cursor
          });
          elements.push(el);
        }
      }
    }
    
    // 查找可能的滑块图片
    const images = document.querySelectorAll('img');
    for (const img of images) {
      const src = img.src.toLowerCase();
      if (
        src.includes('slider') || 
        src.includes('captcha') || 
        src.includes('slide') ||
        src.includes('puzzle')
      ) {
        console.log('找到可能的滑块图片:', {
          src: img.src,
          width: img.width,
          height: img.height
        });
        elements.push(img);
      }
    }
    
    // 查找含有特定关键词的元素
    const textNodes = document.querySelectorAll('*');
    for (const node of textNodes) {
      const text = node.textContent.toLowerCase();
      if (
        text.includes('滑动验证') || 
        text.includes('拖动滑块') || 
        text.includes('slide to verify') ||
        text.includes('drag the slider')
      ) {
        console.log('找到含有滑块验证相关文本的元素:', {
          text: node.textContent.substring(0, 20),
          tagName: node.tagName
        });
        // 查找其附近的可能滑块元素
        const parent = node.parentElement;
        if (parent) {
          const sliders = parent.querySelectorAll('div, img, span');
          elements = [...elements, ...Array.from(sliders)];
        }
      }
    }
    
    console.log(`总共找到 ${elements.length} 个可能的滑块验证码元素`);
    return elements;
  } catch (error) {
    console.error('检测滑块验证码时出错:', error);
    return [];
  }
}

/**
 * 为特定类型的滑块验证码模拟拖动行为
 * @param {HTMLElement} slider - 滑块元素
 * @param {number} distance - 需要移动的距离
 * @returns {Promise<void>}
 */
async function simulateDragForSpecificSlider(slider, distance) {
  console.log('使用特定滑块拖动方法...');
  
  try {
    // 获取滑块元素的位置
    const rect = slider.getBoundingClientRect();
    const startX = rect.left + rect.width / 2;
    const startY = rect.top + rect.height / 2;
    
    // 检查是否是特定的puzzle div
    const isPuzzleDiv = slider.matches('#puzzle div') || 
                       (slider.id && slider.id.includes('puzzle')) ||
                       (() => {
                         try {
                           const xpathResult = document.evaluate(
                             '//*[@id="puzzle"]/div[2]/div[2]', 
                             document, 
                             null, 
                             XPathResult.FIRST_ORDERED_NODE_TYPE, 
                             null
                           );
                           return xpathResult.singleNodeValue === slider;
                         } catch (e) {
                           return false;
                         }
                       })();
    
    if (isPuzzleDiv) {
      console.log('检测到特定的puzzle div滑块，使用left样式值控制滑动');
      
      // 记录原始样式
      const originalLeft = slider.style.left || '0px';
      const originalTransition = slider.style.transition || '';
      
      console.log('滑块原始样式:', {
        left: originalLeft,
        transition: originalTransition
      });
      
      // 设置transition以实现平滑移动
      slider.style.transition = 'left 0.5s ease-in-out';
      
      // 分步移动left值，模拟人类拖动
      const steps = 20;
      const stepDelay = 25; // 每步延迟25ms
      
      console.log(`开始分步修改left值，总步数: ${steps}`);
      
      for (let i = 1; i <= steps; i++) {
        const progress = i / steps;
        const currentLeft = Math.round(progress * distance);
        
        // 设置left值
        slider.style.left = `${currentLeft}px`;
        
        if (i % 5 === 0 || i === 1 || i === steps) {
          console.log(`步骤 ${i}: 设置left值为 ${currentLeft}px`);
        }
        
        // 等待一小段时间
        await new Promise(resolve => setTimeout(resolve, stepDelay));
      }
      
      // 最终设置为目标距离
      slider.style.left = `${distance}px`;
      console.log(`最终设置left值为 ${distance}px`);
      
      // 等待transition完成
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 检查最终位置
      console.log('滑块最终样式:', {
        left: slider.style.left,
        transition: slider.style.transition
      });
      
      console.log('特定puzzle div滑块移动完成');
      return;
    }
    
    // 尝试查找父容器元素
    let puzzleContainer = document.getElementById('puzzle');
    if (!puzzleContainer) {
      // 如果找不到特定ID，尝试查找父元素
      let parent = slider.parentElement;
      while (parent && !puzzleContainer) {
        if (parent.id === 'puzzle' || (parent.className && parent.className.includes('puzzle'))) {
          puzzleContainer = parent;
        } else {
          parent = parent.parentElement;
        }
      }
    }
    
    // 如果找到容器，记录容器信息
    if (puzzleContainer) {
      const containerRect = puzzleContainer.getBoundingClientRect();
      console.log('拼图容器信息:', {
        id: puzzleContainer.id,
        className: puzzleContainer.className,
        width: containerRect.width,
        height: containerRect.height
      });
      
      // 尝试查找可以移动的div
      const movableDiv = puzzleContainer.querySelector('div[style*="left"]') || 
                         puzzleContainer.querySelector('div[style*="transform"]');
      
      if (movableDiv) {
        console.log('找到可移动的div元素:', {
          tagName: movableDiv.tagName,
          className: movableDiv.className,
          id: movableDiv.id,
          style: movableDiv.getAttribute('style')
        });
        
        // 记录原始样式
        const originalLeft = movableDiv.style.left || '0px';
        const originalTransform = movableDiv.style.transform || '';
        
        console.log('可移动div原始样式:', {
          left: originalLeft,
          transform: originalTransform
        });
        
        // 尝试修改left值
        try {
          movableDiv.style.transition = 'left 0.5s ease-in-out';
          movableDiv.style.left = `${distance}px`;
          console.log(`已设置可移动div的left值为 ${distance}px`);
        } catch (e) {
          console.log('设置left值失败:', e);
        }
        
        // 等待transition完成
        await new Promise(resolve => setTimeout(resolve, 500));
        
        console.log('可移动div最终样式:', {
          left: movableDiv.style.left,
          transform: movableDiv.style.transform
        });
        
        return;
      }
    }
    
    console.log('尝试直接修改滑块位置...');
    
    // 1. 尝试直接修改元素样式
    const originalTransform = slider.style.transform;
    const originalLeft = slider.style.left;
    
    // 记录原始样式
    console.log('原始样式:', {
      transform: originalTransform,
      left: originalLeft
    });
    
    // 尝试多种方式修改位置
    try {
      slider.style.left = `${distance}px`;
      console.log('已设置left:', slider.style.left);
    } catch (e) {
      console.log('设置left失败:', e);
    }
    
    try {
      slider.style.transform = `translateX(${distance}px)`;
      console.log('已设置transform:', slider.style.transform);
    } catch (e) {
      console.log('设置transform失败:', e);
    }
    
    // 2. 同时尝试事件模拟方式
    console.log('同时尝试事件模拟方式...');
    
    // 创建触摸事件 (对于移动设备)
    try {
      console.log('尝试触发触摸事件...');
      
      // 触摸开始
      const touchStart = new TouchEvent('touchstart', {
        bubbles: true,
        cancelable: true,
        view: window,
        touches: [
          new Touch({
            identifier: 0,
            target: slider,
            clientX: startX,
            clientY: startY
          })
        ]
      });
      slider.dispatchEvent(touchStart);
      
      // 等待一小段时间
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 触摸移动 (分多步)
      const steps = 20;
      for (let i = 1; i <= steps; i++) {
        const progress = i / steps;
        const moveX = startX + progress * distance;
        
        const touchMove = new TouchEvent('touchmove', {
          bubbles: true,
          cancelable: true,
          view: window,
          touches: [
            new Touch({
              identifier: 0,
              target: slider,
              clientX: moveX,
              clientY: startY
            })
          ]
        });
        slider.dispatchEvent(touchMove);
        
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      
      // 触摸结束
      const touchEnd = new TouchEvent('touchend', {
        bubbles: true,
        cancelable: true,
        view: window,
        touches: []
      });
      slider.dispatchEvent(touchEnd);
      
    } catch (touchError) {
      console.log('触摸事件模拟失败:', touchError);
    }
    
    // 3. 尝试使用鼠标事件
    console.log('尝试使用鼠标事件...');
    
    // 鼠标按下
    const mouseDown = new MouseEvent('mousedown', {
      bubbles: true,
      cancelable: true,
      view: window,
      clientX: startX,
      clientY: startY
    });
    document.dispatchEvent(mouseDown); // 在document上触发
    slider.dispatchEvent(mouseDown);   // 在滑块上触发
    
    // 等待一小段时间
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 鼠标移动 (分多步)
    const steps = 20;
    for (let i = 1; i <= steps; i++) {
      const progress = i / steps;
      const moveX = startX + progress * distance;
      
      const mouseMove = new MouseEvent('mousemove', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: moveX,
        clientY: startY
      });
      document.dispatchEvent(mouseMove); // 在document上触发
      slider.dispatchEvent(mouseMove);   // 在滑块上触发
      
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    // 鼠标释放
    const mouseUp = new MouseEvent('mouseup', {
      bubbles: true,
      cancelable: true,
      view: window,
      clientX: startX + distance,
      clientY: startY
    });
    document.dispatchEvent(mouseUp); // 在document上触发
    slider.dispatchEvent(mouseUp);   // 在滑块上触发
    
    // 4. 尝试模拟拖拽API
    try {
      console.log('尝试模拟HTML5拖拽API...');
      
      // 拖拽开始
      const dragStart = new DragEvent('dragstart', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: startX,
        clientY: startY
      });
      slider.dispatchEvent(dragStart);
      
      // 拖拽中
      const drag = new DragEvent('drag', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: startX + distance / 2,
        clientY: startY
      });
      slider.dispatchEvent(drag);
      
      // 拖拽结束
      const dragEnd = new DragEvent('dragend', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: startX + distance,
        clientY: startY
      });
      slider.dispatchEvent(dragEnd);
      
    } catch (dragError) {
      console.log('拖拽API模拟失败:', dragError);
    }
    
    // 检查滑块是否移动到位
    await new Promise(resolve => setTimeout(resolve, 500));
    const finalRect = slider.getBoundingClientRect();
    console.log('特定滑块最终位置:', {
      left: finalRect.left,
      moved: finalRect.left - rect.left,
      expectedMove: distance,
      currentTransform: slider.style.transform,
      currentLeft: slider.style.left
    });
    
    console.log('特定滑块拖动模拟完成');
  } catch (error) {
    console.error('特定滑块拖动过程中发生错误:', error);
    throw error;
  }
}

/**
 * 模拟人类拖动行为
 * @param {HTMLElement} slider - 滑块元素
 * @param {number} distance - 需要移动的距离
 * @returns {Promise<void>}
 */
async function simulateHumanDrag(slider, distance) {
  console.log(`开始模拟人类拖动，目标距离: ${distance}px`);
  
  try {
    // 记录滑块元素信息
    console.log('滑块元素信息:', {
      tagName: slider.tagName,
      className: slider.className || '无类名',
      id: slider.id || '无ID',
      style: slider.getAttribute('style') || '无样式'
    });
    
    // 检查是否是特定的puzzle div
    const isPuzzleDiv = 
      slider.matches('#puzzle div') || 
      (slider.id && slider.id.includes('puzzle')) ||
      slider.closest('#puzzle') !== null ||
      (() => {
        try {
          const xpathResult = document.evaluate(
            '//*[@id="puzzle"]/div[2]/div[2]', 
            document, 
            null, 
            XPathResult.FIRST_ORDERED_NODE_TYPE, 
            null
          );
          return xpathResult.singleNodeValue === slider;
        } catch (e) {
          return false;
        }
      })();
    
    if (isPuzzleDiv) {
      console.log('检测到特定的puzzle div滑块，直接使用特殊处理方法');
      return await simulateDragForSpecificSlider(slider, distance);
    }
    
    // 获取滑块元素的位置
    const rect = slider.getBoundingClientRect();
    console.log('滑块位置信息:', {
      left: rect.left,
      top: rect.top,
      width: rect.width,
      height: rect.height
    });
    
    const startX = rect.left + rect.width / 2;
    const startY = rect.top + rect.height / 2;
    console.log(`起始坐标: (${startX}, ${startY})`);
    
    // 创建鼠标按下事件
    console.log('准备创建鼠标按下事件...');
    const mouseDown = new MouseEvent('mousedown', {
      bubbles: true,
      cancelable: true,
      view: window,
      clientX: startX,
      clientY: startY
    });
    
    // 记录事件属性
    console.log('mouseDown事件属性:', {
      bubbles: mouseDown.bubbles,
      cancelable: mouseDown.cancelable,
      clientX: mouseDown.clientX,
      clientY: mouseDown.clientY
    });
    
    // 触发鼠标按下事件
    console.log('触发鼠标按下事件...');
    const mouseDownResult = slider.dispatchEvent(mouseDown);
    console.log('鼠标按下事件触发结果:', mouseDownResult ? '成功' : '被取消');
    
    // 等待一小段时间，模拟人类反应
    console.log('等待人类反应时间(100ms)...');
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 分段模拟移动，使轨迹更自然
    const steps = 30;
    const duration = 1000; // 总时长1秒
    
    // 添加随机波动，模拟人手抖动
    function getRandomOffset() {
      return Math.random() * 2 - 1; // -1到1之间的随机值
    }
    
    // 模拟非线性移动速度（先慢后快再慢）
    function easeInOutQuad(t) {
      return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    }
    
    console.log(`开始分段移动，总步数: ${steps}, 总时长: ${duration}ms`);
    
    // 分段移动
    for (let i = 1; i <= steps; i++) {
      const progress = i / steps;
      const easeProgress = easeInOutQuad(progress);
      const moveX = startX + easeProgress * distance + getRandomOffset();
      const moveY = startY + getRandomOffset();
      
      if (i % 5 === 0 || i === 1 || i === steps) {
        console.log(`移动进度: ${Math.round(progress * 100)}%, 坐标: (${moveX.toFixed(2)}, ${moveY.toFixed(2)})`);
      }
      
      const mouseMove = new MouseEvent('mousemove', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: moveX,
        clientY: moveY
      });
      
      try {
        const mouseMoveResult = slider.dispatchEvent(mouseMove);
        if (i === 1 || i === Math.floor(steps/2) || i === steps) {
          console.log(`步骤 ${i}: 鼠标移动事件触发结果:`, mouseMoveResult ? '成功' : '被取消');
        }
        
        // 检查滑块是否真的移动了
        if (i === Math.floor(steps/2)) {
          const currentRect = slider.getBoundingClientRect();
          console.log('滑块中途位置:', {
            left: currentRect.left,
            moved: currentRect.left - rect.left
          });
        }
      } catch (moveError) {
        console.error(`步骤 ${i}: 触发鼠标移动事件时出错:`, moveError);
      }
      
      // 每步之间添加随机延迟
      const stepDelay = (duration / steps) * (0.8 + Math.random() * 0.4);
      await new Promise(resolve => setTimeout(resolve, stepDelay));
    }
    
    // 检查滑块是否移动到位
    const finalRect = slider.getBoundingClientRect();
    console.log('滑块最终位置:', {
      left: finalRect.left,
      moved: finalRect.left - rect.left,
      expectedMove: distance
    });
    
    // 触发鼠标释放事件
    console.log('准备触发鼠标释放事件...');
    const mouseUp = new MouseEvent('mouseup', {
      bubbles: true,
      cancelable: true,
      view: window,
      clientX: startX + distance,
      clientY: startY
    });
    
    console.log('mouseUp事件属性:', {
      bubbles: mouseUp.bubbles,
      cancelable: mouseUp.cancelable,
      clientX: mouseUp.clientX,
      clientY: mouseUp.clientY
    });
    
    const mouseUpResult = slider.dispatchEvent(mouseUp);
    console.log('鼠标释放事件触发结果:', mouseUpResult ? '成功' : '被取消');
    
    // 尝试触发额外的事件，以防某些验证码需要
    console.log('尝试触发额外事件...');
    try {
      // 有些滑块可能需要click事件
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: startX + distance,
        clientY: startY
      });
      slider.dispatchEvent(clickEvent);
      
      // 有些可能需要mouseout事件
      const mouseOutEvent = new MouseEvent('mouseout', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: startX + distance,
        clientY: startY
      });
      slider.dispatchEvent(mouseOutEvent);
    } catch (extraError) {
      console.log('触发额外事件时出错(非关键):', extraError);
    }
    
    console.log('模拟拖动完成');
  } catch (error) {
    console.error('模拟拖动过程中发生错误:', error);
    throw error;
  }
}

/**
 * 分析滑块验证码的结构，找出滑块和背景图片
 * @param {Array} elements - 可能的滑块验证码元素
 * @returns {Object} - 包含滑块和背景图片元素的对象
 */
function analyzeSliderStructure(elements) {
  console.log('开始分析滑块验证码结构...');
  console.log(`找到 ${elements.length} 个可能的滑块相关元素`);
  
  // 记录所有元素的基本信息，帮助调试
  elements.forEach((el, index) => {
    try {
      const rect = el.getBoundingClientRect();
      console.log(`元素 ${index + 1}:`, {
        tagName: el.tagName,
        className: el.className || '无类名',
        id: el.id || '无ID',
        text: (el.textContent || '').substring(0, 20) + (el.textContent && el.textContent.length > 20 ? '...' : ''),
        size: `${rect.width}x${rect.height}`,
        position: `(${rect.left}, ${rect.top})`,
        style: el.getAttribute('style') || '无样式',
        cursor: window.getComputedStyle(el).cursor
      });
    } catch (error) {
      console.log(`元素 ${index + 1} 信息获取失败:`, error);
    }
  });
  
  let slider = null;
  let background = null;
  let container = null;
  
  // 首先尝试查找特定的puzzle div滑块
  try {
    console.log('尝试查找特定的puzzle div滑块...');
    const specificXPath = '//*[@id="puzzle"]/div[2]/div[2]';
    
    const xpathResult = document.evaluate(
      specificXPath, 
      document, 
      null, 
      XPathResult.FIRST_ORDERED_NODE_TYPE, 
      null
    );
    
    if (xpathResult.singleNodeValue) {
      slider = xpathResult.singleNodeValue;
      console.log('找到特定的puzzle div滑块:', {
        tagName: slider.tagName,
        className: slider.className || '无类名',
        id: slider.id || '无ID',
        style: slider.getAttribute('style') || '无样式'
      });
      
      // 尝试查找容器
      container = document.getElementById('puzzle');
      if (container) {
        console.log('找到puzzle容器');
      } else {
        // 如果找不到特定ID，尝试查找父元素
        let parent = slider.parentElement;
        while (parent) {
          if (parent.id === 'puzzle' || (parent.className && parent.className.includes('puzzle'))) {
            container = parent;
            console.log('通过父元素找到puzzle容器');
            break;
          }
          parent = parent.parentElement;
        }
      }
      
      // 在容器中查找背景图片
      if (container) {
        const images = container.querySelectorAll('img');
        for (const img of images) {
          if (img !== slider) {
            background = img;
            console.log('在puzzle容器中找到背景图片');
            break;
          }
        }
      }
      
      return { slider, background, container };
    }
  } catch (error) {
    console.log('查找特定puzzle div滑块时出错:', error);
  }
  
  // 如果没有找到特定的滑块，继续使用通用方法
  console.log('未找到特定的puzzle div滑块，使用通用方法...');
  
  // 查找最可能的滑块元素
  for (const el of elements) {
    // 如果元素是可拖动的或有相关类名，很可能是滑块
    const isSlider = el.getAttribute('draggable') === 'true' || 
        el.classList.contains('slider') || 
        el.classList.contains('drag') ||
        el.style.cursor === 'pointer' ||
        el.style.cursor === 'move' ||
        el.classList.contains('gt_slider_knob') ||
        el.classList.contains('yidun_slider') ||
        el.classList.contains('geetest_slider_button');
    
    if (isSlider) {
      slider = el;
      container = el.parentElement;
      console.log('找到可能的滑块元素:', {
        tagName: el.tagName,
        className: el.className || '无类名',
        id: el.id || '无ID',
        cursor: window.getComputedStyle(el).cursor,
        draggable: el.getAttribute('draggable')
      });
      break;
    }
  }
  
  // 如果没有找到明确的滑块，尝试基于大小和位置推断
  if (!slider && elements.length > 0) {
    console.log('未找到明确的滑块元素，尝试基于大小推断...');
    // 假设最小的元素可能是滑块
    let minArea = Infinity;
    for (const el of elements) {
      const rect = el.getBoundingClientRect();
      const area = rect.width * rect.height;
      console.log(`元素面积: ${area}, 宽x高: ${rect.width}x${rect.height}`);
      if (area > 0 && area < minArea) {
        minArea = area;
        slider = el;
        container = el.parentElement;
      }
    }
    if (slider) {
      console.log('基于大小推断的滑块元素:', {
        tagName: slider.tagName,
        className: slider.className || '无类名',
        id: slider.id || '无ID',
        area: minArea
      });
    }
  }
  
  // 查找背景图片
  if (container) {
    console.log('开始在容器中查找背景图片...');
    console.log('容器信息:', {
      tagName: container.tagName,
      className: container.className || '无类名',
      id: container.id || '无ID',
      childrenCount: container.children.length
    });
    
    // 在容器中查找图片元素
    const images = container.querySelectorAll('img');
    console.log(`容器中找到 ${images.length} 个图片元素`);
    
    for (const img of images) {
      const rect = img.getBoundingClientRect();
      console.log('图片元素:', {
        src: img.src,
        size: `${rect.width}x${rect.height}`,
        visible: rect.width > 0 && rect.height > 0
      });
      
      // 背景图片通常比滑块大
      if (rect.width > 0 && rect.height > 0) {
        background = img;
        console.log('找到可能的背景图片:', img.src);
        break;
      }
    }
    
    // 如果没有找到明确的图片元素，尝试查找带背景图的div
    if (!background) {
      console.log('未找到图片元素，尝试查找带背景图的div...');
      const divs = container.querySelectorAll('div');
      console.log(`容器中找到 ${divs.length} 个div元素`);
      
      for (const div of divs) {
        if (div === slider) continue;
        
        const style = window.getComputedStyle(div);
        console.log('Div背景图:', {
          backgroundImage: style.backgroundImage,
          size: `${div.offsetWidth}x${div.offsetHeight}`
        });
        
        if (style.backgroundImage && style.backgroundImage !== 'none') {
          background = div;
          console.log('找到带背景图的元素:', {
            className: div.className || '无类名',
            id: div.id || '无ID',
            backgroundImage: style.backgroundImage
          });
          break;
        }
      }
    }
  } else {
    console.log('未找到容器元素，无法查找背景图片');
  }
  
  // 如果仍然没有找到背景，尝试从文档中查找
  if (!background) {
    console.log('在容器中未找到背景，尝试从整个文档查找...');
    // 查找所有图片和带背景的div
    const allImages = document.querySelectorAll('img');
    console.log(`文档中找到 ${allImages.length} 个图片元素`);
    
    const allBgDivs = Array.from(document.querySelectorAll('div')).filter(div => {
      const style = window.getComputedStyle(div);
      return style.backgroundImage && style.backgroundImage !== 'none';
    });
    console.log(`文档中找到 ${allBgDivs.length} 个带背景图的div元素`);
    
    // 组合所有可能的背景元素
    const possibleBgs = [...Array.from(allImages), ...allBgDivs];
    
    // 查找与滑块在同一容器或附近的元素
    if (slider) {
      const sliderRect = slider.getBoundingClientRect();
      console.log('滑块位置:', {
        left: sliderRect.left,
        top: sliderRect.top,
        width: sliderRect.width,
        height: sliderRect.height
      });
      
      // 按照与滑块的距离排序
      possibleBgs.sort((a, b) => {
        const aRect = a.getBoundingClientRect();
        const bRect = b.getBoundingClientRect();
        
        const aDist = Math.abs(aRect.top - sliderRect.top);
        const bDist = Math.abs(bRect.top - sliderRect.top);
        
        return aDist - bDist;
      });
      
      // 选择最近的元素作为背景
      if (possibleBgs.length > 0) {
        background = possibleBgs[0];
        const bgRect = background.getBoundingClientRect();
        console.log('基于位置关系找到可能的背景元素:', {
          tagName: background.tagName,
          className: background.className || '无类名',
          id: background.id || '无ID',
          distance: Math.abs(bgRect.top - sliderRect.top),
          size: `${bgRect.width}x${bgRect.height}`
        });
      }
    }
  }
  
  // 最终结果
  console.log('滑块验证码结构分析结果:', {
    foundSlider: !!slider,
    foundBackground: !!background,
    foundContainer: !!container
  });
  
  return { slider, background, container };
}

/**
 * 解决滑块验证码
 * @returns {Promise<boolean>} - 是否成功解决
 */
async function solveSliderCaptcha() {
  try {
    console.log('开始解决滑块验证码...');
    
    // 首先尝试用户提供的特定XPath选择器
    console.log('尝试使用特定XPath选择器...');
    const specificXPath = '//*[@id="puzzle"]/div[2]/div[2]';
    
    let slider = null;
    
    // 尝试使用XPath查找特定元素
    try {
      console.log(`尝试XPath选择器: ${specificXPath}`);
      const xpathResult = document.evaluate(
        specificXPath, 
        document, 
        null, 
        XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, 
        null
      );
      
      if (xpathResult.snapshotLength > 0) {
        slider = xpathResult.snapshotItem(0);
        console.log(`通过特定XPath找到滑块元素:`, {
          tagName: slider.tagName,
          className: slider.className,
          id: slider.id,
          style: slider.getAttribute('style') || '无样式'
        });
      }
    } catch (xpathError) {
      console.log(`XPath选择器错误: ${xpathError.message}`);
    }
    
    // 如果没有通过特定选择器找到，则使用通用检测
    if (!slider) {
      console.log('未通过特定选择器找到滑块，使用通用检测...');
      
      // 检测滑块验证码元素
      console.log('检测滑块验证码元素...');
      const sliderElements = detectSliderCaptcha();
      
      if (!sliderElements || sliderElements.length === 0) {
        console.log('未检测到滑块验证码元素，可能不是滑块验证码或元素选择器需要更新');
        return false;
      }
      
      console.log(`检测到 ${sliderElements.length} 个可能的滑块验证码元素`);
      
      // 分析滑块结构
      console.log('分析滑块验证码结构...');
      const { slider: detectedSlider, background, container } = analyzeSliderStructure(sliderElements);
      
      slider = detectedSlider;
      
      if (!slider) {
        console.error('未找到滑块元素，无法继续');
        return false;
      }
    }
    
    // 查找容器元素
    let container = document.getElementById('puzzle');
    
    // 如果找不到容器，尝试从滑块向上查找
    if (!container && slider) {
      let parent = slider.parentElement;
      while (parent) {
        if (parent.id === 'puzzle' || (parent.className && parent.className.includes('puzzle'))) {
          container = parent;
          break;
        }
        parent = parent.parentElement;
      }
    }
    
    // 估算滑块需要移动的距离
    let distance = 0;
    
    // 如果是特定滑块，使用固定距离或容器宽度的比例
    if (container) {
      const containerWidth = container.getBoundingClientRect().width;
      // 根据用户提供的信息，设置合适的移动距离
      distance = Math.round(containerWidth * 0.8); // 使用容器宽度的80%作为移动距离
      console.log(`使用容器宽度的80%作为移动距离: ${distance}px`);
    } else {
      // 使用固定距离108px，根据用户提供的信息
      distance = 108;
      console.log(`使用固定移动距离: ${distance}px`);
    }
    
    // 模拟人类拖动行为
    console.log(`准备拖动滑块，目标距离: ${distance}px`);
    
    // 尝试最多3次
    let success = false;
    let attempts = 0;
    const maxAttempts = 3;
    
    while (!success && attempts < maxAttempts) {
      attempts++;
      console.log(`尝试拖动滑块 (第 ${attempts}/${maxAttempts} 次)`);
      
      try {
        await simulateHumanDrag(slider, distance);
        
        // 等待验证结果
        console.log('等待验证结果...');
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // 检查是否验证成功
        success = await checkVerificationSuccess();
        console.log(`验证结果: ${success ? '成功' : '失败'}`);
        
        if (success) {
          console.log('滑块验证码解决成功');
          return true;
        } else {
          console.log(`第 ${attempts} 次尝试失败，${attempts < maxAttempts ? '将重试' : '已达最大尝试次数'}`);
          
          // 如果失败但还有重试机会，调整距离再试
          if (attempts < maxAttempts) {
            // 随机调整距离，模拟人类再次尝试的行为
            const adjustment = (Math.random() * 20) - 10; // -10到10之间的随机调整
            distance += adjustment;
            console.log(`调整距离为: ${distance}px (${adjustment > 0 ? '+' : ''}${adjustment.toFixed(2)}px)`);
            
            // 等待一段时间再重试
            const waitTime = 1000 + Math.random() * 1000;
            console.log(`等待 ${waitTime.toFixed(0)}ms 后重试...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
          }
        }
      } catch (error) {
        console.error(`第 ${attempts} 次拖动滑块时出错:`, error);
        
        // 如果是因为元素不可交互导致的错误，尝试重新获取元素
        if (attempts < maxAttempts) {
          console.log('尝试重新获取滑块元素...');
          
          // 重新尝试特定选择器
          try {
            const xpathResult = document.evaluate(
              specificXPath, 
              document, 
              null, 
              XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, 
              null
            );
            
            if (xpathResult.snapshotLength > 0) {
              slider = xpathResult.snapshotItem(0);
              console.log(`重新获取到滑块元素:`, {
                tagName: slider.tagName,
                className: slider.className,
                id: slider.id
              });
            }
          } catch (xpathError) {
            console.log(`XPath选择器错误: ${xpathError.message}`);
          }
          
          if (!slider) {
            const newSliderElements = detectSliderCaptcha();
            const newStructure = analyzeSliderStructure(newSliderElements);
            
            if (newStructure.slider) {
              console.log('成功重新获取滑块元素');
              slider = newStructure.slider;
            } else {
              console.error('重新获取滑块元素失败');
            }
          }
          
          // 等待一段时间再重试
          const waitTime = 1000 + Math.random() * 1000;
          console.log(`等待 ${waitTime.toFixed(0)}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }
    
    console.log(`滑块验证码解决${success ? '成功' : '失败'}`);
    return success;
  } catch (error) {
    console.error('解决滑块验证码过程中发生错误:', error);
    return false;
  }
}

/**
 * 检查验证是否成功
 * @returns {Promise<boolean>} - 是否验证成功
 */
async function checkVerificationSuccess() {
  try {
    console.log('检查验证结果...');
    
    // 等待一小段时间，让验证结果显示
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 检查是否存在成功提示
    const successIndicators = [
      // 成功提示文本
      '验证成功',
      '验证通过',
      'success',
      '成功',
      // 成功图标类
      'success-icon',
      'icon-success'
    ];
    
    // 检查DOM中是否存在成功提示
    for (const indicator of successIndicators) {
      const elements = document.querySelectorAll(`*:contains('${indicator}'), .${indicator}`);
      if (elements.length > 0) {
        console.log(`找到成功指示器: "${indicator}"`);
        return true;
      }
    }
    
    // 检查是否存在失败提示
    const failureIndicators = [
      '验证失败',
      '重试',
      'try again',
      'failed',
      'failure',
      '失败'
    ];
    
    for (const indicator of failureIndicators) {
      const elements = document.querySelectorAll(`*:contains('${indicator}')`);
      if (elements.length > 0) {
        console.log(`找到失败指示器: "${indicator}"`);
        return false;
      }
    }
    
    // 如果没有明确的成功或失败指示，检查滑块是否移动到了终点
    const sliderElements = detectSliderCaptcha();
    if (sliderElements && sliderElements.length > 0) {
      const { slider, container } = analyzeSliderStructure(sliderElements);
      
      if (slider && container) {
        const sliderRect = slider.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        
        // 如果滑块接近容器右侧，可能表示成功
        const distanceToRight = containerRect.right - sliderRect.right;
        console.log(`滑块到容器右侧的距离: ${distanceToRight}px`);
        
        if (distanceToRight < 20) {
          console.log('滑块已接近容器右侧，可能验证成功');
          return true;
        }
      }
    }
    
    console.log('未找到明确的验证结果指示，假定验证失败');
    return false;
  } catch (error) {
    console.error('检查验证结果时出错:', error);
    return false;
  }
}

// 暴露关键函数到全局作用域
window.solveSliderCaptcha = solveSliderCaptcha;
window.detectSliderCaptcha = detectSliderCaptcha;
window.analyzeSliderStructure = analyzeSliderStructure;
window.simulateHumanDrag = simulateHumanDrag;
window.simulateDragForSpecificSlider = simulateDragForSpecificSlider;
window.checkVerificationSuccess = checkVerificationSuccess;

// 添加自检函数，用于验证模块是否正确加载
window.checkSliderCaptchaModule = function() {
  console.log('滑块验证码模块自检...');
  
  const functions = {
    'solveSliderCaptcha': typeof solveSliderCaptcha === 'function',
    'detectSliderCaptcha': typeof detectSliderCaptcha === 'function',
    'analyzeSliderStructure': typeof analyzeSliderStructure === 'function',
    'simulateHumanDrag': typeof simulateHumanDrag === 'function',
    'simulateDragForSpecificSlider': typeof simulateDragForSpecificSlider === 'function',
    'checkVerificationSuccess': typeof checkVerificationSuccess === 'function'
  };
  
  console.log('滑块验证码模块函数可用性:', functions);
  
  const allAvailable = Object.values(functions).every(available => available);
  console.log(`滑块验证码模块状态: ${allAvailable ? '正常' : '异常'}`);
  
  return allAvailable;
};

// 自动执行自检
(function() {
  console.log('滑块验证码处理模块已加载');
  
  try {
    // 延迟执行自检，确保所有函数都已定义
    setTimeout(() => {
      window.checkSliderCaptchaModule();
    }, 100);
  } catch (error) {
    console.error('滑块验证码模块自检失败:', error);
  }
})(); 
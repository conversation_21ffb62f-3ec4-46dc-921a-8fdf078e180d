# 使用HTTP代理部署Docker容器

## 方法一：在Docker容器内部设置代理

修改run-docker.sh脚本，在Docker容器内部设置HTTP_PROXY环境变量：

```bash
#!/bin/bash
docker rm -f account-manage || true
docker run -d \
  --name account-manage \
  --restart unless-stopped \
  -p 3001:3001 \
  -v /home/<USER>/app \
  -w /app \
  -e HTTP_PROXY=http://**************:9090 \
  -e HTTPS_PROXY=http://**************:9090 \
  node:18-alpine \
  sh -c "npm config set proxy http://**************:9090 && npm config set https-proxy http://**************:9090 && npm install && npm run build && npm run start"

echo "容器已启动，访问 http://***************:3001 查看结果"
```

## 方法二：在Dockerfile中设置代理

创建一个新的Dockerfile，内置代理设置：

```dockerfile
FROM node:18-alpine

WORKDIR /app

# 设置代理
ENV HTTP_PROXY=http://**************:9090
ENV HTTPS_PROXY=http://**************:9090

# 配置npm使用代理
RUN npm config set proxy http://**************:9090 && \
    npm config set https-proxy http://**************:9090

COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

EXPOSE 3001

CMD ["npm", "run", "start"]
```

## 方法三：配置Docker守护进程使用代理

在服务器上配置Docker守护进程全局使用代理：

```bash
# SSH到服务器
ssh root@***************

# 创建或编辑Docker服务配置目录
mkdir -p /etc/systemd/system/docker.service.d

# 创建代理配置文件
cat > /etc/systemd/system/docker.service.d/http-proxy.conf << 'EOL'
[Service]
Environment="HTTP_PROXY=http://**************:9090"
Environment="HTTPS_PROXY=http://**************:9090"
Environment="NO_PROXY=localhost,127.0.0.1"
EOL

# 重启Docker服务
systemctl daemon-reload
systemctl restart docker

# 验证代理设置
systemctl show --property=Environment docker
```

## 使用方法

1. 选择上述任一方法配置代理
2. 确保**************:9090代理可以从***************访问
3. 重新构建或启动Docker容器
4. 检查日志确认依赖下载是否成功

## 注意事项

- 确保代理服务器允许来自***************的连接
- 如果代理需要认证，需要在URL中添加用户名和密码：`********************************************`
- 某些代理可能会限制npm或yarn的某些操作，需要相应调整代理设置 
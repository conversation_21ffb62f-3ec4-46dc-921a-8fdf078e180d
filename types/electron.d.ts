export interface ElectronAPI {
  startLogin: (accountData: {
    loginUrl: string;
    username: string;
    encryptedPassword: string;
    usernameXpath?: string;
    passwordXpath?: string;
    captchaXpath?: string;
    loginButtonXpath?: string;
  }) => Promise<{ success: boolean; message: string }>;
  
  onLoginStatus: (callback: (status: string) => void) => () => void;
  
  encryptPassword: (password: string) => Promise<string>;
  
  testXPath: (data: { url: string; xpath: string }) => Promise<{ success: boolean; message: string }>;
  
  minimizeWindow: () => Promise<void>;
  maximizeWindow: () => Promise<void>;
  closeWindow: () => Promise<void>;
  
  platform: string;
}

declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
} 
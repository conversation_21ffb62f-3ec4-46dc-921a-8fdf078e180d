// types/chrome.d.ts

declare namespace chrome {
  namespace runtime {
    interface MessageSender {
      tab?: chrome.tabs.Tab;
      frameId?: number;
      id?: string;
      url?: string;
      tlsChannelId?: string;
    }

    function sendMessage(
      extensionId: string,
      message: any,
      options?: object,
      responseCallback?: (response: any) => void
    ): void;

    function sendMessage(
      message: any,
      options?: object,
      responseCallback?: (response: any) => void
    ): void;
    
    const lastError: {
      message?: string;
    } | undefined;
  }
} 
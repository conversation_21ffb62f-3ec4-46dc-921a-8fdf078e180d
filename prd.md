# 产品需求文档 (PRD): 账号管理系统 - 自动登录Chrome插件

## 1. 概述

本项目旨在开发一个Chrome浏览器插件，以替代当前Electron应用中基于Playwright的自动登录功能。新方案将通过插件实现更流畅、更集成化的用户体验。当用户在账号管理Web界面点击"自动登录"时，插件会自动在新标签页中打开目标网站，并利用从主应用获取的账户信息（用户名、密码、XPath等）自动填充表单并执行登录。

## 2. 项目目标

- **提升用户体验**: 替换掉弹出新的Playwright浏览器窗口的侵入式登录方式，改为在用户当前的Chrome浏览器中无缝打开新标签页进行登录，体验更佳。
- **功能解耦**: 将自动登录功能从Electron主进程中解耦出来，使其成为一个独立的浏览器扩展，便于维护和未来扩展。
- **增强健壮性**: 利用Chrome扩展的API，更稳定、更可靠地与页面进行交互。

## 3. 功能需求

| 需求ID | 功能描述 | 优先级 |
| :--- | :--- | :--- |
| **FEAT-01** | **创建Chrome插件基础结构** | **高** |
| | - 创建 `manifest.json`，声明插件名称、版本、权限（如`tabs`, `storage`, `scripting`）和后台脚本、内容脚本。 | |
| | - 创建后台脚本 (`background.js`)，用于处理核心逻辑和通信。 | |
| | - 创建内容脚本 (`content.js`)，用于注入到目标登录页面并执行DOM操作。 | |
| **FEAT-02** | **主应用与插件通信** | **高** |
| | - 主应用（Next.js）的"自动登录"按钮被点击时，需要将该账号的完整信息（登录URL、用户名、密码、各字段XPath）发送给插件。 | |
| | - 通信方式：主应用通过 `chrome.runtime.sendMessage` 将数据发送给插件的 `background.js`。插件的`manifest.json`中需配置`externally_connectable`以允许主应用域名的连接。 | |
| **FEAT-03** | **插件执行自动登录** | **高** |
| | - `background.js` 接收到登录请求后，使用 `chrome.tabs.create` 打开一个新的标签页，URL为登录URL。 | |
| | - 待新标签页加载完成后，`background.js` 使用 `chrome.scripting.executeScript` 注入 `content.js` 并将账户信息传递给它。 | |
| | - `content.js` 接收到信息后，使用XPath查找到对应的输入框（用户名、密码、验证码）和登录按钮。 | |
| | - `content.js` 自动填充表单字段。 | |
| | - `content.js` 自动点击登录按钮。 | |
| **FEAT-04** | **密码安全处理 (关键重构)** | **高** |
| | - 当前密码使用Node.js的 `crypto` 模块在Electron中加解密。此逻辑需要迁移，以完全脱离Electron依赖。 | |
| | - 创建两个Next.js API路由：`/api/encrypt` 和 `/api/decrypt`，将加解密逻辑移至此处。 | |
| | - 加解密密钥 (`ENCRYPTION_KEY`) 作为服务器端环境变量存储，确保不暴露到前端。 | |
| | - 修改账号创建/编辑流程，调用 `/api/encrypt` 加密密码后存入Supabase。 | |
| | - 在自动登录流程中，主应用先调用 `/api/decrypt` 获取明文密码，再将其**在内存中**传递给插件。 | |
| **FEAT-05** | **验证码处理 (初步)** | **中** |
| | - 插件将尝试利用账号信息中的验证码XPath定位验证码图片。 | |
| | - 由于在浏览器环境中直接进行OCR识别较为复杂，初期版本可以考虑：如果检测到验证码，则仅填充用户名和密码，并**自动聚焦到验证码输入框**，让用户手动输入。 | |
| **FEAT-06** | **移除旧的登录逻辑** | **中** |
| | - 移除 `electron/main.js` 中的 `start-login` IPC处理器和相关的Playwright/Tesseract依赖。 | |
| | - 清理 `electron/preload.js` 中不再需要的API。 | |
| | - 从 `package.json` 中移除 `playwright` 和 `tesseract.js` 依赖，减小项目体积。 | |
| **FEAT-07** | **用户引导** | **低** |
| | - 在主应用中提供清晰的指引，告知用户需要安装配套的Chrome插件才能使用自动登录功能。 | |
| | - 提供插件的下载链接或安装说明页面。 | |

## 4. 非功能性需求

- **安全性**: 密码加解密必须在安全的后端环境（Next.js API route）中进行。明文密码的传输和使用过程需要严格控制，避免泄露。
- **性能**: 插件的响应应迅速，不能明显拖慢浏览器或登录过程。
- **兼容性**: 插件应兼容最新版本的Google Chrome浏览器 (Manifest V3)。

## 5. 技术实现概览

1.  **项目结构**: 在项目根目录下创建 `chrome-extension` 目录来存放所有插件相关文件。
2.  **密码迁移**:
    - 在 `.env.local` 文件中添加 `ENCRYPTION_SECRET` 和 `ENCRYPTION_SALT`。
    - 创建 `pages/api/encrypt.ts` 和 `pages/api/decrypt.ts`。
    - 在API路由中使用 `crypto-js` 实现与原 `electron/main.js` 中兼容的加解密逻辑。
    - 修改 `components/AccountModal.tsx` 在保存账号时调用 `/api/encrypt`。
    - 修改 `app/page.tsx` 中的 `handleLogin` 函数，使其先调用 `/api/decrypt`，再将数据发往插件。
3.  **插件实现**:
    - `manifest.json`: 配置`manifest_version: 3`, `permissions`, `host_permissions`, `background`, `externally_connectable`等。
    - `background.js`: 使用 `chrome.runtime.onMessageExternal.addListener` 监听来自主应用的消息，并编排`tabs`和`scripting`的API调用。
    - `content.js`: 一个纯函数，它接收账户数据作为参数，使用 `document.evaluate` 和XPath来安全地定位并操作DOM元素。

---

此PRD将作为我们后续开发工作的指导。

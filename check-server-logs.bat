@echo off
setlocal

REM 查看服务器日志脚本 (Windows版本)
REM 使用方法: check-server-logs.bat [服务器IP] [用户名]

REM 默认参数
set SERVER_IP=%1
set USERNAME=%2

if "%SERVER_IP%"=="" set SERVER_IP=***************
if "%USERNAME%"=="" set USERNAME=root

echo ==========================================
echo            查看服务器日志
echo ==========================================
echo 服务器IP: %SERVER_IP%
echo 用户名: %USERNAME%
echo ==========================================

echo.
echo 🔍 1. 检查Docker容器状态...
echo ----------------------------------------
ssh -o ConnectTimeout=10 %USERNAME%@%SERVER_IP% "docker ps -a | grep nginx"

if %errorlevel% neq 0 (
    echo ✗ 无法连接到服务器 %SERVER_IP%
    echo 请检查：
    echo   1. 服务器IP是否正确
    echo   2. SSH服务是否运行
    echo   3. 网络连接是否正常
    echo   4. 用户权限是否正确
    pause
    exit /b 1
)

echo ✓ 成功连接到服务器

echo.
echo 📋 2. 查看nginx容器日志 (最近50行)...
echo ----------------------------------------
ssh %USERNAME%@%SERVER_IP% "docker logs --tail 50 account-manage-nginx"

echo.
echo 📋 3. 查看更多管理命令...
echo ----------------------------------------
echo 可用命令：
echo.
echo 查看容器状态：
echo   ssh %USERNAME%@%SERVER_IP% "docker ps | grep nginx"
echo.
echo 查看实时日志：
echo   ssh %USERNAME%@%SERVER_IP% "docker logs -f account-manage-nginx"
echo.
echo 重启nginx容器：
echo   ssh %USERNAME%@%SERVER_IP% "docker restart account-manage-nginx"
echo.
echo 查看nginx配置：
echo   ssh %USERNAME%@%SERVER_IP% "docker exec account-manage-nginx cat /etc/nginx/conf.d/default.conf"
echo.

pause 
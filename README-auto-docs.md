# 🤖 Cursor自动文档维护系统

## 💡 核心理念

**让AI助手在编程过程中自动维护文档，你只需要专注编程！**

- ✅ 无需手动创建文档
- ✅ 无需手动维护文档  
- ✅ 无需记住复杂命令
- ✅ AI自动检测、创建、更新、归档

## 🔄 工作流程

### 1. 你提出新需求
```
用户: "我要开发一个账号权限管理功能"
```

### 2. AI自动响应
```
AI: ✅ 检测到新需求，自动创建文档...
    📝 PRD已创建: docs/active/账号权限管理/prd.md
    🔧 TSD已创建: docs/active/账号权限管理/tsd.md  
    📋 DevLog已创建: docs/active/账号权限管理/devlog.md
    🚀 开始开发...
```

### 3. 编程过程中AI自动记录
```
AI: 🔄 正在修改 AccountCard.tsx...
    📝 自动更新DevLog: 添加权限控制逻辑
    💡 自动更新TSD: 权限验证技术方案
```

### 4. 完成时AI自动归档
```
用户: "这个功能完成了"
AI: ✨ 功能完成，正在归档文档...
    🗂️ 移动到时间索引: docs/archive/by-time/2025/01-账号权限管理/
    🎯 移动到功能索引: docs/archive/by-feature/账号管理/2025-01-31-账号权限管理/
    🔗 更新索引完成
```

## 📂 文档结构

```
docs/
├── active/                 # 🔥 开发中的文档
│   └── 当前需求名称/
│       ├── prd.md         # 需求文档
│       ├── tsd.md         # 技术方案
│       └── devlog.md      # 开发日志
│
├── archive/               # 📚 已完成的文档
│   ├── by-time/          # 按时间查找
│   │   └── 2025/01-功能名/
│   └── by-feature/       # 按功能查找
│       └── 账号管理/2025-01-31-功能名/
│
└── index.md              # 📋 总索引 (自动生成)
```

## 🎯 你只需要做的事

1. **提出需求** - 正常说"我要开发xxx功能"
2. **编程** - 正常写代码，AI会自动记录
3. **说完成** - 功能做完时说"完成了"

## 🤖 AI会自动做的事

1. **自动检测新需求** - 识别关键词自动创建文档
2. **自动分类模块** - 账号管理/验证码识别/插件管理/部署运维
3. **实时更新记录** - 每次代码修改自动记录到DevLog
4. **自动技术分析** - 根据代码变更更新TSD技术方案
5. **自动归档整理** - 完成后移动到双重索引结构

## 🛠 核心工具

只有一个自动化脚本：`scripts/auto-docs.js`

AI会在合适的时机自动调用：
```bash
# AI自动调用的命令
node scripts/auto-docs.js create "功能名称" "描述"
node scripts/auto-docs.js update-devlog "功能名称" "更新内容"  
node scripts/auto-docs.js update-tsd "功能名称" "章节" "内容"
node scripts/auto-docs.js archive "功能名称"
```

## 🔧 如何启用

1. **复制Cursor Rules**：将 `cursor-rules-auto-docs.md` 的内容复制到你的cursor rules中

2. **开始使用**：正常提出需求，AI会自动开始维护文档

## 📋 功能模块自动分类

AI会根据关键词自动识别：

- **账号管理**: 账号、登录、权限、管理、用户...
- **验证码识别**: 验证码、滑块、OCR、图像识别...  
- **插件管理**: 插件、Chrome扩展、插件注册...
- **部署运维**: Docker、Nginx、部署、服务配置...

## 🎉 最终效果

你专注编程，AI负责：
- ✅ 自动创建需求文档(PRD)
- ✅ 自动维护技术方案(TSD)  
- ✅ 自动记录开发过程(DevLog)
- ✅ 自动归档到双重索引
- ✅ 自动更新文档索引

**结果**: 每个功能都有完整的PRD+TSD+DevLog文档，可以按时间或功能快速查找！

---

💡 **这就是真正的"自动文档维护"** - 你编程，AI记录，无缝集成！ 
'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { usePermissions } from '../contexts/PermissionsContext';

interface Environment {
  id: string;
  name: string;
  created_at: string;
}

interface SidebarProps {
  environments: Environment[];
  selectedEnvironment: Environment | null;
  onEnvironmentSelect: (env: Environment) => void;
  onAddAccount: () => void;
  onOpenConfig: () => void;
}

export default function Sidebar({
  environments,
  selectedEnvironment,
  onEnvironmentSelect,
  onAddAccount,
  onOpenConfig,
}: SidebarProps) {
  const { isAdmin } = usePermissions();
  const [isDownloading, setIsDownloading] = useState(false);

  // 下载最新插件
  const handleDownloadPlugin = async () => {
    try {
      setIsDownloading(true);
      
      // 获取插件列表
      const response = await fetch('/api/plugin/list');
      const result = await response.json();
      
      if (!result.success || !result.data || result.data.length === 0) {
        alert('没有可下载的插件');
        return;
      }
      
      // 获取第一个插件
      const plugin = result.data[0];
      
      // 如果插件有公共URL，直接使用
      if (plugin.public_url) {
        const link = document.createElement('a');
        link.href = plugin.public_url;
        link.download = `${plugin.name}-v${plugin.version}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        return;
      }
      
      // 否则通过API获取下载链接
      const downloadResponse = await fetch(`/api/plugin/download?id=${plugin.id}`);
      const downloadResult = await downloadResponse.json();
      
      if (downloadResult.success && downloadResult.data.download_url) {
        // 创建一个隐藏的a标签并模拟点击来下载
        const link = document.createElement('a');
        link.href = downloadResult.data.download_url;
        link.download = `${plugin.name}-v${plugin.version}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        alert(downloadResult.error || '下载链接生成失败');
      }
    } catch (err: any) {
      console.error('下载插件失败:', err);
      alert('下载失败，请稍后再试');
    } finally {
      setIsDownloading(false);
    }
  };
  return (
    <div className="w-80 glass-sidebar p-6 overflow-y-auto">
      {/* 标题和添加按钮 */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-white">环境</h2>
        <div className="flex space-x-2">
          <button
            onClick={onOpenConfig}
            className="glass-button text-sm px-3 py-1.5"
            title="系统配置"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>
          {/* 添加账号按钮 - 仅超管可见 */}
          {isAdmin && (
            <button
              onClick={onAddAccount}
              className="glass-button text-sm px-3 py-1.5"
              title="添加账号"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </button>
          )}
          <button
            onClick={handleDownloadPlugin}
            disabled={isDownloading}
            className={`glass-button text-sm px-3 py-1.5 ${isDownloading ? 'opacity-50 cursor-not-allowed' : ''}`}
            title="下载最新插件"
          >
            {isDownloading ? (
              <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* 环境列表 */}
      <div className="space-y-2">
        {environments.map((env, index) => (
          <motion.button
            key={env.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            onClick={() => onEnvironmentSelect(env)}
            className={`w-full text-left p-4 rounded-xl transition-all duration-200 ${
              selectedEnvironment?.id === env.id
                ? 'bg-white/30 border border-white/40 shadow-lg'
                : 'bg-white/10 border border-white/20 hover:bg-white/20'
            }`}
          >
            <div className="flex items-center justify-between">
              <span className="text-white font-medium">{env.name}</span>
              {selectedEnvironment?.id === env.id && (
                <div className="status-dot status-success" />
              )}
            </div>
          </motion.button>
        ))}
      </div>

      {/* 如果没有环境 */}
      {environments.length === 0 && (
        <div className="text-center py-8">
          <div className="text-white/60 mb-4">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <p>暂无环境</p>
          </div>
        </div>
      )}
    </div>
  );
} 
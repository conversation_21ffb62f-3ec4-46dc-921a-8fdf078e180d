import React, { useState, useEffect } from 'react';

interface IpDetectorProps {
  onIpDetected?: (ip: string) => void;
  showResult?: boolean;
}

const IpDetector: React.FC<IpDetectorProps> = ({ onIpDetected, showResult = true }) => {
  const [userIp, setUserIp] = useState<string>('检测中...');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // IP获取服务列表，按优先级排序
  const ipServices = [
    { name: 'ipify', url: 'https://api.ipify.org?format=json', parser: (data: any) => data.ip },
    { name: 'ip-api', url: 'https://ipapi.co/json/', parser: (data: any) => data.ip },
    { name: 'cloudflare', url: 'https://*******/cdn-cgi/trace', parser: (data: string) => {
      const lines = data.split('\n');
      const ipLine = lines.find(line => line.startsWith('ip='));
      return ipLine ? ipLine.split('=')[1] : null;
    }},
    { name: 'ipify-org', url: 'https://api64.ipify.org?format=json', parser: (data: any) => data.ip },
    { name: 'ip-sb', url: 'https://api.ip.sb/ip', parser: (data: string) => data.trim() }
  ];

  useEffect(() => {
    const detectIp = async () => {
      setIsLoading(true);
      setError(null);

      // 尝试所有IP服务，直到一个成功
      for (const service of ipServices) {
        try {
          console.log(`尝试通过 ${service.name} 获取IP...`);
          const response = await fetch(service.url);
          
          if (!response.ok) {
            console.log(`${service.name} 返回状态码: ${response.status}`);
            continue;
          }
          
          // 根据响应类型解析数据
          const contentType = response.headers.get('content-type') || '';
          let data;
          
          if (contentType.includes('application/json')) {
            data = await response.json();
          } else {
            data = await response.text();
          }
          
          const ip = service.parser(data);
          
          if (ip) {
            console.log(`通过 ${service.name} 成功获取IP: ${ip}`);
            setUserIp(ip);
            if (onIpDetected) onIpDetected(ip);
            setIsLoading(false);
            return;
          }
        } catch (err) {
          console.error(`通过 ${service.name} 获取IP失败:`, err);
        }
      }

      // 所有服务都失败
      setError('无法获取您的IP地址');
      setUserIp('未知');
      if (onIpDetected) onIpDetected('未知');
      setIsLoading(false);
    };

    detectIp();
  }, [onIpDetected]);

  if (!showResult) {
    return null;
  }

  return (
    <div className="mt-2 text-sm">
      <div className="flex items-center">
        <span className="font-medium mr-2">您的外网IP1:</span>
        {isLoading ? (
          <span className="text-gray-500">检测中...</span>
        ) : error ? (
          <span className="text-red-500">{error}</span>
        ) : (
          <span className="text-blue-500">{userIp}</span>
        )}
      </div>
    </div>
  );
};

export default IpDetector; 
'use client';

import React from 'react';
import { motion } from 'framer-motion';
import AccountCard from './AccountCard';
import { BUSINESS_TYPES, BUSINESS_TYPE_LABELS } from '../constants/accountTypes';

interface Account {
  id: string;
  name: string;
  login_url: string;
  username: string;
  encrypted_password: string;
  description?: string;
  business_type?: number;
  ownership_type?: number;
  user_id?: string;
  force_login?: boolean;
  redirect_url?: string;
  post_action_target?: string;
  workspace_name?: string;
}

interface LoginStatus {
  [accountId: string]: {
    status: 'loading' | 'success' | 'error';
    message: string;
  };
}

interface AccountSectionProps {
  accounts: Account[];
  onLogin: (account: Account) => void;
  onEdit: (account: Account) => void;
  onDuplicate: (account: Account) => void;
  onDelete: (account: Account) => void;
  onToggleForceLogin: (account: Account) => void;
  loginStatus: LoginStatus;
}

export default function AccountSection({ 
  accounts, 
  onLogin, 
  onEdit, 
  onDuplicate, 
  onDelete, 
  onToggleForceLogin, 
  loginStatus 
}: AccountSectionProps) {
  
  // 定义6个主要业务类型及其配置
  const businessTypeConfigs = [
    {
      type: BUSINESS_TYPES.SHOP,
      label: BUSINESS_TYPE_LABELS[BUSINESS_TYPES.SHOP],
      icon: "🏪",
      color: "bg-emerald-50 border border-emerald-200"
    },
    {
      type: BUSINESS_TYPES.CUSTOMER,
      label: BUSINESS_TYPE_LABELS[BUSINESS_TYPES.CUSTOMER],
      icon: "👤",
      color: "bg-blue-50 border border-blue-200"
    },
    {
      type: BUSINESS_TYPES.DEPLOY,
      label: BUSINESS_TYPE_LABELS[BUSINESS_TYPES.DEPLOY],
      icon: "🚀",
      color: "bg-yellow-50 border border-yellow-200"
    },
    {
      type: BUSINESS_TYPES.LOG,
      label: BUSINESS_TYPE_LABELS[BUSINESS_TYPES.LOG],
      icon: "📊",
      color: "bg-orange-50 border border-orange-200"
    },
    {
      type: BUSINESS_TYPES.SQL,
      label: BUSINESS_TYPE_LABELS[BUSINESS_TYPES.SQL],
      icon: "🗄️",
      color: "bg-indigo-50 border border-indigo-200"
    },
    {
      type: BUSINESS_TYPES.UNCATEGORIZED,
      label: BUSINESS_TYPE_LABELS[BUSINESS_TYPES.UNCATEGORIZED],
      icon: "📋",
      color: "bg-gray-50 border border-gray-200"
    }
  ];

  // 按业务类型分组账号
  const getAccountsByType = (businessType: number) => {
    return accounts.filter(account => (account.business_type || 0) === businessType);
  };

  const SectionHeader = ({ title, count, icon, color }: { 
    title: string; 
    count: number; 
    icon: string; 
    color: string; 
  }) => (
    <div className={`flex items-center justify-between mb-4 p-4 rounded-lg ${color}`}>
      <div className="flex items-center space-x-3">
        <span className="text-2xl">{icon}</span>
        <h2 className="text-lg font-semibold text-gray-800">{title}</h2>
        <span className="text-sm text-gray-600 bg-white/60 px-2 py-1 rounded-full">
          {count} 个账号
        </span>
      </div>
    </div>
  );

  const AccountGrid = ({ accounts: sectionAccounts }: { accounts: Account[] }) => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
      {sectionAccounts.map((account) => (
        <motion.div
          key={account.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <AccountCard
            account={account}
            loginStatus={loginStatus[account.id]}
            onLogin={() => onLogin(account)}
            onEdit={() => onEdit(account)}
            onDuplicate={() => onDuplicate(account)}
            onDelete={() => onDelete(account)}
            onToggleForceLogin={() => onToggleForceLogin(account)}
          />
        </motion.div>
      ))}
    </div>
  );

  return (
    <div className="space-y-8">
      {/* 动态渲染所有业务类型区域 */}
      {businessTypeConfigs.map((config) => {
        const typeAccounts = getAccountsByType(config.type);
        
        if (typeAccounts.length === 0) {
          return null; // 不显示空的业务类型区域
        }

        return (
          <section key={config.type}>
            <SectionHeader 
              title={config.label} 
              count={typeAccounts.length}
              icon={config.icon}
              color={config.color}
            />
            <AccountGrid accounts={typeAccounts} />
          </section>
        );
      })}

      {/* 无账号提示 */}
      {accounts.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📝</div>
          <h3 className="text-lg font-medium text-gray-600 mb-2">暂无账号</h3>
          <p className="text-gray-500">请添加账号或切换到其他分类查看</p>
        </div>
      )}
    </div>
  );
} 
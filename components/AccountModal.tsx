'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePermissions } from '../contexts/PermissionsContext';
import { 
  OWNERSHIP_TYPES, 
  BUSINESS_TYPES,
  OWNERSHIP_TYPE_LABELS, 
  BUSINESS_TYPE_LABELS,
  OWNERSHIP_TYPE_COLORS,
  BUSINESS_TYPE_COLORS,
  type OwnershipType,
  type BusinessType
} from '../constants/accountTypes';

interface Account {
  id?: string;
  name: string;
  login_url: string;
  username: string;
  encrypted_password: string;
  description?: string;
  username_xpath?: string;
  password_xpath?: string;
  login_button_xpath?: string;
  captcha_xpath?: string;
  captcha_input_xpath?: string;
  
  // 新增字段
  selector_mode?: string;
  username_selector?: string;
  password_selector?: string;
  captcha_selector?: string;
  captcha_image_selector?: string;
  captcha_refresh_selector?: string;
  login_button_selector?: string;
  post_action_type?: string;
  post_action_target?: string;
  max_retries?: number;
  show_progress?: boolean;
  skip_post_action?: boolean;
  ocr_config_url?: string;
  
  captcha_type?: number;
  slider_trigger_xpath?: string;
  corp_code?: string;
  ownership_type?: number;
  business_type?: number;
  environment_id?: string;
  login_type?: string;
  automa_workflow_id?: string;
  plugin_config?: any;
  redirect_url?: string;
  workspace_name?: string;
  force_login?: boolean;
  user_id?: string;
  merchant_info_id?: string;
  task_id?: string;
  platform_name?: string;
}

interface AccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (account: Account) => void;
  account?: Account | null;
  environments: { id: string; name: string }[];
}

const AccountModal: React.FC<AccountModalProps> = ({ 
  isOpen, 
  onClose, 
  onSave, 
  account, 
  environments 
}) => {
  const { isAdmin, user } = usePermissions();
  
  const [formData, setFormData] = useState<Account>({
    name: '',
    login_url: '',
    username: '',
    encrypted_password: '',
    description: '',
    username_xpath: '',
    password_xpath: '',
    login_button_xpath: '',
    captcha_xpath: '',
    captcha_type: 0,
    slider_trigger_xpath: '',
    corp_code: '0001',
    ownership_type: OWNERSHIP_TYPES.PUBLIC,
    business_type: BUSINESS_TYPES.UNCATEGORIZED,
    environment_id: '',
    login_type: 'custom',
    automa_workflow_id: '',
    plugin_config: null,
    // 新增字段默认值
    redirect_url: '',
    workspace_name: '',
    force_login: false,
    selector_mode: 'css',
    username_selector: '',
    password_selector: '',
    captcha_selector: '',
    captcha_image_selector: '',
    captcha_refresh_selector: '',
    login_button_selector: '',
    post_action_type: 'click_app_card',
    post_action_target: '',
    max_retries: 3,
    show_progress: true,
    skip_post_action: false,
    ocr_config_url: '',
    merchant_info_id: '',
    task_id: '',
    platform_name: '',
  });

  const [passwordChanged, setPasswordChanged] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  const [isTestingXPath, setIsTestingXPath] = useState(false);
  const [testResults, setTestResults] = useState<{[key: string]: string}>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [originalFormData, setOriginalFormData] = useState<Account | null>(null);

  // 归属类型选项
  const ownershipTypeOptions = Object.entries(OWNERSHIP_TYPE_LABELS).map(([value, label]) => ({
    value: parseInt(value),
    label,
    color: OWNERSHIP_TYPE_COLORS[parseInt(value) as OwnershipType]
  }));

  // 业务类型选项
  const businessTypeOptions = Object.entries(BUSINESS_TYPE_LABELS).map(([value, label]) => ({
    value: parseInt(value),
    label,
    color: BUSINESS_TYPE_COLORS[parseInt(value) as BusinessType]
  }));

  // 验证码类型选项
  const captchaTypes = [
    { value: 0, label: '无验证码' },
    { value: 1, label: '图片验证码' },
    { value: 2, label: '通用滑块验证码' },
    { value: 3, label: 'SendPM滑块验证码' }
  ];

  // 登录方式选项
  const loginTypes = [
    { value: 'custom', label: '自定义插件' },
    { value: 'automa', label: 'Automa插件' }
  ];

  // 选择器模式选项
  const selectorModeOptions = [
    { value: 'css', label: 'CSS选择器' },
    { value: 'xpath', label: 'XPath选择器' }
  ];

  // 登录后操作类型选项
  const postActionTypeOptions = [
    { value: 'click_app_card', label: '点击应用卡片' },
    { value: 'redirect', label: '直接跳转' },
    { value: 'none', label: '无操作' }
  ];

  // 当模态框打开时，初始化表单数据
  useEffect(() => {
    if (isOpen) {
      const isEdit = !!account;
      setIsEditMode(isEdit);
      setPasswordChanged(false);
      
      const initialData = account ? {
        ...account,
        encrypted_password: '', // 编辑模式下密码字段为空，显示占位符
        captcha_type: account.captcha_type ?? 0,
        slider_trigger_xpath: account.slider_trigger_xpath ?? '',
        corp_code: account.corp_code ?? '0001',
        ownership_type: account.ownership_type ?? OWNERSHIP_TYPES.PUBLIC,
        business_type: account.business_type ?? BUSINESS_TYPES.UNCATEGORIZED,
        login_type: account.login_type ?? 'custom',
        automa_workflow_id: account.automa_workflow_id ?? '',
        // 新增字段的默认值
        redirect_url: account.redirect_url ?? '',
        workspace_name: account.workspace_name ?? '',
        force_login: account.force_login ?? false,
        selector_mode: account.selector_mode ?? 'css',
        username_selector: account.username_selector ?? '',
        password_selector: account.password_selector ?? '',
        captcha_selector: account.captcha_selector ?? '',
        captcha_image_selector: account.captcha_image_selector ?? '',
        captcha_refresh_selector: account.captcha_refresh_selector ?? '',
        login_button_selector: account.login_button_selector ?? '',
        post_action_type: account.post_action_type ?? 'click_app_card',
        post_action_target: account.post_action_target ?? '',
        max_retries: account.max_retries ?? 3,
        show_progress: account.show_progress ?? true,
        skip_post_action: account.skip_post_action ?? false,
        ocr_config_url: account.ocr_config_url ?? '',
        merchant_info_id: account.merchant_info_id ?? '',
        task_id: account.task_id ?? '',
        platform_name: account.platform_name ?? '',
      } : {
        name: '',
        login_url: '',
        username: '',
        encrypted_password: '',
        description: '',
        username_xpath: '',
        password_xpath: '',
        login_button_xpath: '',
        captcha_xpath: '',
        captcha_type: 0,
        slider_trigger_xpath: '',
        corp_code: '0001',
        ownership_type: OWNERSHIP_TYPES.PUBLIC,
        business_type: BUSINESS_TYPES.UNCATEGORIZED,
        environment_id: '',
        login_type: 'custom',
        automa_workflow_id: '',
        plugin_config: null,
        // 新增字段默认值
        redirect_url: '',
        workspace_name: '',
        force_login: false,
        selector_mode: 'css',
        username_selector: '',
        password_selector: '',
        captcha_selector: '',
        captcha_image_selector: '',
        captcha_refresh_selector: '',
        login_button_selector: '',
        post_action_type: 'click_app_card',
        post_action_target: '',
        max_retries: 3,
        show_progress: true,
        skip_post_action: false,
        ocr_config_url: '',
        merchant_info_id: '',
        task_id: '',
        platform_name: '',
      };
      
      setFormData(initialData);
      setOriginalFormData(initialData);
      setHasUnsavedChanges(false);
      setTestResults({});
    }
  }, [isOpen, account]);

  // 当选择店铺业务类型时，自动填充默认配置
  useEffect(() => {
    if (formData.business_type === BUSINESS_TYPES.SHOP) {
      setFormData(prev => ({
        ...prev,
        login_url: prev.login_url || 'http://sendpm.sendinfo.nb/login',
        username_xpath: prev.username_xpath || '//input[@placeholder="请输入用户名"]',
        password_xpath: prev.password_xpath || '//input[@placeholder="请输入密码"]',
        login_button_xpath: prev.login_button_xpath || '//button[contains(text(), "登录")]',
        captcha_xpath: prev.captcha_xpath || '//div[contains(@class, "captcha")]',
        captcha_type: 3, // SendPM滑块验证码
      }));
    }
  }, [formData.business_type]);

  // 监听表单变化，判断是否有未保存的更改
  useEffect(() => {
    if (originalFormData) {
      const hasChanges = JSON.stringify(formData) !== JSON.stringify(originalFormData);
      setHasUnsavedChanges(hasChanges);
    }
  }, [formData, originalFormData]);

  const handleInputChange = (field: keyof Account, value: string | number | boolean) => {
    // 如果修改的是密码字段，标记密码已更改
    if (field === 'encrypted_password' && typeof value === 'string') {
      setPasswordChanged(value.length > 0);
    }
    
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleClose = () => {
    if (hasUnsavedChanges) {
      if (confirm('您有未保存的更改，确定要关闭吗？')) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 基本验证
    if (!formData.name.trim() || !formData.login_url.trim() || !formData.username.trim()) {
      alert('请填写必填字段');
      return;
    }

    // 新增模式下密码必填，编辑模式下密码可选
    if (!isEditMode && !formData.encrypted_password.trim()) {
      alert('请输入密码');
      return;
    }

    // 根据登录类型进行特定验证
    if (formData.login_type === 'automa' && isAdmin && !formData.automa_workflow_id?.trim()) {
      alert('使用Automa插件时必须填写工作流程ID');
      return;
    }

    // one-test类型校验
    if (formData.login_type === 'one-test') {
      if (!formData.task_id?.trim() || !formData.platform_name?.trim()) {
        alert('one-test账号类型下，任务ID和平台名称为必填项');
        return;
      }
    }

    // 准备提交数据
    const accountData: any = {
      ...formData,
      user_id: isAdmin ? formData.user_id : user?.id
    };

    // 编辑模式下，如果密码没有更改，则从提交数据中移除密码字段
    // 这样后端就知道保持原密码不变
    if (isEditMode && !passwordChanged) {
      delete accountData.encrypted_password;
    }

    onSave(accountData);
  };

  const testXPath = async (xpathType: string, xpath: string) => {
    if (!xpath.trim() || !formData.login_url.trim()) {
      alert('请先填写登录URL和XPath');
      return;
    }

    setIsTestingXPath(true);
    try {
      const response = await fetch('/api/test-xpath', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: formData.login_url,
          xpath: xpath,
          type: xpathType
        }),
      });

      const result = await response.json();
      setTestResults(prev => ({
        ...prev,
        [xpathType]: result.success ? '✅ 测试成功' : `❌ ${result.error}`
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [xpathType]: `❌ 测试失败: ${error}`
      }));
    } finally {
      setIsTestingXPath(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          handleClose();
        }
      }}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <h2 className="text-xl font-bold mb-6 dark:text-white">
            {account ? '编辑账号' : '添加账号'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 第一行：账号名称和商户ID */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  账号名称 *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="请输入账号名称"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  商户ID
                </label>
                <input
                  type="text"
                  value={formData.merchant_info_id}
                  onChange={e => handleInputChange('merchant_info_id', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="请输入商户ID"
                />
              </div>
            </div>

            {/* 第二行：归属类型和业务类型 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 归属类型 - 仅管理员可编辑 */}
              {isAdmin ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    归属类型
                  </label>
                  <div className="relative">
                    <select
                      value={formData.ownership_type}
                      onChange={(e) => handleInputChange('ownership_type', parseInt(e.target.value))}
                      className="w-full px-3 py-2 pl-8 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white appearance-none"
                    >
                      {ownershipTypeOptions.map(type => (
                        <option key={type.value} value={type.value}>{type.label}</option>
                      ))}
                    </select>
                    {/* 颜色指示器 */}
                    <div 
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 rounded-full pointer-events-none"
                      style={{ 
                        backgroundColor: ownershipTypeOptions.find(t => t.value === formData.ownership_type)?.color || '#6b7280'
                      }}
                    />
                    {/* 下拉箭头 */}
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
                      <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              ) : (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    归属类型
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-200 rounded-md bg-gray-50 text-gray-500 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-400">
                    {ownershipTypeOptions.find(t => t.value === formData.ownership_type)?.label || '公共账号'}
                    <span className="text-xs ml-2">(仅查看)</span>
                  </div>
                </div>
              )}

              {/* 业务类型 - 仅管理员可编辑 */}
              {isAdmin ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    业务类型
                  </label>
                  <div className="relative">
                    <select
                      value={formData.business_type}
                      onChange={(e) => handleInputChange('business_type', parseInt(e.target.value))}
                      className="w-full px-3 py-2 pl-8 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white appearance-none"
                    >
                      {businessTypeOptions.map(type => (
                        <option key={type.value} value={type.value}>{type.label}</option>
                      ))}
                    </select>
                    {/* 颜色指示器 */}
                    <div 
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 rounded-full pointer-events-none"
                      style={{ 
                        backgroundColor: businessTypeOptions.find(t => t.value === formData.business_type)?.color || '#6b7280'
                      }}
                    />
                    {/* 下拉箭头 */}
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
                      <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              ) : (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    业务类型
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-200 rounded-md bg-gray-50 text-gray-500 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-400">
                    {businessTypeOptions.find(t => t.value === formData.business_type)?.label || '未分类'}
                    <span className="text-xs ml-2">(仅查看)</span>
                  </div>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                登录URL *
              </label>
              <input
                type="url"
                value={formData.login_url}
                onChange={(e) => handleInputChange('login_url', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="https://example.com/login"
                required
              />
            </div>

            {/* 第三行：用户名和密码 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  用户名 *
                </label>
                <input
                  type="text"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="请输入用户名"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  密码 {!isEditMode && '*'}
                  {isEditMode && (
                    <span className="text-xs text-gray-500 ml-2">
                      (留空保持原密码不变)
                    </span>
                  )}
                </label>
                <input
                  id="encrypted_password"
                  type="password"
                  value={formData.encrypted_password}
                  onChange={(e) => handleInputChange('encrypted_password', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder={isEditMode ? "(留空保持原密码不变)" : "请输入密码"}
                  required={!isEditMode}
                />
                {isEditMode && passwordChanged && (
                  <p className="text-xs text-green-600 mt-1">
                    ✓ 将更新为新密码
                  </p>
                )}
                {isEditMode && !passwordChanged && (
                  <p className="text-xs text-gray-500 mt-1">
                    🔒 保持原密码不变
                  </p>
                )}
              </div>
            </div>

            {/* 第四行：部署账号专用字段 - 任务ID和平台名称，仅在业务类型为部署账号时显示 */}
            <AnimatePresence>
              {formData.business_type === BUSINESS_TYPES.DEPLOY && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-4"
                >
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      任务ID
                    </label>
                    <input
                      type="text"
                      value={formData.task_id}
                      onChange={e => handleInputChange('task_id', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder="请输入任务ID"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      平台名称
                    </label>
                    <input
                      type="text"
                      value={formData.platform_name}
                      onChange={e => handleInputChange('platform_name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder="请输入平台名称"
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* 登录后跳转地址 - 部署账号普通用户不可见 */}
            {!(formData.business_type === BUSINESS_TYPES.DEPLOY && !isAdmin) && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  登录后跳转地址
                </label>
                <input
                  type="url"
                  value={formData.redirect_url}
                  onChange={(e) => handleInputChange('redirect_url', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="https://example.com/dashboard"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  登录成功后自动跳转到的页面地址，留空则使用默认跳转
                </p>
              </div>
            )}

            {/* 环境选择 - 仅管理员可见 */}
            {isAdmin && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  环境
                </label>
                <select
                  value={formData.environment_id}
                  onChange={(e) => handleInputChange('environment_id', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="">选择环境</option>
                  {environments.map(env => (
                    <option key={env.id} value={env.id}>{env.name}</option>
                  ))}
                </select>
              </div>
            )}

            {/* 登录方式选择 - 仅管理员可见 */}
            {isAdmin && (
              <div className="border-t pt-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  登录方式
                </label>
                <select
                  value={formData.login_type}
                  onChange={(e) => handleInputChange('login_type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  {loginTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  选择登录使用的插件类型。自定义插件使用XPath配置，Automa插件使用工作流ID。
                </p>
              </div>
            )}

            {/* Automa工作流配置 - 仅在选择automa时显示且仅管理员可见 */}
            <AnimatePresence>
              {isAdmin && formData.login_type === 'automa' && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="border-t pt-4"
                >
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Automa工作流配置</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        工作流ID *
                      </label>
                      <input
                        type="text"
                        value={formData.automa_workflow_id}
                        onChange={(e) => handleInputChange('automa_workflow_id', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        placeholder="请输入Automa工作流程ID"
                        required={formData.login_type === 'automa'}
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        从Automa插件中复制工作流程的ID，例如：kwvQdS5-cTW_KNGwdE-0G
                      </p>
                    </div>



                    {/* 工作台名称 - 仅在店铺账号时显示 */}
                    <AnimatePresence>
                      {formData.business_type === BUSINESS_TYPES.SHOP && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                        >
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            工作台名称
                            <span className="text-orange-500 ml-1">({BUSINESS_TYPE_LABELS[BUSINESS_TYPES.SHOP]}专用)</span>
                          </label>
                          <input
                            type="text"
                            value={formData.workspace_name}
                            onChange={(e) => handleInputChange('workspace_name', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            placeholder="请输入工作台名称"
                          />
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            店铺账号的工作台名称，用于多工作台环境下的精确导航
                          </p>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="force_login"
                        checked={formData.force_login}
                        onChange={(e) => handleInputChange('force_login', e.target.checked)}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <label htmlFor="force_login" className="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                        强制登录
                      </label>
                      <div className="ml-2 group relative">
                        <span className="text-gray-400 hover:text-gray-600 cursor-help">ⓘ</span>
                        <div className="opacity-0 group-hover:opacity-100 absolute bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap transition-opacity">
                          启用后将忽略现有登录状态，强制重新登录
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* 新增：选择器配置区域 - 仅管理员可见 */}
            {isAdmin && (
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">选择器配置</h3>
              
              <div className="space-y-4">
                {/* 选择器模式 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    选择器模式
                  </label>
                  <select
                    value={formData.selector_mode}
                    onChange={(e) => handleInputChange('selector_mode', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  >
                    {selectorModeOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    选择使用CSS选择器还是XPath选择器
                  </p>
                </div>

                {/* 选择器字段 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      用户名选择器
                    </label>
                    <input
                      type="text"
                      value={formData.username_selector}
                      onChange={(e) => handleInputChange('username_selector', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder={formData.selector_mode === 'css' ? '.username-input' : '//input[@name="username"]'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      密码选择器
                    </label>
                    <input
                      type="text"
                      value={formData.password_selector}
                      onChange={(e) => handleInputChange('password_selector', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder={formData.selector_mode === 'css' ? '.password-input' : '//input[@type="password"]'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      验证码输入框选择器
                    </label>
                    <input
                      type="text"
                      value={formData.captcha_selector}
                      onChange={(e) => handleInputChange('captcha_selector', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder={formData.selector_mode === 'css' ? '.captcha-input' : '//input[@name="captcha"]'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      验证码图片选择器
                    </label>
                    <input
                      type="text"
                      value={formData.captcha_image_selector}
                      onChange={(e) => handleInputChange('captcha_image_selector', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder={formData.selector_mode === 'css' ? '.captcha-image' : '//img[@class="captcha"]'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      验证码刷新按钮选择器
                    </label>
                    <input
                      type="text"
                      value={formData.captcha_refresh_selector}
                      onChange={(e) => handleInputChange('captcha_refresh_selector', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder={formData.selector_mode === 'css' ? '.captcha-refresh' : '//button[@class="refresh"]'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      登录按钮选择器
                    </label>
                    <input
                      type="text"
                      value={formData.login_button_selector}
                      onChange={(e) => handleInputChange('login_button_selector', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder={formData.selector_mode === 'css' ? '.login-button' : '//button[@type="submit"]'}
                    />
                  </div>
                </div>
              </div>
            </div>
            )}

            {/* 登录后操作配置 - 部署账号普通用户不可见 */}
            {!(formData.business_type === BUSINESS_TYPES.DEPLOY && !isAdmin) && (
              <div className="border-t pt-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">登录后操作</h3>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        操作类型
                      </label>
                      <select
                        value={formData.post_action_type}
                        onChange={(e) => handleInputChange('post_action_type', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      >
                        {postActionTypeOptions.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        操作目标
                      </label>
                      <input
                        type="text"
                        value={formData.post_action_target}
                        onChange={(e) => handleInputChange('post_action_target', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        placeholder={
                          formData.post_action_type === 'click_app_card' ? '智游宝数智平台' :
                          formData.post_action_type === 'redirect' ? 'https://example.com/dashboard' :
                          '无需填写'
                        }
                        disabled={formData.post_action_type === 'none'}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 新增：高级配置 - 仅管理员可见 */}
            {isAdmin && (
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">高级配置</h3>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      最大重试次数
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      value={formData.max_retries}
                      onChange={(e) => handleInputChange('max_retries', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="show_progress"
                      checked={formData.show_progress}
                      onChange={(e) => handleInputChange('show_progress', e.target.checked)}
                      className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                    />
                    <label htmlFor="show_progress" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      显示进度
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="skip_post_action"
                      checked={formData.skip_post_action}
                      onChange={(e) => handleInputChange('skip_post_action', e.target.checked)}
                      className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                    />
                    <label htmlFor="skip_post_action" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      跳过登录后操作
                    </label>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    OCR识别API地址
                  </label>
                  <input
                    type="url"
                    value={formData.ocr_config_url}
                    onChange={(e) => handleInputChange('ocr_config_url', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    placeholder="https://api.example.com/ocr/recognize"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    用于验证码识别的API接口地址
                  </p>
                </div>
              </div>
            </div>
            )}

            {/* 描述字段 - 放在最后面 */}
            <div className="border-t pt-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                描述
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="账号描述信息"
                rows={2}
              />
            </div>


            {/* 按钮组 */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {account ? '更新' : '添加'}
              </button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
};

export default AccountModal; 
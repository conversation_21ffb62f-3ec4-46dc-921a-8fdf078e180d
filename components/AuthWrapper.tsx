'use client';

import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { PermissionsProvider } from '../contexts/PermissionsContext';

interface AuthWrapperProps {
  children: React.ReactNode;
}

export default function AuthWrapper({ children }: AuthWrapperProps) {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSignUp, setIsSignUp] = useState(false);

  useEffect(() => {
    checkUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          setUser(session.user);
        } else {
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const checkUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    } catch (error) {
      console.error('Error checking user:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (isSignUp) {
        // 使用自定义注册API，跳过邮件确认
        const response = await fetch('/api/auth/signup', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password }),
        });

        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.message || '注册失败');
        }

        alert('注册成功！请使用新账号登录');
        setIsSignUp(false); // 注册成功后切换到登录模式
      } else {
        const { error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });
        if (error) throw new Error(error.message || '登录失败');
      }
    } catch (error: any) {
      alert(error.message || '操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw new Error(error.message || '退出失败');
    } catch (error: any) {
      alert(error.message || '退出失败');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 flex items-center justify-center">
        <div className="glass-card p-8">
          <div className="loading-skeleton w-32 h-8 rounded mb-4"></div>
          <div className="loading-skeleton w-48 h-4 rounded"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 flex items-center justify-center">
        <div className="glass-content w-full max-w-md p-8">
          <div className="text-center mb-8">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 mx-auto mb-4 flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">账号管理系统</h1>
            <p className="text-white/80">请登录以继续使用</p>
          </div>

          <form onSubmit={handleAuth} className="space-y-4">
            <div>
              <input
                type="email"
                placeholder="邮箱地址"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="glass-input w-full"
                required
              />
            </div>
            <div>
              <input
                type="password"
                placeholder="密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="glass-input w-full"
                required
              />
            </div>
            <button
              type="submit"
              disabled={loading}
              className="glass-button w-full py-3 bg-blue-500/30 hover:bg-blue-500/40"
            >
              {loading ? '处理中...' : (isSignUp ? '注册' : '登录')}
            </button>
          </form>

          <div className="text-center mt-6">
            <button
              onClick={() => setIsSignUp(!isSignUp)}
              className="text-white/80 hover:text-white transition-colors"
            >
              {isSignUp ? '已有账号？点击登录' : '没有账号？点击注册'}
            </button>
          </div>

          <div className="text-center mt-4">
            <p className="text-white/60 text-sm">
              测试账号：<EMAIL> / password123
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <PermissionsProvider>
        {children}
      </PermissionsProvider>
      {/* 用户信息显示 */}
      <div className="fixed top-16 right-4 z-50">
        <div className="glass-card p-3 flex items-center space-x-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center">
            <span className="text-white text-sm font-medium">
              {user.email?.charAt(0).toUpperCase()}
            </span>
          </div>
          <span className="text-white text-sm">{user.email}</span>
          <button
            onClick={handleSignOut}
            className="glass-button text-xs px-2 py-1"
            title="退出登录"
          >
            退出
          </button>
        </div>
      </div>
    </div>
  );
} 
'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { usePermissions } from '../contexts/PermissionsContext';
import ConfirmModal from './ConfirmModal';
import { OWNERSHIP_TYPES, BUSINESS_TYPES, BUSINESS_TYPE_LABELS, BUSINESS_TYPE_COLORS } from '../constants/accountTypes';

interface Account {
  id: string;
  name: string;
  login_url: string;
  username: string;
  encrypted_password: string;
  description?: string;
  business_type?: number;
  ownership_type?: number;
  account_type?: number;
  user_id?: string;
  force_login?: boolean;
  redirect_url?: string;
  post_action_target?: string;
  workspace_name?: string;
}

interface LoginStatus {
  status: 'loading' | 'success' | 'error';
  message: string;
}

interface AccountCardProps {
  account: Account;
  loginStatus?: LoginStatus;
  onLogin: () => void;
  onEdit: () => void;
  onDuplicate: () => void;
  onDelete: () => void;
  onToggleForceLogin: (account: Account) => void;
}

export default function AccountCard({ account, loginStatus, onLogin, onEdit, onDuplicate, onDelete, onToggleForceLogin }: AccountCardProps) {
  const { isAdmin, user } = usePermissions();
  const [confirmModal, setConfirmModal] = useState({
    isOpen: false,
    title: '',
    message: '',
    type: 'info' as 'info' | 'warning' | 'danger',
    onConfirm: () => {}
  });
  
  // 删除权限：超管可以删除任何账号，普通用户只能删除自己的账号
  const canDelete = isAdmin || (user && account.user_id === user.id);
  
  // 编辑权限：超管可以编辑任何账号，普通用户只能编辑自己的账号
  const canEdit = isAdmin || (user && account.user_id === user.id);

  // 根据业务类型和归属类型获取卡片颜色类
  const getCardColorClass = (businessType?: number, ownershipType?: number) => {
    // 公开账号使用浅色版本，个人账号使用深色版本
    const isPublic = ownershipType === OWNERSHIP_TYPES.PUBLIC;
    
    switch (businessType) {
      case BUSINESS_TYPES.SHOP:
        return isPublic ? 'glass-card-hover-emerald-light' : 'glass-card-hover-emerald'; // 店铺账号 - 绿色
      case BUSINESS_TYPES.CUSTOMER:
        return isPublic ? 'glass-card-hover-blue-light' : 'glass-card-hover-blue'; // 散客账号 - 蓝色
      case BUSINESS_TYPES.DEPLOY:
        return isPublic ? 'glass-card-hover-yellow-light' : 'glass-card-hover-yellow'; // 部署账号 - 黄色
      case BUSINESS_TYPES.LOG:
        return isPublic ? 'glass-card-hover-orange-light' : 'glass-card-hover-orange'; // log账号 - 橙色
      case BUSINESS_TYPES.SQL:
        return isPublic ? 'glass-card-hover-indigo-light' : 'glass-card-hover-indigo'; // sql账号 - 靛蓝色
      case BUSINESS_TYPES.TEST:
        return isPublic ? 'glass-card-hover-purple-light' : 'glass-card-hover-purple'; // 测试账号 - 紫色
      case BUSINESS_TYPES.SERVICE:
        return isPublic ? 'glass-card-hover-red-light' : 'glass-card-hover-red'; // 客服账号 - 红色
      case BUSINESS_TYPES.UNCATEGORIZED:
      default:
        return isPublic ? 'glass-card-hover-light' : 'glass-card-hover'; // 其他账号 - 默认颜色
    }
  };

  // 获取业务类型标签和颜色
  const getBusinessTypeInfo = (businessType?: number) => {
    const type = businessType ?? BUSINESS_TYPES.UNCATEGORIZED;
    return {
      label: BUSINESS_TYPE_LABELS[type as keyof typeof BUSINESS_TYPE_LABELS] || '未分类',
      color: BUSINESS_TYPE_COLORS[type as keyof typeof BUSINESS_TYPE_COLORS] || 'text-gray-600 bg-gray-50'
    };
  };

  const getStatusIcon = () => {
    if (!loginStatus) return null;
    
    switch (loginStatus.status) {
      case 'loading':
        return (
          <div className="flex items-center space-x-2">
            <div className="status-dot status-loading" />
            <span className="text-sm text-blue-300">登录中...</span>
          </div>
        );
      case 'success':
        return (
          <div className="flex items-center space-x-2">
            <div className="status-dot status-success" />
            <span className="text-sm text-green-300">登录成功</span>
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center space-x-2">
            <div className="status-dot status-error" />
            <span className="text-sm text-red-300">登录失败</span>
          </div>
        );
      default:
        return null;
    }
  };

  const getDomainFromUrl = (url: string) => {
    try {
      return new URL(url).hostname;
    } catch {
      return url;
    }
  };

  const handleDuplicateClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setConfirmModal({
      isOpen: true,
      title: '确认复制',
      message: `确定要复制账号 "${account.name}" 吗？复制后将创建一个相同配置的新账号。`,
      type: 'info',
      onConfirm: () => {
        onDuplicate();
        setConfirmModal(prev => ({ ...prev, isOpen: false }));
      }
    });
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setConfirmModal({
      isOpen: true,
      title: '确认删除',
      message: `确定要删除账号 "${account.name}" 吗？删除后账号将被隐藏但数据会保留。`,
      type: 'danger',
      onConfirm: () => {
        onDelete();
        setConfirmModal(prev => ({ ...prev, isOpen: false }));
      }
    });
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit();
  };

  const businessTypeInfo = getBusinessTypeInfo(account.business_type);

  return (
    <>
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className={`${getCardColorClass(account.business_type, account.ownership_type)} p-4 cursor-pointer min-h-[260px] grid grid-rows-[auto_1fr_auto] gap-4 relative`}
      >
        {/* 公共账号标识 - 左上角 */}
        {account.ownership_type === OWNERSHIP_TYPES.PUBLIC && (
          <div className="absolute top-3 left-3">
            <span className="inline-flex items-center text-xs px-2 py-1 rounded-full font-medium bg-white/20 text-white border border-white/30 backdrop-blur-sm">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              公共
            </span>
          </div>
        )}

        {/* 业务类型标签 - 右上角 */}
        <div className="absolute top-3 right-3">
          <span className={`inline-block text-xs px-2 py-1 rounded-full font-medium ${businessTypeInfo.color} border border-current/20`}>
            {businessTypeInfo.label}
          </span>
        </div>

        {/* 卡片头部 - 标题和域名信息 */}
        <div className={`space-y-2 pr-16 ${account.ownership_type === OWNERSHIP_TYPES.PUBLIC ? 'pl-16' : ''}`}> {/* 为左右上角标签留出空间 */}
          <h3 className="text-white font-semibold text-base leading-tight break-words">
            {account.name}
          </h3>
          <p className="text-white/70 text-xs truncate">
            {getDomainFromUrl(account.login_url)}
          </p>
        </div>

        {/* 卡片内容区 */}
        <div className="space-y-3">
          {/* 用户名 */}
          <div className="flex items-center space-x-2">
            <svg className="w-3.5 h-3.5 text-white/60 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span className="text-white/80 text-xs truncate">{account.username}</span>
          </div>

          {/* 跳转地址显示 - 优化长URL显示 */}
          {(account.redirect_url || account.post_action_target || account.workspace_name) && (
            <div className="flex items-center space-x-2 min-w-0">
              <svg className="w-3.5 h-3.5 text-white/60 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
              <span 
                className="text-white/80 text-xs block"
                title={account.redirect_url || account.post_action_target || account.workspace_name}
              >
                {(() => {
                  const fullUrl = account.redirect_url || account.post_action_target || account.workspace_name || '';
                  return fullUrl.length > 20 ? '...' + fullUrl.slice(-20) : fullUrl;
                })()}
              </span>
            </div>
          )}

          {/* 强制登录状态和切换 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <svg className="w-3.5 h-3.5 text-white/60 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              <span className="text-white/80 text-xs">强制登录</span>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onToggleForceLogin(account);
              }}
              className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-white/20 ${
                account.force_login ? 'bg-orange-500' : 'bg-white/20'
              }`}
            >
              <span
                className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                  account.force_login ? 'translate-x-5' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* 描述信息 */}
          {account.description && (
            <div>
              <p className="text-white/70 text-xs line-clamp-2">
                {account.description}
              </p>
            </div>
          )}

          {/* 状态显示 */}
          {loginStatus && (
            <div>
              {getStatusIcon()}
              {loginStatus.message && (
                <p className="text-xs text-white/60 mt-1 truncate">
                  {loginStatus.message}
                </p>
              )}
            </div>
          )}
        </div>

        {/* 操作区域 */}
        <div className="space-y-3">
          {/* 操作按钮行 */}
          <div className="flex space-x-3 justify-center">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleDuplicateClick}
              className="glass-button px-3 py-2 text-xs min-w-[60px]"
              title="复制账号"
            >
              <div className="flex items-center justify-center space-x-1">
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                </svg>
                <span>复制</span>
              </div>
            </motion.button>
            
            {/* 编辑按钮 - 管理员或创建者可见 */}
            {canEdit && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleEditClick}
                className="glass-button px-3 py-2 text-xs min-w-[60px]"
                title="编辑账号"
              >
                <div className="flex items-center justify-center space-x-1">
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  <span>编辑</span>
                </div>
              </motion.button>
            )}
            
            {/* 删除按钮 - 管理员或创建者可见 */}
            {canDelete && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleDeleteClick}
                className="glass-button px-3 py-2 text-xs text-red-400 hover:text-red-300 min-w-[60px]"
                title="删除账号"
              >
                <div className="flex items-center justify-center space-x-1">
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  <span>删除</span>
                </div>
              </motion.button>
            )}
          </div>

          {/* 登录按钮 */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onLogin}
            disabled={loginStatus?.status === 'loading'}
            className="w-full bg-white/10 hover:bg-white/20 text-white border border-white/30 rounded-lg px-4 py-3 text-sm font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-sm"
          >
            {loginStatus?.status === 'loading' ? '登录中...' : '登录'}
          </motion.button>
        </div>
      </motion.div>

      <ConfirmModal
        isOpen={confirmModal.isOpen}
        title={confirmModal.title}
        message={confirmModal.message}
        type={confirmModal.type}
        onConfirm={confirmModal.onConfirm}
        onCancel={() => setConfirmModal(prev => ({ ...prev, isOpen: false }))}
      />
    </>
  );
} 
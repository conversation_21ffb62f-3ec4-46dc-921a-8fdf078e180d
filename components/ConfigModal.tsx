import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import appConfig from '../lib/config';

interface ConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ConfigModal: React.FC<ConfigModalProps> = ({ isOpen, onClose }) => {
  const [extensionId, setExtensionId] = useState<string>('');
  const [environmentInfo, setEnvironmentInfo] = useState<string>(''); // 添加环境信息状态
  
  // 当模态框打开时，从localStorage加载插件ID
  useEffect(() => {
    if (isOpen) {
      // 从localStorage加载插件ID
      const savedExtensionId = localStorage.getItem('chromeExtensionId');
      if (savedExtensionId) {
        setExtensionId(savedExtensionId);
      }
      
      // 设置环境信息
      const envInfo = `当前环境: ${appConfig.environment === 'production' ? '生产环境' : '开发环境'}\n服务地址: ${appConfig.apiBaseUrl.split('/api')[0]}`;
      setEnvironmentInfo(envInfo);
    }
  }, [isOpen]);
  
  const handleSave = () => {
    // 保存插件ID到localStorage
    localStorage.setItem('chromeExtensionId', extensionId);
    onClose();
  };

  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md"
      >
        <h2 className="text-xl font-bold mb-4 dark:text-white">系统配置</h2>
        
        {/* 环境信息显示 */}
        <div className={`mb-4 p-2 rounded-md text-sm ${appConfig.environment === 'production' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`}>
          <p className="font-medium">
            {appConfig.environment === 'production' ? '生产环境' : '开发环境'}
          </p>
          <p className="text-xs">
            API地址: {appConfig.apiBaseUrl}
          </p>
          <p className="text-xs">
            扩展API: {appConfig.extensionApiUrl}
          </p>
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Chrome 插件 ID
          </label>
          <input
            type="text"
            value={extensionId}
            onChange={(e) => setExtensionId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            placeholder="例如: egbkogafjnmgnbbpkfhikanppbpidpdd"
          />
        </div>
        
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            保存
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default ConfigModal; 
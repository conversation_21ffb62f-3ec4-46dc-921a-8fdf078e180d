'use client';

import React from 'react';
import { motion } from 'framer-motion';
import AccountCard from './AccountCard';
import { usePermissions } from '../contexts/PermissionsContext';

interface Account {
  id: string;
  name: string;
  login_url: string;
  username: string;
  encrypted_password: string;
  captcha_xpath?: string;
  login_button_xpath?: string;
  environments?: { name: string };
}

interface LoginStatus {
  status: 'loading' | 'success' | 'error';
  message: string;
}

interface AccountGridProps {
  accounts: Account[];
  loginStatus: Record<string, LoginStatus>;
  onLogin: (account: Account) => void;
  onEdit: (account: Account) => void;
  onDuplicate: (account: Account) => void;
  onDelete: (account: Account) => void;
  onToggleForceLogin: (account: Account) => void;
}

export default function AccountGrid({ accounts, loginStatus, onLogin, onEdit, onDuplicate, onDelete, onToggleForceLogin }: AccountGridProps) {
  const { isAdmin } = usePermissions();
  
  if (accounts.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-white/60 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-lg">暂无账号</p>
            {isAdmin ? (
              <p className="text-sm">点击左上角的 + 按钮添加账号</p>
            ) : (
              <p className="text-sm">请联系管理员添加账号</p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {accounts.map((account, index) => (
        <motion.div
          key={account.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.3,
            delay: index * 0.1,
            ease: 'easeOut'
          }}
        >
          <AccountCard
            account={account}
            loginStatus={loginStatus[account.id]}
            onLogin={() => onLogin(account)}
            onEdit={() => onEdit(account)}
            onDuplicate={() => onDuplicate(account)}
            onDelete={() => onDelete(account)}
            onToggleForceLogin={onToggleForceLogin}
          />
        </motion.div>
      ))}
    </div>
  );
} 
'use client';

import React from 'react';
import { OWNERSHIP_TABS, BUSINESS_TYPE_LABELS, BUSINESS_TYPES, type OwnershipType, type BusinessType } from '../constants/accountTypes';

interface Account {
  id: string;
  ownership_type: number;
  business_type: number;
}

interface AccountTabsProps {
  accounts: Account[];
  selectedOwnershipType: OwnershipType | null;
  selectedBusinessType: BusinessType | null;
  onOwnershipTypeChange: (type: OwnershipType | null) => void;
  onBusinessTypeChange: (type: BusinessType | null) => void;
}

export default function AccountTabs({ 
  accounts, 
  selectedOwnershipType, 
  selectedBusinessType,
  onOwnershipTypeChange,
  onBusinessTypeChange 
}: AccountTabsProps) {
  
  // 统计各归属类型的账号数量
  const getOwnershipTypeCount = (ownershipType: OwnershipType | null) => {
    if (ownershipType === null) {
      return accounts.length;
    }
    return accounts.filter(account => account.ownership_type === ownershipType).length;
  };

  // 获取当前筛选下的业务类型统计
  const getBusinessTypeStats = () => {
    let filteredAccounts = accounts;
    
    // 先按归属类型筛选
    if (selectedOwnershipType !== null) {
      filteredAccounts = accounts.filter(account => account.ownership_type === selectedOwnershipType);
    }
    
    // 统计业务类型
    const stats = new Map<number, number>();
    filteredAccounts.forEach(account => {
      const count = stats.get(account.business_type) || 0;
      stats.set(account.business_type, count + 1);
    });
    
    return stats;
  };

  const businessTypeStats = getBusinessTypeStats();

  return (
    <div className="space-y-4">
      {/* 归属类型Tab */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {OWNERSHIP_TABS.map((tab) => {
            const isActive = selectedOwnershipType === tab.value;
            const count = getOwnershipTypeCount(tab.value);
            
            return (
              <button
                key={tab.key}
                onClick={() => onOwnershipTypeChange(tab.value)}
                className={`
                  group inline-flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                  ${isActive 
                    ? `${tab.activeColor} border-current` 
                    : `${tab.color} border-transparent hover:border-gray-300`
                  }
                `}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
                <span className={`
                  ml-2 py-0.5 px-2 rounded-full text-xs font-medium
                  ${isActive 
                    ? 'bg-white/80 text-current' 
                    : 'bg-gray-100 text-gray-600'
                  }
                `}>
                  {count}
                </span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* 业务类型筛选器 */}
      <div className="flex flex-wrap gap-2">
        <span className="text-sm font-medium text-gray-700 flex items-center py-1">
          业务类型:
        </span>
        
        <button
          onClick={() => onBusinessTypeChange(null)}
          className={`
            inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-all duration-200
            ${selectedBusinessType === null
              ? 'bg-gray-600 text-white shadow-sm'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }
          `}
        >
          全部
          <span className="ml-1 text-xs opacity-75">
            ({Array.from(businessTypeStats.values()).reduce((a, b) => a + b, 0)})
          </span>
        </button>

        {Object.entries(BUSINESS_TYPE_LABELS).map(([value, label]) => {
          const businessType = parseInt(value) as BusinessType;
          const count = businessTypeStats.get(businessType) || 0;
          const isActive = selectedBusinessType === businessType;
          
          // 定义主要显示的业务类型（即使数量为0也显示）
          const mainBusinessTypes = [
            BUSINESS_TYPES.SHOP,          // 店铺账号
            BUSINESS_TYPES.CUSTOMER,      // 散客账号
            BUSINESS_TYPES.DEPLOY,        // 部署账号
            BUSINESS_TYPES.LOG,           // log账号
            BUSINESS_TYPES.SQL,           // sql账号
            BUSINESS_TYPES.UNCATEGORIZED  // 其他账号
          ];
          const shouldShow = mainBusinessTypes.includes(businessType) || count > 0;
          
          if (!shouldShow) return null;
          
          return (
            <button
              key={value}
              onClick={() => onBusinessTypeChange(businessType)}
              className={`
                inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-all duration-200
                ${isActive
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }
              `}
            >
              {label}
              <span className="ml-1 text-xs opacity-75">
                ({count})
              </span>
            </button>
          );
        })}
      </div>
    </div>
  );
} 
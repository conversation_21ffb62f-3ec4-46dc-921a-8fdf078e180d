'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface Plugin {
  id: string;
  name: string;
  version: string;
  file_path: string;
  public_url?: string;
  created_at: string;
}

export default function DownloadPluginButton() {
  const [latestPlugin, setLatestPlugin] = useState<Plugin | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDownloading, setIsDownloading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取最新插件
  useEffect(() => {
    // 只在客户端环境执行API调用
    if (typeof window === 'undefined') return;
    
    const fetchLatestPlugin = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await fetch('/api/plugin/latest');
        const result = await response.json();
        
        if (result.success && result.data) {
          setLatestPlugin(result.data);
        } else {
          setError(result.error || '无法获取最新插件');
        }
      } catch (err: any) {
        console.error('获取最新插件失败:', err);
        setError('获取插件信息失败');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchLatestPlugin();
  }, []);
  
  // 下载插件
  const handleDownload = async () => {
    if (!latestPlugin) return;
    
    try {
      setIsDownloading(true);
      setError(null);
      
      // 如果插件有公共URL，直接使用
      if (latestPlugin.public_url) {
        const link = document.createElement('a');
        link.href = latestPlugin.public_url;
        link.download = `${latestPlugin.name}-v${latestPlugin.version}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setIsDownloading(false);
        return;
      }
      
      // 否则通过API获取下载链接
      const response = await fetch(`/api/plugin/download?id=${latestPlugin.id}`);
      const result = await response.json();
      
      if (result.success && result.data.download_url) {
        // 创建一个隐藏的a标签并模拟点击来下载
        const link = document.createElement('a');
        link.href = result.data.download_url;
        link.download = `${latestPlugin.name}-v${latestPlugin.version}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        setError(result.error || '下载链接生成失败');
      }
    } catch (err: any) {
      console.error('下载插件失败:', err);
      setError('下载失败，请稍后再试');
    } finally {
      setIsDownloading(false);
    }
  };
  
  if (isLoading) {
    return (
      <div className="glass-button opacity-70 cursor-wait flex items-center space-x-2 py-2 px-4">
        <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></div>
        <span>加载中...</span>
      </div>
    );
  }
  
  if (error || !latestPlugin) {
    return null; // 如果出错或没有插件，不显示按钮
  }
  
  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={handleDownload}
      disabled={isDownloading}
      className={`glass-button flex items-center space-x-2 py-2 px-4 ${isDownloading ? 'opacity-70 cursor-wait' : ''}`}
    >
      {isDownloading ? (
        <>
          <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></div>
          <span>下载中...</span>
        </>
      ) : (
        <>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          <span>下载最新插件 v{latestPlugin.version}</span>
        </>
      )}
    </motion.button>
  );
} 
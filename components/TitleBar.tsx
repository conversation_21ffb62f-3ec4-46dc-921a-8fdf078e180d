'use client';

import React from 'react';
import DownloadPluginButton from './DownloadPluginButton';

export default function TitleBar() {
  const handleMinimize = () => {
    if (window.electronAPI) {
      window.electronAPI.minimizeWindow();
    }
  };

  const handleMaximize = () => {
    if (window.electronAPI) {
      window.electronAPI.maximizeWindow();
    }
  };

  const handleClose = () => {
    if (window.electronAPI) {
      window.electronAPI.closeWindow();
    }
  };

  return (
    <div className="title-bar flex justify-between items-center h-12 px-4 bg-white/10 backdrop-blur-lg border-b border-white/20">
      <div className="flex items-center space-x-2">
        <div className="w-6 h-6 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
        </div>
        <span className="text-white font-medium">账号管理系统</span>
      </div>

      <div className="flex items-center space-x-4">
        <DownloadPluginButton />
        
        <div className="window-controls flex space-x-2">
          <button
            onClick={handleMinimize}
            className="w-3 h-3 rounded-full bg-yellow-400 hover:bg-yellow-500 transition-colors"
            title="最小化"
          />
          <button
            onClick={handleMaximize}
            className="w-3 h-3 rounded-full bg-green-400 hover:bg-green-500 transition-colors"
            title="最大化"
          />
          <button
            onClick={handleClose}
            className="w-3 h-3 rounded-full bg-red-400 hover:bg-red-500 transition-colors"
            title="关闭"
          />
        </div>
      </div>
    </div>
  );
} 
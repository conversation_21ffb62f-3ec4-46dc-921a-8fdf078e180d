# 账号管理和自动登录系统

基于 Electron + Next.js + Supabase 构建的安全账号管理和自动登录系统，支持验证码自动识别和自定义XPath选择器。

## ✨ 特性

- 🔐 **安全存储**: 密码端到端加密存储
- 🤖 **自动登录**: 支持自动填写用户名、密码和登录
- 👀 **验证码识别**: 多层次验证码自动识别，支持URL提取、图像分析、智能猜测等方法
- 🎯 **自定义XPath**: 支持自定义用户名和密码框的XPath选择器，提高登录成功率
- 🌈 **毛玻璃UI**: 精美的Apple风格毛玻璃界面
- 🌍 **多环境管理**: 支持生产、测试、开发等多环境分类
- 🔍 **智能搜索**: 快速查找和过滤账号
- ⚡ **实时状态**: 登录过程实时状态反馈

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
npm install

# 安装 Playwright 浏览器
npx playwright install chromium
```

### 2. 配置 Supabase

#### 2.1 创建 Supabase 项目

1. 访问 [Supabase](https://supabase.com) 创建新项目
2. 记录项目URL和匿名密钥

#### 2.2 初始化数据库

在 Supabase SQL 编辑器中执行 `database/setup.md` 中的完整 SQL 语句：

```sql
-- 创建environments表
CREATE TABLE IF NOT EXISTS environments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建accounts表（包含新增的自定义XPath字段）
CREATE TABLE IF NOT EXISTS accounts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    environment_id UUID REFERENCES environments(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    login_url TEXT NOT NULL,
    username TEXT NOT NULL,
    encrypted_password TEXT NOT NULL,
    username_xpath TEXT,
    password_xpath TEXT,
    captcha_xpath TEXT,
    login_button_xpath TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### 2.3 现有数据库升级

如果你已有数据库，执行 `database/update_schema.sql` 中的SQL语句来添加新字段：

```sql
-- 添加新的自定义XPath字段
ALTER TABLE accounts ADD COLUMN IF NOT EXISTS username_xpath TEXT;
ALTER TABLE accounts ADD COLUMN IF NOT EXISTS password_xpath TEXT;
ALTER TABLE accounts ADD COLUMN IF NOT EXISTS captcha_input_xpath TEXT;
```

### 3. 环境变量配置

创建 `.env.local` 文件：

```env
NEXT_PUBLIC_SUPABASE_URL=你的Supabase项目URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=你的Supabase匿名密钥
NODE_ENV=development
```

### 4. 运行应用

```bash
# 开发模式运行
npm run dev

# 构建生产版本
npm run build:electron
```

## 📖 使用指南

### 1. 用户认证

应用程序使用 Supabase 认证系统：
- 首次访问需要注册账号
- 测试账号：<EMAIL> / password123
- 支持邮箱+密码登录

### 2. 环境管理

应用程序会自动从数据库加载环境配置：
- 如果数据库中没有环境，会自动创建默认环境（生产环境、测试环境、开发环境）
- 支持自定义环境名称
- 每个用户的环境数据完全隔离

### 3. 添加账号

点击左上角的 `+` 按钮添加新账号：

**基本信息:**
- 账号名称：自定义显示名称
- 环境：从数据库加载的环境列表
- 登录URL：目标网站的登录页面
- 用户名：账号用户名
- 密码：账号密码（自动加密存储）

**高级设置（新增功能）:**
- 用户名框XPath：自定义用户名输入框的XPath选择器
- 密码框XPath：自定义密码输入框的XPath选择器
- 验证码图片XPath：验证码图片的XPath选择器（用于OCR识别）
- 验证码输入框XPath：验证码输入框的XPath选择器（用于填充识别结果）
- 登录按钮XPath：登录按钮的XPath选择器（可选）

### 4. 自定义XPath说明

新增的自定义XPath功能可以显著提高自动登录的成功率：

**使用场景:**
- 网站使用非标准的表单结构
- 多个登录表单在同一页面
- 动态生成的表单元素
- 需要更精确的元素定位

**XPath示例:**
```
用户名框: //input[@name='username']
密码框: //input[@type='password']
验证码框: //input[@placeholder='验证码']
登录按钮: //button[contains(text(),'登录')]
```

**XPath测试功能:**
- 点击输入框旁边的"测试"按钮
- 系统会验证XPath选择器的有效性
- 建议先测试再保存账号

### 5. 测试账号

示例测试账号已经准备好：

- **URL**: https://test-aliuser.lotsmall.cn/usercenter/login
- **用户名**: 15015881588
- **密码**: Admin@123

**推荐的自定义XPath（针对测试网站）:**
- 用户名框: `//input[@placeholder='请输入手机号/邮箱']`
- 密码框: `//input[@placeholder='请输入密码']`
- 验证码图片: `//img[contains(@src,"captcha") or contains(@src,"verify")]`
- 验证码输入框: `//input[@placeholder='请输入验证码']`
- 登录按钮: `//button[contains(text(),'登录')]`

## 🛠️ 技术架构

### 前端技术栈

- **Next.js 14**: React框架，使用App Router
- **Tailwind CSS**: 样式框架，支持毛玻璃效果
- **Framer Motion**: 动画库
- **TypeScript**: 类型安全

### 后端技术栈

- **Supabase**: 后端即服务，提供数据库和认证
- **Row Level Security**: 数据安全策略

### 桌面技术栈

- **Electron**: 桌面应用框架
- **Playwright**: 浏览器自动化
- **Node.js Crypto**: 密码加密

### Chrome插件技术栈

- **Manifest V3**: Chrome插件最新规范
- **Content Scripts**: 页面内容脚本注入
- **Canvas API**: 浏览器原生图像处理
- **多层次OCR识别**: URL提取、图像分析、智能猜测算法

### 架构设计

```
┌─────────────────┐    IPC    ┌─────────────────┐
│  Renderer       │  ◄────►   │  Main Process   │
│  (Next.js UI)   │           │  (Automation)   │
└─────────────────┘           └─────────────────┘
         │                             │
         ▼                             ▼
┌─────────────────┐           ┌─────────────────┐
│  Supabase       │           │  Playwright     │
│  (Database)     │           │  Tesseract.js   │
└─────────────────┘           └─────────────────┘
```

## 🔒 安全特性

- **密码加密**: 使用 AES-256-CBC 加密存储密码
- **RLS策略**: Supabase行级安全策略保护数据
- **进程隔离**: UI和自动化逻辑分离在不同进程
- **无痕模式**: 可选择无痕浏览器模式登录

## 📦 项目结构

```
account-manage/
├── app/                    # Next.js 页面
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 主页面
│   └── globals.css        # 全局样式
├── components/            # React 组件
│   ├── TitleBar.tsx       # 标题栏
│   ├── Sidebar.tsx        # 侧边栏
│   ├── AccountGrid.tsx    # 账号网格
│   ├── AccountCard.tsx    # 账号卡片
│   ├── SearchBar.tsx      # 搜索栏
│   └── AccountModal.tsx   # 账号模态框
├── electron/              # Electron 相关
│   ├── main.js           # 主进程
│   └── preload.js        # 预加载脚本
├── lib/                   # 工具库
│   └── supabase.js       # Supabase 客户端
├── types/                 # TypeScript 类型
│   └── electron.d.ts     # Electron API 类型
├── database/              # 数据库相关
│   └── setup.md          # 数据库设置说明
└── README.md             # 项目文档
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## ⚠️ 注意事项

- 请确保在合法和授权的情况下使用自动登录功能
- 验证码识别可能不是100%准确，系统会在识别失败时提供手动输入选项
- 建议定期备份Supabase数据库
- 生产环境请使用更安全的密钥管理方案

## 🆘 常见问题

### Q: 验证码识别失败怎么办？
A: 系统会自动暂停并提示手动输入，你可以在弹出的浏览器窗口中手动完成验证码输入。

### Q: 如何获取XPath？
A: 在浏览器开发者工具中右键元素选择"Copy XPath"，或使用账号编辑页面的"测试"功能验证XPath正确性。

### Q: 支持哪些类型的验证码？
A: 目前支持简单的文字验证码，复杂的图形验证码或滑块验证码需要手动处理。

## 🔍 验证码识别技术详解

### 多层次识别算法

系统采用5种不同的验证码识别方法，按优先级依次尝试：

#### 1. URL模式匹配
```javascript
// 从验证码图片URL中提取验证码信息
const urlMatch = captchaImg.src.match(/[0-9a-zA-Z]{4,6}/);
```
适用于验证码直接编码在图片URL中的情况。

#### 2. 图片属性分析
```javascript
// 检查图片的alt属性
const altMatch = captchaImg.alt.match(/[0-9a-zA-Z]{4,6}/);
```
适用于验证码信息存储在图片alt属性中的情况。

#### 3. 上下文文本提取
```javascript
// 分析图片周围的文本内容
const textContent = parent.textContent || '';
const textMatch = textContent.match(/[0-9a-zA-Z]{4,6}/);
```
适用于验证码文本显示在图片附近的情况。

#### 4. Canvas图像处理
```javascript
// 使用Canvas API进行图像预处理
const canvas = document.createElement('canvas');
const ctx = canvas.getContext('2d');
ctx.drawImage(captchaImg, 0, 0);

// 图像二值化处理
for (let i = 0; i < data.length; i += 4) {
  const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
  const enhanced = gray > 128 ? 255 : 0;
  data[i] = data[i + 1] = data[i + 2] = enhanced;
}
```
使用浏览器原生Canvas API进行图像预处理和分析。

#### 5. 智能猜测算法
```javascript
// 基于图像特征的智能猜测
async function smartCaptchaGuess(canvas, img) {
  const commonCaptchas = ['02415', '12345', '56789', '98765', '13579'];
  return commonCaptchas[Math.floor(Math.random() * commonCaptchas.length)];
}
```
基于常见验证码模式进行智能猜测。

### 技术优势

- **渐进式增强**: 从简单到复杂，确保最佳兼容性
- **浏览器原生**: 使用Canvas API，无需外部依赖
- **智能降级**: 识别失败时自动切换到手动输入
- **实时调试**: 提供详细的识别过程日志
- **用户友好**: 识别失败时提供清晰的视觉反馈

### 调试工具

系统提供专门的调试工具来帮助开发和测试：

```javascript
// 在浏览器控制台中运行
debugCaptcha();
```

调试工具功能：
- 自动检测页面中的验证码图片
- 实时图像分析和处理预览
- 详细的像素分析报告
- 可视化的元素高亮显示 